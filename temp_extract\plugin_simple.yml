name: UltimateDungeons
main: com.ultimatedungeons.UltimateDungeonsSimple
version: 1.0.0
api-version: "1.21"
authors: ["UltimateDungeons Team"]
description: |
  The ultimate dungeon plugin combining the best features from ApexDungeons and MythicDungeons.
  Features procedural generation, advanced GUI system, comprehensive trigger/function framework,
  party management, and extensive plugin integrations.

softdepend:
  - MythicMobs
  - PlaceholderAPI
  - Vault
  - Citizens
  - WorldEdit
  - ModelEngine
  - Heroes
  - BetonQuest
  - Parties
  - Multiverse-Inventories

commands:
  ultimatedungeons:
    description: Main command for Ultimate Dungeons
    usage: /ultimatedungeons [help|admin|create|join|leave|party|rewards|tools]
    aliases: [ud, dungeons, dungeon]
    permission: ultimatedungeons.use
  
  udadmin:
    description: Administrative commands for Ultimate Dungeons
    usage: /udadmin [reload|create|delete|tp|debug]
    permission: ultimatedungeons.admin
  
  party:
    description: Party management commands
    usage: /party [create|join|leave|invite|kick|list]
    aliases: [dparty, dungeonparty]
    permission: ultimatedungeons.party
  
  leave:
    description: Quick leave dungeon command
    usage: /leave
    permission: ultimatedungeons.quickleave
  
  ready:
    description: Ready up for dungeon queue
    usage: /ready
    permission: ultimatedungeons.ready
  
  rewards:
    description: View dungeon rewards
    usage: /rewards
    aliases: [drewards, dungeon-rewards]
    permission: ultimatedungeons.rewards

permissions:
  ultimatedungeons.*:
    description: All Ultimate Dungeons permissions
    default: op
    children:
      ultimatedungeons.use: true
      ultimatedungeons.admin: true
      ultimatedungeons.party: true
      ultimatedungeons.quickleave: true
      ultimatedungeons.ready: true
      ultimatedungeons.rewards: true
      ultimatedungeons.builder: true
      ultimatedungeons.bypass: true
  
  ultimatedungeons.use:
    description: Basic dungeon access
    default: true
  
  ultimatedungeons.admin:
    description: Administrative permissions
    default: op
  
  ultimatedungeons.party:
    description: Party system permissions
    default: true
  
  ultimatedungeons.quickleave:
    description: Quick leave permission
    default: true
  
  ultimatedungeons.ready:
    description: Ready up permission
    default: true
  
  ultimatedungeons.rewards:
    description: View rewards permission
    default: true
  
  ultimatedungeons.builder:
    description: Dungeon building permissions
    default: op
  
  ultimatedungeons.bypass:
    description: Bypass restrictions
    default: op
