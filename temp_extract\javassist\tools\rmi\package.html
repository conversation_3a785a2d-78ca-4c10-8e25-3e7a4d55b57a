<html>
<body>
Sample implementation of remote method invocation.

<p>This package enables applets to access remote objects
running on the web server with regular Java syntax.
It is provided as a sample implementation with Javassist.
All the programs in this package uses only the regular
Javassist API; they never call any hidden methods.

<p>The most significant class of this package is
<code>ObjectImporter</code>.
See the description of this class first.

</body>
</html>
