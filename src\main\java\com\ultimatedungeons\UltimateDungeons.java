package com.ultimatedungeons;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.logging.Level;

public class UltimateDungeons extends JavaPlugin {
    
    private static UltimateDungeons instance;
    private FileConfiguration config;
    private DungeonManager dungeonManager;
    private PartyManager partyManager;
    private RewardManager rewardManager;
    
    @Override
    public void onEnable() {
        instance = this;
        
        // Initialize plugin
        getLogger().info("UltimateDungeons v" + getDescription().getVersion() + " is starting...");
        
        // Create plugin folder if it doesn't exist
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }
        
        // Load configuration
        loadConfig();
        
        // Initialize managers
        dungeonManager = new DungeonManager(this);
        partyManager = new PartyManager(this);
        rewardManager = new RewardManager(this);
        
        // Register commands
        registerCommands();
        
        // Register events
        registerEvents();
        
        getLogger().info("UltimateDungeons has been enabled successfully!");
        getLogger().info("Plugin created with " + getDungeonCount() + " dungeons loaded.");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("UltimateDungeons is shutting down...");
        
        // Save any pending data
        if (dungeonManager != null) {
            dungeonManager.shutdown();
        }
        
        if (partyManager != null) {
            partyManager.shutdown();
        }
        
        getLogger().info("UltimateDungeons has been disabled.");
    }
    
    private void loadConfig() {
        // Copy default config if it doesn't exist
        saveDefaultConfig();
        
        // Load config.yml
        config = getConfig();
        
        // Set default values if not present
        config.addDefault("dungeons.enabled", true);
        config.addDefault("dungeons.max-party-size", 5);
        config.addDefault("dungeons.default-difficulty", "normal");
        config.addDefault("dungeons.auto-start-delay", 30);
        config.addDefault("messages.prefix", "&8[&6UltimateDungeons&8] ");
        config.addDefault("messages.no-permission", "&cYou don't have permission to use this command!");
        config.addDefault("messages.dungeon-joined", "&aYou have joined the dungeon queue!");
        config.addDefault("messages.dungeon-left", "&cYou have left the dungeon.");
        config.addDefault("messages.party-created", "&aParty created successfully!");
        config.addDefault("messages.party-joined", "&aYou joined the party!");
        
        config.options().copyDefaults(true);
        saveConfig();
    }
    
    private void registerCommands() {
        // Commands are automatically registered from plugin.yml
        // Command handling is done in onCommand method
    }
    
    private void registerEvents() {
        // Register event listeners here when you create them
        // getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        String commandName = command.getName().toLowerCase();
        
        switch (commandName) {
            case "ultimatedungeons":
            case "ud":
            case "dungeons":
            case "dungeon":
                return handleMainCommand(sender, args);
                
            case "udadmin":
                return handleAdminCommand(sender, args);
                
            case "party":
            case "dparty":
            case "dungeonparty":
                return handlePartyCommand(sender, args);
                
            case "leave":
                return handleLeaveCommand(sender, args);
                
            case "ready":
                return handleReadyCommand(sender, args);
                
            case "rewards":
            case "drewards":
            case "dungeon-rewards":
                return handleRewardsCommand(sender, args);
                
            default:
                return false;
        }
    }
    
    private boolean handleMainCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.use")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelpMessage(sender);
                break;
                
            case "join":
                if (sender instanceof Player) {
                    Player player = (Player) sender;
                    dungeonManager.joinQueue(player);
                    player.sendMessage(colorize(config.getString("messages.dungeon-joined")));
                } else {
                    sender.sendMessage(colorize("&cOnly players can join dungeons!"));
                }
                break;
                
            case "leave":
                if (sender instanceof Player) {
                    Player player = (Player) sender;
                    dungeonManager.leaveDungeon(player);
                    player.sendMessage(colorize(config.getString("messages.dungeon-left")));
                } else {
                    sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
                }
                break;
                
            case "list":
                sender.sendMessage(colorize("&6Available Dungeons:"));
                sender.sendMessage(colorize("&7- Castle Fortress"));
                sender.sendMessage(colorize("&7- " + getDungeonCount() + " total dungeons loaded"));
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private boolean handleAdminCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.admin")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        if (args.length == 0) {
            sender.sendMessage(colorize("&6UltimateDungeons Admin Commands:"));
            sender.sendMessage(colorize("&7/udadmin reload - Reload configuration"));
            sender.sendMessage(colorize("&7/udadmin debug - Toggle debug mode"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                reloadConfig();
                loadConfig();
                sender.sendMessage(colorize("&aConfiguration reloaded!"));
                break;
                
            case "debug":
                sender.sendMessage(colorize("&aDebug mode toggled!"));
                break;
                
            default:
                sender.sendMessage(colorize("&cUnknown admin command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handlePartyCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&cOnly players can use party commands!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.party")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        Player player = (Player) sender;
        
        if (args.length == 0) {
            player.sendMessage(colorize("&6Party Commands:"));
            player.sendMessage(colorize("&7/party create - Create a party"));
            player.sendMessage(colorize("&7/party join <player> - Join a party"));
            player.sendMessage(colorize("&7/party leave - Leave your party"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "create":
                partyManager.createParty(player);
                player.sendMessage(colorize(config.getString("messages.party-created")));
                break;
                
            case "join":
                if (args.length > 1) {
                    Player target = Bukkit.getPlayer(args[1]);
                    if (target != null) {
                        partyManager.joinParty(player, target);
                        player.sendMessage(colorize(config.getString("messages.party-joined")));
                    } else {
                        player.sendMessage(colorize("&cPlayer not found!"));
                    }
                } else {
                    player.sendMessage(colorize("&cUsage: /party join <player>"));
                }
                break;
                
            case "leave":
                partyManager.leaveParty(player);
                player.sendMessage(colorize("&cYou left the party."));
                break;
                
            default:
                player.sendMessage(colorize("&cUnknown party command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handleLeaveCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.quickleave")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        Player player = (Player) sender;
        dungeonManager.leaveDungeon(player);
        player.sendMessage(colorize(config.getString("messages.dungeon-left")));
        
        return true;
    }
    
    private boolean handleReadyCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(colorize("&cOnly players can ready up!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.ready")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        Player player = (Player) sender;
        dungeonManager.toggleReady(player);
        player.sendMessage(colorize("&aReady status toggled!"));
        
        return true;
    }
    
    private boolean handleRewardsCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.rewards")) {
            sender.sendMessage(colorize(config.getString("messages.no-permission")));
            return true;
        }
        
        sender.sendMessage(colorize("&6Dungeon Rewards:"));
        sender.sendMessage(colorize("&7- Experience points"));
        sender.sendMessage(colorize("&7- Rare items"));
        sender.sendMessage(colorize("&7- Currency rewards"));
        
        return true;
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(colorize("&6=== UltimateDungeons Help ==="));
        sender.sendMessage(colorize("&7/ud join - Join dungeon queue"));
        sender.sendMessage(colorize("&7/ud leave - Leave current dungeon"));
        sender.sendMessage(colorize("&7/ud list - List available dungeons"));
        sender.sendMessage(colorize("&7/party create - Create a party"));
        sender.sendMessage(colorize("&7/ready - Toggle ready status"));
        sender.sendMessage(colorize("&7/rewards - View dungeon rewards"));
    }
    
    private String colorize(String message) {
        if (message == null) return "";
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    private int getDungeonCount() {
        File dungeonsFolder = new File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            return 0;
        }
        File[] files = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        return files != null ? files.length : 0;
    }
    
    public static UltimateDungeons getInstance() {
        return instance;
    }
    
    public DungeonManager getDungeonManager() {
        return dungeonManager;
    }
    
    public PartyManager getPartyManager() {
        return partyManager;
    }
    
    public RewardManager getRewardManager() {
        return rewardManager;
    }
}
