# ULTIMATE FEATURES COMBINED 
 
FROM APEXDUNGEONS: 
- Advanced GUI system with 20+ interfaces 
- Procedural dungeon generation 
- Schematic-based room system 
- Building tools and wands 
- Party management system 
- Chest and mob spawn tools 
- Template and preset system 
- World management 
- Statistics tracking 
- Help and tutorial system 
 
FROM MYTHICDUNGEONS: 
- Advanced trigger system (25+ trigger types) 
- Complex condition system 
- Function system (50+ functions) 
- Reward and loot table system 
- Instance management 
- Variable and data system 
- Integration framework 
- Procedural layout generation 
- Room-based dungeon system 
- Queue and party finder 
- Hotbar menu system 
- BetonQuest integration 
- Citizens NPC integration 
- MythicMobs integration 
