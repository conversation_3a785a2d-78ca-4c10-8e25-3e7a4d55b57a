C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\RedstoneWallTorch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\RecipeChoice.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRecipeBookClickEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\InvalidPluginException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockPhysicsEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\permissions\CommandPermissions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerInteractAtEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Wither.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Dye.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDamageEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\BigDripleaf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerResourcePackStatusEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\SmithingInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\PufferFish.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\SimplexNoiseGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\FormattedCommandAlias.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Chain.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Particle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockBurnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\HelpCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\RedstoneRail.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\structure\StructureManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Allay.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Cauldron.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AbstractSkeleton.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockIgniteEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\tags\ItemTagType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntitySpawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Score.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\FurnaceExtractEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockPistonEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ComplexLivingEntity.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LivingEntity.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TrapDoor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServerEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\io\Wrapper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\HorseInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\BookMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\InventoryHolder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\PortalCreateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\RegisteredServiceProvider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\PitcherCrop.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Fish.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\PersistentDataType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockFormEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\enchantments\Enchantment.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\RaidSpawnWaveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\CommandExecutor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Enderman.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\WoodenStep.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\BeaconInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Sign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\CropState.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\HopperMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Container.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\AreaEffectCloudApplyEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleUpdateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TechnicalPiston.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationFactory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\TabExecutor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionEffectType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityToggleGlideEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\io\BukkitObjectOutputStream.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\TippedArrow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Jukebox.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\recipe\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Furnace.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\FluidCollisionMode.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\MemoryConfigurationOptions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Grindstone.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ItemDisplay.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\WorldCreator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Objective.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Snowball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\SpawnerSpawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerChatEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\IllegalPluginAccessException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\event\entity\EntityDismountEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Item.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\StriderTemperatureChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Crafter.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Farmland.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\enchantment\EnchantItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerItemMendEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\Potion.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Sandstone.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\EventPriority.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\HelpTopicFactory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\packs\DataPackManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Spider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntitySpellCastEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\CalibratedSculkSensor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\SectionPathData.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Keyed.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Piston.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\MaterialData.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Jukebox.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AbstractHorse.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\spawner\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\LimitedRegion.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Furnace.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\PluginCommandYamlParser.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Registry.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\io\BukkitObjectInputStream.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityTransformEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerDropItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Marker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\ChiseledBookshelf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataEvaluationException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataStore.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ItemCraftResult.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Comparator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Shulker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Nameable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\TNTPrimed.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\event\entity\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AbstractVillager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\DragonFireball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Skull.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\EnderChest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BrewingStartEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerItemConsumeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Bat.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PlayerLeashEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\FurnaceRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\profile\PlayerProfile.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\YamlConfiguration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SeaPickle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerEditBookEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\Transformation.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Phantom.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleDestroyEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\GlowLichen.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryDragEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\SpawnerMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleEntityCollisionEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Block.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\NoteBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Salmon.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\KnowledgeBookMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ban\ProfileBanList.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionEffect.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\AmethystCluster.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\ChiseledBookshelf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockDamageEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SculkSensor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\SignChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\HelpMap.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Tripwire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerExpChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\NetherWarts.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\BlockDisplay.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Location.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\PistonBaseMaterial.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ComplexEntityPart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\TripwireHook.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Giant.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Animals.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServerLoadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CopperBulb.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerTakeLecternBookEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityUnleashEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryCreativeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\ProxiedCommandSender.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SpawnCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Bed.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityInteractEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\structure\StructureType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\RideableMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\FoodLevelChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerChangedMainHandEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\HumanEntity.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityTeleportEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBucketEmptyEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\FurnaceBurnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\BundleMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\HangingSign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Steerable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\PerlinNoiseGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\WorldUnloadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\FireworkEffectMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ItemFrame.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\MerchantInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scheduler\BukkitTask.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\PistonExtensionMaterial.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Explosive.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityKnockbackByEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\structure\Structure.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\Listener.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationPrefix.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\structure\StructureRotation.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\CampfireStartEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\PoweredMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\EntitiesUnloadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\RedstoneWire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\EndGateway.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Boss.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scheduler\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\SimplePluginManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\SheepRegrowWoolEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\BiomeParameterPoint.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\ChunkEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Ageable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Observer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\WitherSkeleton.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\event\player\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SkeletonHorse.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Chicken.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\LlamaInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TNT.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\attribute\AttributeModifier.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Cod.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BellResonateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\BroadcastMessageEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\NumberConversions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\advancement\Advancement.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerUnregisterChannelEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\projectiles\BlockProjectileSource.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\java\PluginClassLoader.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\PrepareGrindstoneEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Campfire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\LeavesDecayEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Minecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\TrialSpawner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Skeleton.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\DeathMessageType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\enchantments\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBucketEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\EventException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\trim\TrimMaterial.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Sound.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\BossBar.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PigZombieAngerEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\BlockDataMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\SuspiciousStewMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Leaves.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\sign\SignSide.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\HandlerList.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\spawner\SpawnerEntry.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\FurnaceInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TrialSpawner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Criteria.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\trim\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\World.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ZombieHorse.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\PluginDisableEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BlockState.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Warning.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Crops.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SculkShrieker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Cake.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\Damageable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerPickupItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataValue.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginLogger.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ChunkSnapshot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\BlastingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Bell.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\StorageMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\DragType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ExpBottleEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\BiomeProvider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\SimpleCommandMap.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\SuspiciousSand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerItemDamageEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataValueAdapter.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\SoundCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Chunk.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\ChunkGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BlockSupport.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\FireworkMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\packs\ResourcePack.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginLoader.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\PotionMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ChiseledBookshelfInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\java\JavaPluginLoader.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Wood.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\BanEntry.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Banner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerPreLoginEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerItemBreakEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Directional.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Difficulty.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Rotatable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Art.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\java\LibraryLoader.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Ladder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Chest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\TreeType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Axolotl.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerKickEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityBreedEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\PersistentDataAdapterContext.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockPlaceEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Rails.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\WallSign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerSpawnChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Attachable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Creature.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Piglin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Observer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\Permission.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ban\IpBanList.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Dripleaf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\GrassSpecies.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockFadeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerJoinEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerSignOpenEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EvokerFangs.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CraftingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\EnchantingTable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\BarFlag.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDamageByBlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ItemFactory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ManuallyAbandonedConversationCanceller.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockFertilizeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\Configuration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Tadpole.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Snow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\enchantment\PrepareItemEnchantEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\CustomTimingsHandler.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\EventExecutor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\NoiseGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Evoker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionData.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SculkVein.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Sapling.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerExpCooldownChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Door.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\PermissionAttachmentInfo.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBedLeaveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\RedstoneTorch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\KeyedBossBar.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Lantern.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Ocelot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\CommandSender.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Smoker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Server.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Barrel.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityCombustEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Command.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\attribute\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerHideEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\FireworkEffect.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\DirectionalContainer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\advancement\AdvancementProgress.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\PermissibleBase.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\memory\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\StringUtil.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SizedFireball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Rail.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityRegainHealthEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginDescriptionResolver.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDamageByEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\ListPersistentDataTypeProvider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SpectralArrow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Color.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\PiglinBrute.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\RegisteredListener.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBedEnterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Donkey.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\PolarBear.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\permissions\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\BukkitCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapRenderer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\RemoteServerCommandEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Breedable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\CauldronLevelChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\MushroomCow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\ChunkPopulateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\StructureType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\EnchantmentStorageMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\RenderType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\ServerOperator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\MagmaCube.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Openable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Warden.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityCombustByBlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\TimedRegisteredListener.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleEnterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\CreatureSpawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Fireball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityTargetLivingEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Flying.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ShapedRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\PortalType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\PrepareSmithingEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Button.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerShearEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\BiomeSearchResult.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityExhaustionEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Hangable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\weather\LightningStrikeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\BooleanPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Openable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\PigZombie.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\HorseJumpEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Hopper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityCombustByEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\UnknownDependencyException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Pumpkin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\EntityTransformer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\ScoreboardManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServerCommandEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EnderCrystal.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\BlockIterator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\PlayerNamePrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\DaylightDetector.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\PoweredRail.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Creeper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Goat.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\BlockVector.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\TexturedMaterial.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\MultipleFacing.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AreaEffectCloud.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationContext.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\NameTagVisibility.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Team.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerAnimationType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\EulerAngle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\tags\CustomItemTagContainer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\AsyncStructureSpawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginDescriptionFile.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\structure\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LeashHitch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBucketEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Criterias.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Candle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\FileConfiguration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\enchantments\EnchantmentTarget.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SmallFireball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\EntityBlockFormEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\loot\LootContext.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityTargetEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Redstone.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerChannelEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ArmorStand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Fluid.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\LeatherArmorMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CampfireRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\permissions\DefaultPermissions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockFromToEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\EntitiesLoadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDeathEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\types\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBucketFishEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRespawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Tameable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Stairs.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Bed.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Dropper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\attribute\Attribute.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Bisected.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryCloseEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\ListPersistentDataType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\FileUtil.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\serialization\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityTameEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EnderSignal.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockDamageAbortEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SmallDripleaf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\CaveSpider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Diode.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockShearEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Lidded.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\PrepareAnvilEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Tag.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\MapInitializeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapPalette.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\RespawnAnchor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityResurrectEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\BlockChangeDelegate.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerCommandPreprocessEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\MainHand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\WeatherType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\TreeSpecies.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\IndexHelpTopic.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\PointedDripstone.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Dispenser.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Comparator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LingeringPotion.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\BanList.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerPortalEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\InvalidConfigurationException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Sapling.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Villager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockGrowEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\java\JavaPlugin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\weather\ThunderChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\WorldInitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Skull.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Lever.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Cocoa.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\AuthorNagException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\serialization\DelegateDeserialization.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Lightable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\SmokingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Campfire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Torch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Hopper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockPistonExtendEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\IronGolem.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\attribute\Attributable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CrafterInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\SandstoneType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\StructureBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\BrewingStand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\AsyncStructureGenerateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BrushableBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TripwireHook.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryInteractEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\PinkPetals.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\permissions\BroadcastPermissions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleDamageEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerLoginEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Horse.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\RaidEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ZombieVillager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\Cancellable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\TradeSelectEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerCommandSendEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Vex.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\PluginChannelDirection.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Llama.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerFishEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\StonecuttingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Pillager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Snowable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\PiglinAbstract.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\InvalidDescriptionException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\FallingBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\BarStyle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\Vector.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\Inventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionEffectTypeWrapper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\Action.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPickupItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\PistonHead.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\LightningRod.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\structure\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\LongGrass.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\Consumer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\hanging\HangingPlaceEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\tags\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\DoubleChest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerVelocityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerPickupArrowEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityShootBowEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityCreatePortalEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\SkullType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\Scoreboard.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Sign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerTeleportEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\UnsafeValues.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\structure\GeneratedStructure.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Waterlogged.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Mushroom.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\SmithingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CaveVinesPlant.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Jigsaw.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\DragonBattle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Ambient.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRegisterChannelEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationAbandonedEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Statistic.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\structure\Palette.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ShapelessRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPoseChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Trident.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\PluginEnableEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Hopper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\ExplosiveMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Note.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PlayerDeathEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\banner\Pattern.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Stray.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\GenericGameEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServiceRegisterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\NumericPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\ChunkLoadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\StructureGrowEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\SkullMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Door.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockMultiPlaceEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Ladder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Firework.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\TileState.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapCanvas.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Witch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\EntityBlockStorage.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\MusicInstrumentMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationCanceller.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPlaceEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\EntityEffect.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\JukeboxInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Vine.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPortalExitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Gate.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockExplodeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Rabbit.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\GlowItemFrame.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\WorldSaveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\ShulkerBox.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Illager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\RegionAccessor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Repeater.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\serialization\ConfigurationSerializable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ConversationAbandonedListener.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginBase.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\GameMode.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Chest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ThrowableProjectile.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Vibration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\structure\Structure.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\ChannelNameTooLongException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\SculkCatalyst.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Barrel.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\CocoaPlant.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\TurtleEgg.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ComplexRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\CommandBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\hanging\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\FixedSetPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\Prompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Effect.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Directional.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\RegexPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Attachable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\BlockPopulator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Translatable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\WaterMob.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\FileConfigurationOptions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\SlimeSplitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Coal.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\EnderChest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\WorldType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ProjectileHitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CommandBlock.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\TNTPrimeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRecipeBookSettingsChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\PluginsCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\ClickType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\Command.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\HangingSign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\structure\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\loot\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\types\MushroomBlockTexture.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Wool.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\RemoteConsoleCommandSender.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\EventHandler.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\AxolotlBucketMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CoralWallFan.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\SculkBloomEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Chest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\MemoryConfiguration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Beehive.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Endermite.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\Permissible.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\serialization\ConfigurationSerialization.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\tags\ItemTagAdapterContext.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Frog.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Wall.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\potion\PotionBrewer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\WorldBorder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockReceiveGameEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryClickEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryPickupItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerStatisticIncrementEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerEggThrowEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\recipe\CookingBookCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Panda.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\FeatureFlag.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\BarColor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\StringPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerItemHeldEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\VoxelShape.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\AsyncPlayerChatEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Conduit.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Vindicator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\TropicalFish.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\CachedServerIcon.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Ravager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Structure.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\TrapDoor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\CommandMap.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\BannerMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Tripwire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\DamageEffect.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\PluginCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\SpawnChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockSpreadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerSwapHandItemsEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Breeze.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Projectile.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\PressurePlate.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\WallHangingSign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\StandardMessenger.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\SculkCatalyst.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\DisplaySlot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\Event.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerAdvancementDoneEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LargeFireball.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Slime.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\serialization\SerializableAs.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\AbstractHorseInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\ConfigurationOptions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EnderDragonPart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\BlockCommandSender.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\PerlinOctaveGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PigZapEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EnderPearl.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockDispenseEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleMoveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\DetectorRail.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Stairs.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\weather\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Dolphin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\DyeColor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EntityCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BellRingEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\BrewerInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\SmithingTransformRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scheduler\BukkitScheduler.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Beacon.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityKnockbackEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Crafter.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\MultipleCommandAlias.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\BoundingBox.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Hoglin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CreativeCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDropItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\UndefinedNullability.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerMoveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\PermissionRemovedExecutor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\NotePlayEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityBreakDoorEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\WorldInfo.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerArmorStandManipulateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\ArmorMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ChestedHorse.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleBlockCollisionEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockCookEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\DecoratedPot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\RaidTriggerEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\MonsterEggs.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleExitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\profile\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\HelpTopicComparator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Tree.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\loot\Lootable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Squid.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\CreatureSpawner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EnderDragon.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\BrewingStandFuelEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\MerchantRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Dispenser.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Enemy.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ValidatingPrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Rotation.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CraftingInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\advancement\AdvancementDisplayType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AnimalTamer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\PluginIdentifiableCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\TraderLlama.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Lectern.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Lockable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\ChunkUnloadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Guardian.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Scaffolding.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityToggleSwimEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\io\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Jigsaw.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\PluginMessageListener.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BlastFurnace.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapFont.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LlamaSpit.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\BlockStateMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\structure\UsageMode.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\AsyncPlayerPreLoginEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\advancement\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Mob.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\PistonMoveReaction.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Blaze.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Painting.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Entity.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\LightningStrike.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\ItemMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginLoadOrder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\spawner\SpawnRule.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityEnterLoveModeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\loot\LootTable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\BlockInventoryHolder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPortalEnterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\TabCompleter.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\WindCharge.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\hanging\HangingBreakEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\sign\Side.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerLocaleChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\Recipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerToggleSprintEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\hanging\HangingEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\event\player\PlayerSpawnLocationEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\structure\Mirror.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\memory\MemoryKey.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\PluginEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Material.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EntitySnapshot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Boat.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServiceUnregisterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\PrepareItemCraftEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ProjectileLaunchEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Fire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\SpawnEggMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\StonecutterInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\YamlConstructor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BlockFace.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Bukkit.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Vehicle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\ConfigurationSection.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\LazyMetadataValue.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPotionEffectEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ElderGuardian.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\attribute\AttributeInstance.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\banner\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Egg.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\BlockData.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\spigotmc\event\entity\EntityMountEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\TextDisplay.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\TabCompleteEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\CommandException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\DamageType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\FaceAttachable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\enchantments\EnchantmentWrapper.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\ExactMatchConversationCanceller.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\ReloadCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ItemSpawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Zombie.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Gate.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\HopperInventorySearchEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleCollisionEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\PluginNameConversationPrefix.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\vehicle\VehicleCreateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PotionSplashEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ItemStack.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Leaves.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataConversionException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\GlassPane.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Bee.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerInteractEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Interaction.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Pig.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\ReservedChannelException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\ConsoleCommandSender.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Dispenser.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockDropItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\YamlConfigurationOptions.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\projectiles\ProjectileSource.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\SmoothBrick.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Illusioner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\java\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\WanderingTrader.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\CompassMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\TropicalFishBucketMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\EnderChest.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Wolf.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockExpEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\SculkSensor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ban\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Pose.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Banner.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\InactivityConversationCanceller.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\CommandMinecart.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CookingRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Display.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\MangrovePropagule.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\WitherSkull.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\ItemFlag.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Camel.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ThrownExpBottle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\SculkShrieker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerQuitEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Light.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Powerable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Parrot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ThrownPotion.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\ChannelNotRegisteredException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scheduler\BukkitWorker.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ItemMergeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\GameRule.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockPistonRetractEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\RaidStopEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\profile\PlayerTextures.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\enchantments\EnchantmentOffer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Fence.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\Plugin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerAnimationEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\CrossbowMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\AbstractArrow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockCanBuildEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Biome.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\structure\StructurePiece.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Furnace.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Bamboo.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\Conversable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\MapMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\SplashPotion.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Bell.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ShulkerBullet.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Sniffer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\InventoryBlockStartEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockBreakEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\GlowSquid.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\Merchant.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Sign.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\GameEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\NetherWartsState.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\FixedMetadataValue.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\banner\PatternType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\hanging\HangingBreakByEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Arrow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\DamageScaling.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\DecoratedPotInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapCursorCollection.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\DecoratedPot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRecipeDiscoverEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Step.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\VillagerAcquireTradeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\Conversation.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\AnvilInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Zoglin.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Spellcaster.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\recipe\CraftingBookCategory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\BrewEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\FurnaceSmeltEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\PermissionAttachment.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Switch.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\trim\ArmorTrim.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ExplosionPrimeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\SmithingTrimRecipe.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\ServicePriority.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\PluginMessageRecipient.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\FlowerPot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\PiglinBarterEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServerListPingEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityEnterBlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\EquipmentSlot.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\PersistentDataContainer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\StructureSearchResult.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\LecternInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\Repairable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Ageable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerRiptideEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EnderDragonChangePhaseEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\ExtendedRails.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CaveVines.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryAction.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityDismountEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\InventoryView.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Ghast.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\DoubleChestInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\boss\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityChangeBlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\Bed.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\generator\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Cow.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Orientable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\DaylightDetector.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Hanging.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Silverfish.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Brushable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\SheepDyeWoolEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\OfflinePlayer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\LoomInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Raider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockDispenseArmorEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\LootGenerateEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\PluginMessageListenerRegistration.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\OctaveGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\AnaloguePowerable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Monster.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\MinecraftExperimental.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\ServicesManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\weather\WeatherChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\noise\SimplexOctaveGenerator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scheduler\BukkitRunnable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\damage\DamageSource.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\PressureSensor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\PrepareInventoryResultEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerInteractEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\HeightMap.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\RedstoneWire.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\ColorableArmorMeta.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\FurnaceAndDispenser.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\permissions\PermissionDefault.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Snowman.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\server\ServiceEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\GenericCommandHelpTopic.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\weather\WeatherEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\EntityEquipment.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityMountEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\minecart\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\ChatPaginator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\AsyncPlayerChatPreviewEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\help\HelpTopic.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerToggleFlightEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Golem.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\scoreboard\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Sittable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryMoveItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\enchantment\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Fox.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityPortalEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\projectiles\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Levelled.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Axis.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\CartographyInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Player.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\FireworkExplodeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapCursor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\EndPortalFrame.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\BlockRedstoneEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\NamespacedKey.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityRemoveEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerHarvestBlockEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MapView.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Lectern.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Comparator.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\BlockTransformer.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\WorldEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ChestBoat.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\CraftItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerLevelChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerUnleashEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\VillagerReplenishTradeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\NullConversationPrefix.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\EntityType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Mule.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Turtle.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\CoalType.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerChatTabCompleteEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\Metadatable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\loot\LootTables.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Sheep.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\metadata\MetadataStoreBase.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Drowned.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\MessageTooLargeException.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\util\RayTraceResult.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Beehive.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\TimeSkipEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\EnchantingInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\BubbleColumn.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\VersionCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ServerTickManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\SimpleAttachableMaterialData.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\CreeperPowerEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\PlayerInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityAirChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\packs\DataPack.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Strider.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\SpongeAbsorbEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\VillagerCareerChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\SoundGroup.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ItemDespawnEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerBucketFillEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\ArrowBodyCountChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Instrument.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\GrindstoneInventory.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\PluginAwareness.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\InventoryOpenEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerToggleSneakEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerShowEntityEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\world\WorldLoadEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\EntityExplodeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\FluidLevelChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\CalibratedSculkSensor.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\sign\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\file\YamlRepresenter.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\inventory\meta\trim\TrimPattern.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\packs\package-info.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\Hatchable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\raid\RaidFinishEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\messaging\Messenger.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\block\MoistureChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Husk.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerChangedWorldEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\BrewingStand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Cat.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\BatToggleSleepEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Raid.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\command\defaults\TimingsCommand.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\entity\LingeringPotionSplashEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\advancement\AdvancementDisplay.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\map\MinecraftFont.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\Damageable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\NPC.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\configuration\MemorySection.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\SpawnEgg.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\FurnaceStartSmeltEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Colorable.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\block\data\type\Slab.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\plugin\SimpleServicesManager.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\ExperienceOrb.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\inventory\SmithItemEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\conversations\MessagePrompt.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\material\Cake.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\MusicInstrument.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\entity\FishHook.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\Utility.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\event\player\PlayerGameModeChangeEvent.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\persistence\PersistentDataHolder.java
C:\Users\<USER>\Downloads\dg\Spigot\Spigot-API\src\main\java\org\bukkit\ChatColor.java
