test:
  ==: org.bukkit.inventory.ItemStack
  v: 2584
  type: NETHER_STAR
  meta:
    ==: ItemMeta
    meta-type: UNSPECIFIC
    PublicBukkitValues:
      test:string_simple: stringy
      test:integer: 2147483647i
      test:long_array: '[L;-9223372036854775808L]'
      test:byte: 127b
      test:double: 1.7976931348623157E308d
      test:short: 32767s
      test:string_complex: What a fun complex string 🔥
      test:integer_array: '[I;-2147483648]'
      test:float: 3.4028235E38f
      test:byte_array: '[B;-128B]'
      test:long: 9223372036854775807L
      test:string_edge_case_number: '5'
      # Constructed via set(key, STRING, "\"Hello world\"") in legacy
      test:string_edge_case_quoted: '"Hello world"'

