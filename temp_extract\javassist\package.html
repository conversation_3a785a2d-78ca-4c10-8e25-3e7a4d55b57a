<html>
<body>
The Javassist Core API.

<p>Javassist (<i>Java</i> programming <i>assist</i>ant) makes bytecode
engineering simple.  It is a class library for editing
bytecode in Java; it enables Java programs to define a new class at
runtime and to modify a given class file when the JVM loads it.

<p>The most significant class of this package is <code>CtClass</code>.
See the description of this class first.

<p>To know the version number of this package, type the following command:

<pre>
java -jar javassist.jar
</pre>

<p>It prints the version number on the console.

</body>
</html>
