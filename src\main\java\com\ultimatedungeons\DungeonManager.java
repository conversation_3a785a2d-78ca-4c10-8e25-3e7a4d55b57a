package com.ultimatedungeons;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.io.File;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class DungeonManager {
    
    private final UltimateDungeons plugin;
    private final Map<UUID, DungeonSession> activeSessions;
    private final Map<UUID, DungeonQueue> playerQueues;
    private final Set<UUID> readyPlayers;
    private final List<DungeonTemplate> dungeonTemplates;
    
    public DungeonManager(UltimateDungeons plugin) {
        this.plugin = plugin;
        this.activeSessions = new ConcurrentHashMap<>();
        this.playerQueues = new ConcurrentHashMap<>();
        this.readyPlayers = ConcurrentHashMap.newKeySet();
        this.dungeonTemplates = new ArrayList<>();
        
        loadDungeonTemplates();
        startQueueProcessor();
    }
    
    private void loadDungeonTemplates() {
        File dungeonsFolder = new File(plugin.getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            dungeonsFolder.mkdirs();
            plugin.getLogger().info("Created dungeons folder");
            return;
        }
        
        File[] schematicFiles = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        if (schematicFiles != null) {
            for (File file : schematicFiles) {
                String name = file.getName().replace(".schematic", "");
                DungeonTemplate template = new DungeonTemplate(name, file);
                dungeonTemplates.add(template);
                plugin.getLogger().info("Loaded dungeon template: " + name);
            }
        }
        
        plugin.getLogger().info("Loaded " + dungeonTemplates.size() + " dungeon templates");
    }
    
    private void startQueueProcessor() {
        new BukkitRunnable() {
            @Override
            public void run() {
                processQueues();
            }
        }.runTaskTimer(plugin, 20L, 20L); // Run every second
    }
    
    private void processQueues() {
        // Group players by dungeon type and try to form parties
        Map<String, List<UUID>> queuesByDungeon = new HashMap<>();
        
        for (Map.Entry<UUID, DungeonQueue> entry : playerQueues.entrySet()) {
            UUID playerId = entry.getKey();
            DungeonQueue queue = entry.getValue();
            
            Player player = Bukkit.getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                playerQueues.remove(playerId);
                continue;
            }
            
            queuesByDungeon.computeIfAbsent(queue.getDungeonType(), k -> new ArrayList<>()).add(playerId);
        }
        
        // Try to start dungeons for each type
        for (Map.Entry<String, List<UUID>> entry : queuesByDungeon.entrySet()) {
            String dungeonType = entry.getKey();
            List<UUID> players = entry.getValue();
            
            if (players.size() >= getMinPartySize()) {
                startDungeon(dungeonType, players.subList(0, Math.min(players.size(), getMaxPartySize())));
            }
        }
    }
    
    public void joinQueue(Player player) {
        if (isInDungeon(player)) {
            player.sendMessage(colorize("&cYou are already in a dungeon!"));
            return;
        }
        
        if (playerQueues.containsKey(player.getUniqueId())) {
            player.sendMessage(colorize("&cYou are already in the queue!"));
            return;
        }
        
        // Default to first available dungeon template
        String dungeonType = dungeonTemplates.isEmpty() ? "default" : dungeonTemplates.get(0).getName();
        
        DungeonQueue queue = new DungeonQueue(player.getUniqueId(), dungeonType, System.currentTimeMillis());
        playerQueues.put(player.getUniqueId(), queue);
        
        player.sendMessage(colorize("&aYou have joined the dungeon queue! (" + getQueuePosition(player) + " in queue)"));
        
        // Notify other players in queue
        notifyQueueUpdate();
    }
    
    public void leaveQueue(Player player) {
        if (playerQueues.remove(player.getUniqueId()) != null) {
            player.sendMessage(colorize("&cYou have left the dungeon queue."));
            notifyQueueUpdate();
        }
    }
    
    public void leaveDungeon(Player player) {
        UUID playerId = player.getUniqueId();
        
        // Remove from queue if in queue
        leaveQueue(player);
        
        // Remove from active session if in dungeon
        DungeonSession session = activeSessions.get(playerId);
        if (session != null) {
            session.removePlayer(player);
            activeSessions.remove(playerId);
            
            // Teleport player to spawn or previous location
            teleportToSafety(player);
            
            player.sendMessage(colorize("&cYou have left the dungeon."));
        }
        
        // Remove ready status
        readyPlayers.remove(playerId);
    }
    
    public void toggleReady(Player player) {
        UUID playerId = player.getUniqueId();
        
        if (!playerQueues.containsKey(playerId)) {
            player.sendMessage(colorize("&cYou must be in the dungeon queue to ready up!"));
            return;
        }
        
        if (readyPlayers.contains(playerId)) {
            readyPlayers.remove(playerId);
            player.sendMessage(colorize("&cYou are no longer ready."));
        } else {
            readyPlayers.add(playerId);
            player.sendMessage(colorize("&aYou are now ready!"));
        }
        
        notifyQueueUpdate();
    }
    
    private void startDungeon(String dungeonType, List<UUID> playerIds) {
        // Remove players from queue
        for (UUID playerId : playerIds) {
            playerQueues.remove(playerId);
            readyPlayers.remove(playerId);
        }
        
        // Create dungeon session
        DungeonSession session = new DungeonSession(dungeonType, playerIds);
        
        // Add players to session
        for (UUID playerId : playerIds) {
            activeSessions.put(playerId, session);
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(colorize("&aStarting dungeon: " + dungeonType + "!"));
                // TODO: Teleport to dungeon location
                // teleportToDungeon(player, session);
            }
        }
        
        plugin.getLogger().info("Started dungeon " + dungeonType + " with " + playerIds.size() + " players");
    }
    
    private void teleportToSafety(Player player) {
        // Teleport to world spawn as fallback
        World world = Bukkit.getWorlds().get(0); // Main world
        if (world != null) {
            Location spawn = world.getSpawnLocation();
            player.teleport(spawn);
        }
    }
    
    private void notifyQueueUpdate() {
        for (UUID playerId : playerQueues.keySet()) {
            Player player = Bukkit.getPlayer(playerId);
            if (player != null) {
                int position = getQueuePosition(player);
                boolean isReady = readyPlayers.contains(playerId);
                
                player.sendMessage(colorize("&7Queue position: " + position + 
                    " | Ready: " + (isReady ? "&aYes" : "&cNo")));
            }
        }
    }
    
    private int getQueuePosition(Player player) {
        List<DungeonQueue> sortedQueues = new ArrayList<>(playerQueues.values());
        sortedQueues.sort(Comparator.comparing(DungeonQueue::getJoinTime));
        
        for (int i = 0; i < sortedQueues.size(); i++) {
            if (sortedQueues.get(i).getPlayerId().equals(player.getUniqueId())) {
                return i + 1;
            }
        }
        return -1;
    }
    
    public boolean isInDungeon(Player player) {
        return activeSessions.containsKey(player.getUniqueId());
    }
    
    public boolean isInQueue(Player player) {
        return playerQueues.containsKey(player.getUniqueId());
    }
    
    private int getMinPartySize() {
        return plugin.getConfig().getInt("dungeons.min-party-size", 1);
    }
    
    private int getMaxPartySize() {
        return plugin.getConfig().getInt("dungeons.max-party-size", 5);
    }
    
    private String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public void shutdown() {
        // Clean up active sessions
        for (DungeonSession session : activeSessions.values()) {
            session.cleanup();
        }
        activeSessions.clear();
        playerQueues.clear();
        readyPlayers.clear();
    }
    
    public List<DungeonTemplate> getDungeonTemplates() {
        return new ArrayList<>(dungeonTemplates);
    }
    
    // Inner classes
    public static class DungeonQueue {
        private final UUID playerId;
        private final String dungeonType;
        private final long joinTime;
        
        public DungeonQueue(UUID playerId, String dungeonType, long joinTime) {
            this.playerId = playerId;
            this.dungeonType = dungeonType;
            this.joinTime = joinTime;
        }
        
        public UUID getPlayerId() { return playerId; }
        public String getDungeonType() { return dungeonType; }
        public long getJoinTime() { return joinTime; }
    }
    
    public static class DungeonSession {
        private final String dungeonType;
        private final List<UUID> playerIds;
        private final long startTime;
        
        public DungeonSession(String dungeonType, List<UUID> playerIds) {
            this.dungeonType = dungeonType;
            this.playerIds = new ArrayList<>(playerIds);
            this.startTime = System.currentTimeMillis();
        }
        
        public void removePlayer(Player player) {
            playerIds.remove(player.getUniqueId());
        }
        
        public void cleanup() {
            // Clean up dungeon resources
        }
        
        public String getDungeonType() { return dungeonType; }
        public List<UUID> getPlayerIds() { return new ArrayList<>(playerIds); }
        public long getStartTime() { return startTime; }
    }
    
    public static class DungeonTemplate {
        private final String name;
        private final File schematicFile;
        
        public DungeonTemplate(String name, File schematicFile) {
            this.name = name;
            this.schematicFile = schematicFile;
        }
        
        public String getName() { return name; }
        public File getSchematicFile() { return schematicFile; }
    }
}
