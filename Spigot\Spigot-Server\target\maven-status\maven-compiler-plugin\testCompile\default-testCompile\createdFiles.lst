org\bukkit\LootTablesTest.class
org\bukkit\PerMaterialTest.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$13.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$28.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$8.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$19.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$1.class
org\bukkit\registry\RegistryArgumentAddedTest.class
org\bukkit\registry\RegistryLoadOrderTest.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$3.class
org\bukkit\SoundTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$7.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$2.class
org\bukkit\craftbukkit\entity\EntityTypesTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$13.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$30.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$1.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$19.class
org\bukkit\support\MatcherAssert.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$20.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$8.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$10.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$3.class
org\bukkit\craftbukkit\block\BlockStateTest.class
org\bukkit\craftbukkit\inventory\ItemStackTest$StackWrapper.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$9.class
org\bukkit\support\provider\RegistryArgumentProvider.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$22.class
org\bukkit\PotionEffectTypeTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$6.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$21.class
org\bukkit\craftbukkit\inventory\ItemStackTest$BukkitWrapper.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$8.class
org\bukkit\enchantments\EnchantmentTargetTest.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$18.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$29.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$23.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$2.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$2.class
org\bukkit\craftbukkit\attribute\AttributeTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$3.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$14.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$7.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$20.class
org\bukkit\craftbukkit\inventory\ItemStackTest$CraftWrapper.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$4.class
org\bukkit\MaterialTest.class
org\bukkit\registry\RegistryLoadOrderTest$BukkitAbstractTestType.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$7.class
org\bukkit\registry\RegistryLoadOrderTest$CraftBukkitAbstractTestType.class
org\bukkit\craftbukkit\inventory\ItemMetaImplementationOverrideTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$21.class
org\bukkit\generator\structure\StructureTest.class
org\bukkit\entity\memory\CraftMemoryKeyTest.class
org\bukkit\craftbukkit\inventory\NMSCraftItemStackTest.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$14.class
org\bukkit\entity\EnderDragonPhaseTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$8.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$7.class
org\bukkit\craftbukkit\inventory\ItemMetaTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$16.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$17.class
org\bukkit\craftbukkit\inventory\PersistentDataContainerTest$PrimitiveTagType.class
org\bukkit\potion\PotionTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$7.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$8.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$17.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$15.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$9.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$11.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$1.class
org\bukkit\registry\RegistryLoadOrderTest$BukkitInterfaceTestType.class
org\bukkit\craftbukkit\generator\ChunkDataTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$12.class
org\bukkit\DyeColorsTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$6.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$18.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$14.class
org\bukkit\registry\PerRegistryTest.class
org\bukkit\registry\RegistryLoadOrderTest$MinecraftTestType.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$10.class
org\bukkit\support\Matchers.class
org\bukkit\GameRuleTest.class
org\bukkit\support\test\RegistriesTest.class
org\bukkit\craftbukkit\inventory\PlayerInventoryTest.class
org\bukkit\craftbukkit\inventory\ItemStackTest$CompoundOperator$RecursiveContainer.class
org\bukkit\craftbukkit\inventory\ItemStackTest$Operator.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$16.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$20.class
org\bukkit\registry\RegistryConversionTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$5.class
org\bukkit\damage\DamageTypeTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$15.class
org\bukkit\craftbukkit\profile\PlayerProfileTest.class
org\bukkit\craftbukkit\inventory\FactoryItemMaterialTest.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$12.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$16.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$18.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$3.class
org\bukkit\ChatTest.class
org\bukkit\craftbukkit\inventory\DeprecatedItemMetaCustomValueTest$PrimitiveTagType.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$4.class
org\bukkit\craftbukkit\inventory\ItemMetaImplementationOverrideTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$2.class
org\bukkit\craftbukkit\legacy\EvilTest.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$7.class
org\bukkit\craftbukkit\legacy\PersistentDataContainerLegacyTest.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$9.class
org\bukkit\generator\structure\StructureTypeTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$5.class
org\bukkit\registry\RegistryConstantsTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$25.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$10.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$8.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$16.class
org\bukkit\craftbukkit\inventory\PersistentDataContainerTest$UUIDPersistentDataType.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$9.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest.class
org\bukkit\support\provider\RegistriesArgumentProvider.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$14.class
org\bukkit\craftbukkit\inventory\ItemStackTest$NoOpProvider.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$8.class
org\bukkit\craftbukkit\inventory\ItemMetaImplementationOverrideTest$2.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$8.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$11.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$2.class
org\bukkit\entity\EntityPoseTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$13.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$15.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackTest$StackProvider.class
org\bukkit\registry\RegistryLoadOrderTest$CraftBukkitInterfaceTestType.class
org\bukkit\support\test\RegistryTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$15.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$7.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$10.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$19.class
org\bukkit\craftbukkit\inventory\DeprecatedItemMetaCustomValueTest.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$9.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$3.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$3.class
org\bukkit\BiomeTest.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$10.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$17.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$12.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$11.class
org\bukkit\craftbukkit\inventory\DeprecatedItemMetaCustomValueTest$UUIDItemTagType.class
org\bukkit\entity\TropicalFishTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$3.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$9.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$24.class
org\bukkit\entity\SpawnCategoryTest.class
org\bukkit\GameEventTest.class
org\bukkit\entity\EntityTypesTest.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$5.class
org\bukkit\craftbukkit\legacy\LegacyTest.class
org\bukkit\craftbukkit\HeightMapTest.class
org\bukkit\map\MapTest.class
org\bukkit\craftbukkit\inventory\PersistentDataContainerTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$10.class
org\bukkit\block\banner\PatternTypeTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$2.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$13.class
org\bukkit\craftbukkit\inventory\CompositeSerialization.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$2.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$10.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$9.class
org\bukkit\EnchantmentTest.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$2.class
org\bukkit\ArtTest.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest$1.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest.class
org\bukkit\entity\PandaGeneTest.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$2.class
org\bukkit\ParticleTest.class
org\bukkit\craftbukkit\inventory\ItemStackTest$CompoundOperator.class
org\bukkit\support\AbstractTestingBase.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$2.class
org\bukkit\craftbukkit\inventory\ItemStackTest$CompoundOperator$1.class
org\bukkit\craftbukkit\util\CraftChatMessageTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$11.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$7.class
org\bukkit\craftbukkit\inventory\ItemStackPotionsTest.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$3.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$5.class
org\bukkit\event\EntityRemoveEventTest.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$5.class
org\bukkit\craftbukkit\inventory\ItemStackMapTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$26.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$12.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest$3.class
org\bukkit\StatisticsAndAchievementsTest.class
org\bukkit\craftbukkit\inventory\ItemStackSkullTest$1.class
org\bukkit\BlockDataConversionTest.class
org\bukkit\craftbukkit\inventory\ItemStackLoreEnchantmentTest$27.class
org\bukkit\support\DummyServer.class
org\bukkit\craftbukkit\inventory\ItemStackLeatherTest$3.class
org\bukkit\craftbukkit\inventory\ItemStackTest$StackProvider$1.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$4.class
org\bukkit\entity\BoatTest.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$4.class
org\bukkit\craftbukkit\inventory\ItemStackBookTest.class
org\bukkit\craftbukkit\inventory\ItemMetaTest$11.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkChargeTest$6.class
org\bukkit\craftbukkit\inventory\ItemStackEnchantStorageTest$6.class
org\bukkit\craftbukkit\inventory\ItemMetaCloneTest.class
org\bukkit\support\Matchers$SameHash.class
org\bukkit\craftbukkit\inventory\ItemStackFireworkTest$12.class
org\bukkit\BlockDataTest.class
