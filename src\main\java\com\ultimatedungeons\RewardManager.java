package com.ultimatedungeons;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

public class RewardManager {
    
    private final UltimateDungeons plugin;
    private final Map<String, List<Reward>> dungeonRewards;
    private final Map<UUID, PlayerRewardData> playerData;
    
    public RewardManager(UltimateDungeons plugin) {
        this.plugin = plugin;
        this.dungeonRewards = new HashMap<>();
        this.playerData = new HashMap<>();
        
        initializeDefaultRewards();
    }
    
    private void initializeDefaultRewards() {
        // Default rewards for different dungeon types
        List<Reward> defaultRewards = Arrays.asList(
            new Reward(RewardType.EXPERIENCE, 100, 0.8),
            new Reward(RewardType.ITEM, createRewardItem(Material.DIAMOND, 1), 0.3),
            new Reward(RewardType.ITEM, createRewardItem(Material.EMERALD, 2), 0.5),
            new Reward(RewardType.ITEM, createRewardItem(Material.GOLD_INGOT, 5), 0.7),
            new Reward(RewardType.CURRENCY, 50, 0.6)
        );
        
        dungeonRewards.put("default", defaultRewards);
        dungeonRewards.put("castle_fortress", Arrays.asList(
            new Reward(RewardType.EXPERIENCE, 200, 0.9),
            new Reward(RewardType.ITEM, createRewardItem(Material.DIAMOND_SWORD, 1), 0.2),
            new Reward(RewardType.ITEM, createRewardItem(Material.DIAMOND_ARMOR, 1), 0.15),
            new Reward(RewardType.CURRENCY, 100, 0.8)
        ));
        
        plugin.getLogger().info("Initialized reward system with " + dungeonRewards.size() + " dungeon types");
    }
    
    public void giveRewards(Player player, String dungeonType, boolean completed) {
        if (!completed) {
            player.sendMessage(colorize("&cDungeon not completed - no rewards given."));
            return;
        }
        
        List<Reward> rewards = dungeonRewards.getOrDefault(dungeonType, dungeonRewards.get("default"));
        if (rewards == null || rewards.isEmpty()) {
            player.sendMessage(colorize("&cNo rewards configured for this dungeon."));
            return;
        }
        
        List<Reward> givenRewards = new ArrayList<>();
        
        for (Reward reward : rewards) {
            if (ThreadLocalRandom.current().nextDouble() <= reward.getChance()) {
                giveReward(player, reward);
                givenRewards.add(reward);
            }
        }
        
        // Update player statistics
        updatePlayerStats(player, dungeonType, givenRewards);
        
        // Send reward summary
        sendRewardSummary(player, givenRewards);
        
        plugin.getLogger().info("Gave " + givenRewards.size() + " rewards to " + player.getName() + 
                               " for completing " + dungeonType);
    }
    
    private void giveReward(Player player, Reward reward) {
        switch (reward.getType()) {
            case EXPERIENCE:
                int exp = (Integer) reward.getValue();
                player.giveExp(exp);
                break;
                
            case ITEM:
                ItemStack item = (ItemStack) reward.getValue();
                if (player.getInventory().firstEmpty() != -1) {
                    player.getInventory().addItem(item);
                } else {
                    player.getWorld().dropItemNaturally(player.getLocation(), item);
                    player.sendMessage(colorize("&7Your inventory is full! Item dropped on the ground."));
                }
                break;
                
            case CURRENCY:
                int amount = (Integer) reward.getValue();
                // This would integrate with an economy plugin like Vault
                // For now, just notify the player
                player.sendMessage(colorize("&a+$" + amount + " added to your account!"));
                break;
        }
    }
    
    private void updatePlayerStats(Player player, String dungeonType, List<Reward> rewards) {
        UUID playerId = player.getUniqueId();
        PlayerRewardData data = playerData.computeIfAbsent(playerId, k -> new PlayerRewardData());
        
        data.incrementDungeonsCompleted(dungeonType);
        data.addRewards(rewards.size());
        
        // Calculate total experience gained
        int totalExp = rewards.stream()
            .filter(r -> r.getType() == RewardType.EXPERIENCE)
            .mapToInt(r -> (Integer) r.getValue())
            .sum();
        data.addExperience(totalExp);
        
        // Calculate total currency gained
        int totalCurrency = rewards.stream()
            .filter(r -> r.getType() == RewardType.CURRENCY)
            .mapToInt(r -> (Integer) r.getValue())
            .sum();
        data.addCurrency(totalCurrency);
    }
    
    private void sendRewardSummary(Player player, List<Reward> rewards) {
        if (rewards.isEmpty()) {
            player.sendMessage(colorize("&cNo rewards received this time. Better luck next time!"));
            return;
        }
        
        player.sendMessage(colorize("&6=== Dungeon Rewards ==="));
        
        for (Reward reward : rewards) {
            switch (reward.getType()) {
                case EXPERIENCE:
                    player.sendMessage(colorize("&a+ " + reward.getValue() + " Experience"));
                    break;
                case ITEM:
                    ItemStack item = (ItemStack) reward.getValue();
                    String itemName = item.hasItemMeta() && item.getItemMeta().hasDisplayName() 
                        ? item.getItemMeta().getDisplayName() 
                        : formatItemName(item.getType().name());
                    player.sendMessage(colorize("&a+ " + item.getAmount() + "x " + itemName));
                    break;
                case CURRENCY:
                    player.sendMessage(colorize("&a+ $" + reward.getValue()));
                    break;
            }
        }
        
        player.sendMessage(colorize("&6====================="));
    }
    
    public void showPlayerStats(Player player) {
        UUID playerId = player.getUniqueId();
        PlayerRewardData data = playerData.get(playerId);
        
        if (data == null) {
            player.sendMessage(colorize("&cNo dungeon statistics found."));
            return;
        }
        
        player.sendMessage(colorize("&6=== Your Dungeon Statistics ==="));
        player.sendMessage(colorize("&7Total Dungeons Completed: &a" + data.getTotalDungeonsCompleted()));
        player.sendMessage(colorize("&7Total Rewards Received: &a" + data.getTotalRewards()));
        player.sendMessage(colorize("&7Total Experience Gained: &a" + data.getTotalExperience()));
        player.sendMessage(colorize("&7Total Currency Earned: &a$" + data.getTotalCurrency()));
        
        if (!data.getDungeonCompletions().isEmpty()) {
            player.sendMessage(colorize("&7Completions by Dungeon:"));
            for (Map.Entry<String, Integer> entry : data.getDungeonCompletions().entrySet()) {
                player.sendMessage(colorize("&8  - " + entry.getKey() + ": &a" + entry.getValue()));
            }
        }
    }
    
    public void showAvailableRewards(Player player, String dungeonType) {
        List<Reward> rewards = dungeonRewards.get(dungeonType);
        if (rewards == null) {
            rewards = dungeonRewards.get("default");
        }
        
        if (rewards == null || rewards.isEmpty()) {
            player.sendMessage(colorize("&cNo rewards configured for this dungeon."));
            return;
        }
        
        player.sendMessage(colorize("&6=== Possible Rewards for " + dungeonType + " ==="));
        
        for (Reward reward : rewards) {
            String chanceStr = String.format("%.1f%%", reward.getChance() * 100);
            
            switch (reward.getType()) {
                case EXPERIENCE:
                    player.sendMessage(colorize("&7" + reward.getValue() + " Experience &8(" + chanceStr + ")"));
                    break;
                case ITEM:
                    ItemStack item = (ItemStack) reward.getValue();
                    String itemName = formatItemName(item.getType().name());
                    player.sendMessage(colorize("&7" + item.getAmount() + "x " + itemName + " &8(" + chanceStr + ")"));
                    break;
                case CURRENCY:
                    player.sendMessage(colorize("&7$" + reward.getValue() + " &8(" + chanceStr + ")"));
                    break;
            }
        }
    }
    
    private ItemStack createRewardItem(Material material, int amount) {
        ItemStack item = new ItemStack(material, amount);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(colorize("&6Dungeon Reward"));
            meta.setLore(Arrays.asList(
                colorize("&7Obtained from completing"),
                colorize("&7an Ultimate Dungeon!")
            ));
            item.setItemMeta(meta);
        }
        return item;
    }
    
    private String formatItemName(String materialName) {
        return Arrays.stream(materialName.split("_"))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
            .reduce((a, b) -> a + " " + b)
            .orElse(materialName);
    }
    
    private String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public void shutdown() {
        // Save player data if needed
        playerData.clear();
    }
    
    // Inner classes
    public enum RewardType {
        EXPERIENCE, ITEM, CURRENCY
    }
    
    public static class Reward {
        private final RewardType type;
        private final Object value;
        private final double chance;
        
        public Reward(RewardType type, Object value, double chance) {
            this.type = type;
            this.value = value;
            this.chance = Math.max(0.0, Math.min(1.0, chance)); // Clamp between 0 and 1
        }
        
        public RewardType getType() { return type; }
        public Object getValue() { return value; }
        public double getChance() { return chance; }
    }
    
    public static class PlayerRewardData {
        private final Map<String, Integer> dungeonCompletions;
        private int totalRewards;
        private int totalExperience;
        private int totalCurrency;
        
        public PlayerRewardData() {
            this.dungeonCompletions = new HashMap<>();
            this.totalRewards = 0;
            this.totalExperience = 0;
            this.totalCurrency = 0;
        }
        
        public void incrementDungeonsCompleted(String dungeonType) {
            dungeonCompletions.merge(dungeonType, 1, Integer::sum);
        }
        
        public void addRewards(int count) {
            totalRewards += count;
        }
        
        public void addExperience(int exp) {
            totalExperience += exp;
        }
        
        public void addCurrency(int currency) {
            totalCurrency += currency;
        }
        
        public Map<String, Integer> getDungeonCompletions() {
            return new HashMap<>(dungeonCompletions);
        }
        
        public int getTotalDungeonsCompleted() {
            return dungeonCompletions.values().stream().mapToInt(Integer::intValue).sum();
        }
        
        public int getTotalRewards() { return totalRewards; }
        public int getTotalExperience() { return totalExperience; }
        public int getTotalCurrency() { return totalCurrency; }
    }
}
