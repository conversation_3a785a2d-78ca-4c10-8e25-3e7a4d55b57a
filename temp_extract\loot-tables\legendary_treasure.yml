# UltimateDungeons Legendary Treasure Loot Table
# The most valuable rewards for completing the hardest dungeons

name: "Legendary Treasure"
type: "legendary"
rarity: "mythic"
description: "The rarest treasures found only in the deepest dungeons"

# Loot pools with different rarities and chances
loot_pools:
  
  # Ultra Rare Items (1% chance)
  ultra_rare:
    chance: 0.01
    min_items: 1
    max_items: 1
    items:
      - item: "NETHERITE_SWORD"
        enchantments:
          - "SHARPNESS:10"
          - "UNBREAKING:5"
          - "MENDING:1"
          - "FIRE_ASPECT:3"
          - "LOOTING:5"
        custom_name: "&6&l⚔ Dragonslayer Blade ⚔"
        lore:
          - "&7A legendary sword forged in the"
          - "&7depths of the Nether by ancient"
          - "&7dragon smiths. Its blade burns"
          - "&7with eternal fire."
          - ""
          - "&c+15 Attack Damage"
          - "&c+20% Critical Strike Chance"
          - "&6Legendary Weapon"
        weight: 1
      
      - item: "NETHERITE_HELMET"
        enchantments:
          - "PROTECTION:6"
          - "UNBREAKING:5"
          - "MENDING:1"
          - "RESPIRATION:5"
          - "AQUA_AFFINITY:1"
        custom_name: "&6&lCrown of the Dungeon Lord"
        lore:
          - "&7Once worn by the greatest dungeon"
          - "&7masters, this crown grants its"
          - "&7wearer dominion over all monsters."
          - ""
          - "&9+12 Armor"
          - "&9+25% Magic Resistance"
          - "&6Legendary Armor"
        weight: 1
      
      - item: "ELYTRA"
        enchantments:
          - "UNBREAKING:10"
          - "MENDING:1"
        custom_name: "&6&lWings of the Ancient Dragon"
        lore:
          - "&7These mystical wings once belonged"
          - "&7to an elder dragon. They grant"
          - "&7unlimited flight to the worthy."
          - ""
          - "&b+Infinite Flight Duration"
          - "&b+50% Flight Speed"
          - "&6Legendary Accessory"
        weight: 1

  # Legendary Items (5% chance)
  legendary:
    chance: 0.05
    min_items: 1
    max_items: 2
    items:
      - item: "DIAMOND_SWORD"
        enchantments:
          - "SHARPNESS:7"
          - "UNBREAKING:4"
          - "FIRE_ASPECT:2"
          - "LOOTING:4"
        custom_name: "&5&lBlade of Eternal Flame"
        lore:
          - "&7A masterwork blade that burns"
          - "&7with magical fire."
          - ""
          - "&c+12 Attack Damage"
          - "&5Epic Weapon"
        weight: 3
      
      - item: "DIAMOND_PICKAXE"
        enchantments:
          - "EFFICIENCY:8"
          - "UNBREAKING:4"
          - "FORTUNE:5"
          - "MENDING:1"
        custom_name: "&5&lMiner's Dream"
        lore:
          - "&7This pickaxe can break through"
          - "&7any material with ease."
          - ""
          - "&a+300% Mining Speed"
          - "&a+500% Ore Drops"
          - "&5Epic Tool"
        weight: 2
      
      - item: "DIAMOND_CHESTPLATE"
        enchantments:
          - "PROTECTION:5"
          - "UNBREAKING:4"
          - "THORNS:3"
        custom_name: "&5&lPlate of the Guardian"
        lore:
          - "&7Forged by master smiths, this"
          - "&7armor provides ultimate protection."
          - ""
          - "&9+8 Armor"
          - "&9Reflects 50% Damage"
          - "&5Epic Armor"
        weight: 2

  # Epic Items (15% chance)
  epic:
    chance: 0.15
    min_items: 1
    max_items: 3
    items:
      - item: "IRON_SWORD"
        enchantments:
          - "SHARPNESS:5"
          - "UNBREAKING:3"
          - "LOOTING:3"
        custom_name: "&d&lDungeon Cleaver"
        lore:
          - "&7A reliable blade for dungeon"
          - "&7exploration."
          - ""
          - "&c+8 Attack Damage"
          - "&dRare Weapon"
        weight: 5
      
      - item: "BOW"
        enchantments:
          - "POWER:5"
          - "UNBREAKING:3"
          - "INFINITY:1"
          - "FLAME:1"
        custom_name: "&d&lHunter's Longbow"
        lore:
          - "&7Perfect for taking down monsters"
          - "&7from a safe distance."
          - ""
          - "&c+150% Arrow Damage"
          - "&cInfinite Arrows"
          - "&dRare Weapon"
        weight: 4
      
      - item: "GOLDEN_APPLE"
        amount: 3
        custom_name: "&d&lDungeon Apple"
        lore:
          - "&7A magical apple that restores"
          - "&7health and grants temporary buffs."
          - ""
          - "&a+Regeneration II (30s)"
          - "&a+Absorption II (2 min)"
          - "&dRare Consumable"
        weight: 6

  # Rare Items (30% chance)
  rare:
    chance: 0.30
    min_items: 2
    max_items: 4
    items:
      - item: "EMERALD"
        amount: 16
        custom_name: "&a&lDungeon Emeralds"
        lore:
          - "&7Valuable gems found deep"
          - "&7within dungeon treasuries."
        weight: 8
      
      - item: "DIAMOND"
        amount: 8
        custom_name: "&b&lDungeon Diamonds"
        lore:
          - "&7Precious diamonds mined from"
          - "&7the deepest dungeon chambers."
        weight: 6
      
      - item: "EXPERIENCE_BOTTLE"
        amount: 32
        custom_name: "&e&lBottled Dungeon Experience"
        lore:
          - "&7The concentrated essence of"
          - "&7defeated dungeon monsters."
        weight: 10
      
      - item: "ENCHANTED_BOOK"
        enchantments:
          - "SHARPNESS:4"
        custom_name: "&9&lTome of Sharpness"
        lore:
          - "&7An ancient tome containing"
          - "&7weapon enhancement knowledge."
        weight: 5

  # Common Items (49% chance)
  common:
    chance: 0.49
    min_items: 3
    max_items: 6
    items:
      - item: "GOLD_INGOT"
        amount: 12
        weight: 15
      
      - item: "IRON_INGOT"
        amount: 16
        weight: 20
      
      - item: "COOKED_BEEF"
        amount: 8
        weight: 12
      
      - item: "BREAD"
        amount: 16
        weight: 18
      
      - item: "ARROW"
        amount: 64
        weight: 10
      
      - item: "TORCH"
        amount: 32
        weight: 25
      
      - item: "POTION"
        potion_type: "HEALING"
        amount: 4
        weight: 8
      
      - item: "ENDER_PEARL"
        amount: 2
        weight: 5

# Special conditions for loot generation
conditions:
  difficulty_scaling:
    enabled: true
    multipliers:
      easy: 0.8
      normal: 1.0
      hard: 1.3
      nightmare: 1.8
      legendary: 2.5
  
  party_size_bonus:
    enabled: true
    bonus_per_player: 0.15
    max_bonus: 1.0
  
  completion_time_bonus:
    enabled: true
    time_thresholds:
      under_10_minutes: 1.5
      under_20_minutes: 1.2
      under_30_minutes: 1.1
      over_60_minutes: 0.9
  
  perfect_run_bonus:
    enabled: true
    conditions:
      - "no_player_deaths"
      - "all_secrets_found"
      - "all_monsters_killed"
    bonus_multiplier: 2.0

# Currency rewards
currency:
  coins:
    min: 1000
    max: 5000
    base_amount: 2500
  
  dungeon_tokens:
    min: 10
    max: 50
    base_amount: 25
  
  experience:
    min: 500
    max: 2000
    base_amount: 1000

# Achievement unlocks
achievements:
  - "legendary_treasure_hunter"
  - "dungeon_master"
  - "treasure_collector"

# Statistics tracking
statistics:
  track_items_found: true
  track_total_value: true
  track_rarity_distribution: true
  leaderboard_eligible: true

# Anti-exploit measures
anti_exploit:
  cooldown_per_player: 3600  # 1 hour
  max_daily_claims: 5
  require_full_completion: true
  prevent_chest_farming: true

# Custom events
events:
  on_loot_generated:
    - "broadcast: &6{player} &ehas found legendary treasure!"
    - "sound: UI_TOAST_CHALLENGE_COMPLETE"
    - "fireworks: 5"
  
  on_ultra_rare_found:
    - "broadcast: &6&l{player} &e&lhas discovered an ULTRA RARE item!"
    - "sound: ENTITY_ENDER_DRAGON_DEATH"
    - "title: &6&lULTRA RARE DISCOVERY!"
    - "subtitle: &e{item_name}"

# Metadata
metadata:
  version: "1.0.0"
  author: "UltimateDungeons Team"
  created: "2024-01-01"
  last_modified: "2024-01-01"
  total_possible_items: 847
  average_value: "2,500 coins"
  rarity_distribution:
    ultra_rare: "1%"
    legendary: "5%"
    epic: "15%"
    rare: "30%"
    common: "49%"
