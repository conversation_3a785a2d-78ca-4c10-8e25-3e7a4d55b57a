# ===============================================================================
# UltimateDungeons Configuration File
# The ultimate dungeon plugin combining ApexDungeons and MythicDungeons features
# ===============================================================================

# General plugin settings
settings:
  # Plugin language (en, es, fr, de, etc.)
  language: "en"
  
  # Check for plugin updates on startup
  check-updates: true
  
  # Debug mode for troubleshooting
  debug: false
  
  # Metrics and statistics collection
  metrics: true

# World settings
worlds:
  # List of worlds where dungeons can be created
  # Leave empty to allow all worlds
  allowed-worlds:
    - world
    - world_nether
    - world_the_end
    - dungeons
  
  # Default world for dungeon instances
  default-world: "world"
  
  # Create separate world for each dungeon instance
  separate-worlds: true
  
  # World generation settings for dungeon worlds
  world-generation:
    generator: "VOID"  # VOID, FLAT, NORMAL
    environment: "NORMAL"  # NORMAL, NETHER, THE_END
    
# Dungeon generation settings
generation:
  # Maximum number of rooms per dungeon
  max-rooms: 25
  
  # Minimum number of rooms per dungeon
  min-rooms: 5
  
  # Chance for creating branches (0.0 - 1.0)
  branch-chance: 0.3
  
  # Maximum depth of branches
  max-branch-depth: 3
  
  # Room spacing (blocks between rooms)
  room-spacing: 5
  
  # Maximum active dungeons at once
  max-active-dungeons: 10
  
  # Dungeon timeout (minutes)
  dungeon-timeout: 60

# Performance settings
performance:
  # Blocks placed per tick during generation
  blocks-per-tick: 8000
  
  # Chunk preload radius around dungeons
  chunk-preload-radius: 2
  
  # Maximum concurrent generations
  max-concurrent-generations: 3
  
  # Use async generation when possible
  async-generation: true
  
  # Cache generated rooms
  cache-rooms: true
  
  # Maximum cached rooms
  max-cached-rooms: 50

# Party system settings
party:
  # Maximum party size
  max-size: 6
  
  # Minimum party size for certain dungeons
  min-size: 1
  
  # Party invitation timeout (seconds)
  invitation-timeout: 30
  
  # Auto-disband empty parties
  auto-disband: true
  
  # Party chat prefix
  chat-prefix: "&6[Party] &f"
  
  # Cross-server party support (requires proxy setup)
  cross-server: false

# Instance management
instances:
  # Maximum instances per player
  max-per-player: 3
  
  # Instance cleanup interval (minutes)
  cleanup-interval: 5
  
  # Save instance data
  save-data: true
  
  # Instance data save interval (minutes)
  save-interval: 10

# GUI settings
gui:
  # GUI update interval (ticks)
  update-interval: 20
  
  # Use custom textures
  custom-textures: true
  
  # GUI sound effects
  sounds: true
  
  # Animation effects
  animations: true
  
  # Items per page in lists
  items-per-page: 45

# Loot and rewards
loot:
  # Default loot tables
  default-tables:
    - "basic"
    - "rare"
  
  # Loot scaling with difficulty
  difficulty-scaling: true
  
  # Bonus loot for parties
  party-bonus: 1.2
  
  # Loot distribution method (EQUAL, RANDOM, CONTRIBUTION)
  distribution: "EQUAL"

# Mob settings
mobs:
  # Mob adapter (AUTO, MYTHICMOBS, VANILLA)
  adapter: "AUTO"
  
  # Mob scaling with party size
  party-scaling: true
  
  # Base mob health multiplier
  health-multiplier: 1.0
  
  # Base mob damage multiplier
  damage-multiplier: 1.0
  
  # Mob spawn delay (ticks)
  spawn-delay: 20

# Boss settings
bosses:
  # Default boss for dungeons without specified boss
  default-boss: "wither_basic"
  
  # Boss health scaling
  health-scaling: true
  
  # Boss damage scaling
  damage-scaling: true
  
  # Boss announcement
  announce-spawn: true
  
  # Boss death effects
  death-effects: true

# Integration settings
integrations:
  # MythicMobs integration
  mythicmobs:
    enabled: true
    use-levels: true
    use-skills: true
  
  # PlaceholderAPI integration
  placeholderapi:
    enabled: true
  
  # Vault integration (economy/permissions)
  vault:
    enabled: true
    use-economy: true
    use-permissions: true
  
  # Citizens integration
  citizens:
    enabled: true
    dungeon-npcs: true
  
  # WorldEdit integration
  worldedit:
    enabled: true
    use-schematics: true
  
  # ModelEngine integration
  modelengine:
    enabled: true

# Database settings (for advanced features)
database:
  # Database type (SQLITE, MYSQL, POSTGRESQL)
  type: "SQLITE"
  
  # Connection settings (for MySQL/PostgreSQL)
  host: "localhost"
  port: 3306
  database: "ultimatedungeons"
  username: "root"
  password: ""
  
  # Connection pool settings
  pool-size: 10
  connection-timeout: 30000

# Advanced settings
advanced:
  # Custom trigger cooldowns
  trigger-cooldowns: true
  
  # Variable persistence
  persistent-variables: true
  
  # Cross-dungeon communication
  cross-dungeon-signals: true
  
  # Advanced pathfinding
  advanced-pathfinding: true
  
  # Custom block physics
  custom-physics: false
