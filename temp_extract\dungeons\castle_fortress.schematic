# UltimateDungeons Castle Fortress Schematic
# This is a massive castle dungeon with multiple floors and rooms
# Generated by UltimateDungeons Schematic System v1.0.0

version: 2
name: "Castle Fortress"
author: "UltimateDungeons Team"
description: "A massive medieval castle with towers, dungeons, and treasure rooms"
size:
  width: 128
  height: 64
  length: 128

# Block data for the castle structure
blocks:
  # Foundation layer (Y=0-5)
  foundation:
    material: STONE_BRICKS
    pattern: solid
    decorations:
      - MOSSY_STONE_BRICKS: 15%
      - CRACKED_STONE_BRICKS: 10%
  
  # Walls (Y=6-32)
  walls:
    material: STONE_BRICKS
    thickness: 3
    height: 26
    towers:
      - position: [0, 0]
        height: 40
        radius: 8
      - position: [120, 0]
        height: 40
        radius: 8
      - position: [0, 120]
        height: 40
        radius: 8
      - position: [120, 120]
        height: 40
        radius: 8
  
  # Interior rooms
  rooms:
    great_hall:
      position: [32, 6, 32]
      size: [64, 16, 32]
      ceiling: DARK_OAK_PLANKS
      floor: POLISHED_ANDESITE
      pillars:
        material: STONE_BRICK_STAIRS
        positions: [[40, 6, 40], [56, 6, 40], [72, 6, 40], [88, 6, 40]]
    
    throne_room:
      position: [48, 6, 16]
      size: [32, 12, 16]
      throne:
        position: [64, 6, 24]
        material: QUARTZ_STAIRS
        decorations: [GOLD_BLOCK, RED_CARPET]
    
    armory:
      position: [16, 6, 80]
      size: [24, 8, 24]
      weapon_racks:
        - [20, 7, 84]
        - [20, 7, 88]
        - [20, 7, 92]
        - [20, 7, 96]
      armor_stands:
        - [24, 6, 86]
        - [24, 6, 90]
        - [24, 6, 94]
    
    treasury:
      position: [88, 6, 80]
      size: [24, 8, 24]
      vault_door:
        position: [100, 6, 80]
        material: IRON_DOOR
        lock_mechanism: REDSTONE_CIRCUIT
      treasure_chests:
        - position: [92, 6, 84]
          loot_table: "castle_treasure_rare"
        - position: [96, 6, 84]
          loot_table: "castle_treasure_common"
        - position: [100, 6, 84]
          loot_table: "castle_treasure_epic"
    
    dungeon_cells:
      position: [16, -8, 16]
      size: [32, 6, 32]
      cells:
        - [20, -8, 20]: IRON_BARS
        - [20, -8, 28]: IRON_BARS
        - [28, -8, 20]: IRON_BARS
        - [28, -8, 28]: IRON_BARS
      torture_chamber:
        position: [24, -8, 24]
        decorations: [ANVIL, CAULDRON, SKELETON_SKULL]

# Mob spawners
spawners:
  - position: [64, 7, 64]
    type: SKELETON
    count: 4
    difficulty_scaling: true
  
  - position: [32, 7, 32]
    type: ZOMBIE
    count: 6
    difficulty_scaling: true
  
  - position: [96, 7, 96]
    type: SPIDER
    count: 3
    difficulty_scaling: true
  
  - position: [64, 22, 64]
    type: WITHER_SKELETON
    count: 2
    difficulty_scaling: true
    boss: true

# Loot chests
loot_chests:
  - position: [92, 6, 84]
    loot_table: "castle_treasure_rare"
    respawn_time: 3600
  
  - position: [96, 6, 84]
    loot_table: "castle_treasure_common"
    respawn_time: 1800
  
  - position: [100, 6, 84]
    loot_table: "castle_treasure_epic"
    respawn_time: 7200
  
  - position: [40, 7, 40]
    loot_table: "castle_supplies"
    respawn_time: 900

# Objectives
objectives:
  primary:
    - type: "kill_boss"
      target: "Castle Lord"
      description: "Defeat the Castle Lord in the throne room"
      reward: "castle_completion_reward"
  
  secondary:
    - type: "collect_keys"
      count: 4
      description: "Collect all 4 tower keys"
      reward: "tower_master_reward"
    
    - type: "clear_dungeon"
      description: "Clear all monsters from the dungeon cells"
      reward: "dungeon_cleaner_reward"

# Triggers and events
triggers:
  entrance:
    type: "player_enter"
    position: [64, 6, 0]
    radius: 5
    actions:
      - "message: Welcome to Castle Fortress!"
      - "sound: BLOCK_BELL_USE"
      - "spawn_mobs: entrance_guards"
  
  throne_room_enter:
    type: "player_enter"
    position: [64, 6, 24]
    radius: 8
    actions:
      - "message: You dare enter the throne room?"
      - "spawn_boss: castle_lord"
      - "lock_doors: throne_room"
  
  treasury_unlock:
    type: "key_use"
    position: [100, 6, 80]
    required_keys: ["tower_key_north", "tower_key_south", "tower_key_east", "tower_key_west"]
    actions:
      - "open_door: treasury_vault"
      - "message: The treasury vault opens with a heavy grinding sound..."
      - "sound: BLOCK_PISTON_EXTEND"

# Environmental effects
effects:
  ambient_sounds:
    - sound: "AMBIENT_CAVE"
      volume: 0.3
      pitch: 0.8
      interval: 30
  
  particle_effects:
    - type: "SMOKE_NORMAL"
      position: [64, 12, 64]
      count: 10
      interval: 5
  
  weather:
    type: "STORM"
    intensity: 0.7
    duration: -1  # Permanent

# Redstone mechanisms
redstone:
  drawbridge:
    position: [64, 6, -8]
    type: "piston_bridge"
    activation: "pressure_plate"
    materials: [OAK_PLANKS, CHAIN]
  
  secret_passage:
    position: [32, 6, 64]
    type: "hidden_door"
    activation: "lever_sequence"
    sequence: ["lever_1", "lever_3", "lever_2", "lever_4"]

# Lighting
lighting:
  torches:
    - [32, 8, 32]
    - [96, 8, 32]
    - [32, 8, 96]
    - [96, 8, 96]
    - [64, 8, 16]
    - [64, 8, 112]
  
  lanterns:
    - [48, 10, 48]
    - [80, 10, 48]
    - [48, 10, 80]
    - [80, 10, 80]
  
  glowstone:
    - [64, 20, 64]  # Central tower top

# Custom decorations
decorations:
  banners:
    - position: [64, 15, 16]
      color: RED
      pattern: "CASTLE_CREST"
  
  item_frames:
    - position: [60, 8, 24]
      item: "DIAMOND_SWORD"
      rotation: 0
    - position: [68, 8, 24]
      item: "DIAMOND_HELMET"
      rotation: 0
  
  paintings:
    - position: [40, 8, 16]
      art: "FIGHTERS"
    - position: [88, 8, 16]
      art: "SKELETON"

# Build instructions
build_order:
  1: "foundation"
  2: "walls"
  3: "towers"
  4: "interior_rooms"
  5: "decorations"
  6: "redstone"
  7: "lighting"
  8: "spawners"
  9: "loot_chests"
  10: "final_touches"

# Metadata
metadata:
  build_time: "45 minutes"
  difficulty: "Hard"
  recommended_players: "3-5"
  estimated_completion: "60-90 minutes"
  loot_quality: "Epic"
  experience_reward: 5000
