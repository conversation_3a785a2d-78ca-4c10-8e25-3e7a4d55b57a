# ===============================================================================
# UltimateDungeons Messages Configuration
# All plugin messages and text displayed to players
# ===============================================================================

# General messages
general:
  prefix: "&6[&eUltimateDungeons&6] &f"
  no-permission: "&cYou don't have permission to use this command!"
  player-only: "&cThis command can only be used by players!"
  invalid-args: "&cInvalid arguments! Use &e{usage}"
  reload-success: "&aPlugin reloaded successfully!"
  reload-failed: "&cFailed to reload plugin! Check console for errors."

# Command messages
commands:
  help:
    header: "&6&l=== UltimateDungeons Help ==="
    main: "&e/ud help &7- Show this help menu"
    create: "&e/ud create <name> &7- Create a new dungeon"
    join: "&e/ud join <dungeon> &7- Join a dungeon"
    leave: "&e/ud leave &7- Leave current dungeon"
    party: "&e/ud party &7- Open party management"
    rewards: "&e/ud rewards &7- View available rewards"
    admin: "&e/udadmin &7- Administrative commands"
    footer: "&6&l=========================="
  
  admin:
    header: "&c&l=== Admin Commands ==="
    reload: "&c/udadmin reload &7- Reload plugin configuration"
    create: "&c/udadmin create <name> <type> &7- Create dungeon template"
    delete: "&c/udadmin delete <name> &7- Delete dungeon template"
    tp: "&c/udadmin tp <dungeon> &7- Teleport to dungeon"
    debug: "&c/udadmin debug &7- Toggle debug mode"
    footer: "&c&l==================="

# Dungeon messages
dungeons:
  not-found: "&cDungeon '{dungeon}' not found!"
  already-in-dungeon: "&cYou are already in a dungeon!"
  not-in-dungeon: "&cYou are not in a dungeon!"
  join-success: "&aSuccessfully joined dungeon '{dungeon}'!"
  leave-success: "&aYou have left the dungeon."
  creation-started: "&aStarting dungeon creation..."
  creation-complete: "&aDungeon '{dungeon}' created successfully!"
  creation-failed: "&cFailed to create dungeon! {reason}"
  generation-progress: "&7Generating dungeon... {progress}%"
  teleporting: "&7Teleporting to dungeon..."
  
  # Dungeon states
  states:
    waiting: "&eWaiting for players..."
    starting: "&aStarting in {time} seconds..."
    active: "&2Active"
    completed: "&aCompleted!"
    failed: "&cFailed!"
  
  # Dungeon completion
  completion:
    success: "&a&l✓ DUNGEON COMPLETED!"
    failure: "&c&l✗ DUNGEON FAILED!"
    time-taken: "&7Time taken: &e{time}"
    rewards-earned: "&7Rewards earned: &a{rewards}"
    experience-gained: "&7Experience: &b+{exp} XP"

# Party messages
party:
  created: "&aParty created successfully!"
  disbanded: "&cParty has been disbanded."
  joined: "&a{player} joined the party!"
  left: "&c{player} left the party."
  kicked: "&c{player} was kicked from the party."
  invite-sent: "&aParty invitation sent to {player}!"
  invite-received: "&aYou've been invited to join {leader}'s party!"
  invite-expired: "&cParty invitation expired."
  invite-accepted: "&aYou joined {leader}'s party!"
  invite-declined: "&cYou declined the party invitation."
  not-in-party: "&cYou are not in a party!"
  already-in-party: "&cYou are already in a party!"
  party-full: "&cThe party is full!"
  not-leader: "&cOnly the party leader can do this!"
  leader-changed: "&a{player} is now the party leader!"
  
  # Party chat
  chat:
    format: "&6[Party] &f{player}: {message}"
    enabled: "&aParty chat enabled!"
    disabled: "&cParty chat disabled!"

# Queue messages
queue:
  joined: "&aJoined queue for '{dungeon}'!"
  left: "&cLeft queue for '{dungeon}'."
  ready: "&aYou are ready! Waiting for other players..."
  not-ready: "&cYou are no longer ready."
  match-found: "&aMatch found! Preparing dungeon..."
  position: "&7Queue position: &e{position}/{total}"
  estimated-time: "&7Estimated wait time: &e{time}"

# GUI messages
gui:
  titles:
    main-menu: "UltimateDungeons"
    dungeon-browser: "Browse Dungeons"
    party-management: "Party Management"
    rewards: "Dungeon Rewards"
    settings: "Settings"
    admin-panel: "Admin Panel"
    dungeon-creation: "Create Dungeon"
    room-selection: "Select Room"
    mob-selection: "Select Mobs"
    loot-configuration: "Configure Loot"
  
  buttons:
    # Navigation
    back: "&7← Back"
    next: "&7Next →"
    previous: "&7← Previous"
    close: "&cClose"
    confirm: "&aConfirm"
    cancel: "&cCancel"
    
    # Actions
    join: "&aJoin Dungeon"
    leave: "&cLeave Dungeon"
    create: "&eCreate New"
    edit: "&6Edit"
    delete: "&cDelete"
    teleport: "&bTeleport"
    
    # Party actions
    invite: "&aInvite Player"
    kick: "&cKick Player"
    promote: "&6Promote to Leader"
    ready: "&aReady Up"
    not-ready: "&cNot Ready"

# Error messages
errors:
  dungeon-not-found: "&cDungeon not found!"
  player-not-found: "&cPlayer not found!"
  world-not-found: "&cWorld not found!"
  insufficient-permissions: "&cInsufficient permissions!"
  command-failed: "&cCommand failed to execute!"
  database-error: "&cDatabase error occurred!"
  generation-error: "&cDungeon generation failed!"
  teleport-failed: "&cTeleportation failed!"
  invalid-location: "&cInvalid location!"
  world-load-failed: "&cFailed to load world!"

# Success messages
success:
  dungeon-created: "&aDungeon created successfully!"
  dungeon-deleted: "&aDungeon deleted successfully!"
  player-teleported: "&aPlayer teleported successfully!"
  settings-saved: "&aSettings saved successfully!"
  data-exported: "&aData exported successfully!"
  backup-created: "&aBackup created successfully!"

# Warning messages
warnings:
  dungeon-will-reset: "&eWarning: This dungeon will reset in {time}!"
  low-health: "&cWarning: Your health is low!"
  party-member-died: "&c{player} has died!"
  boss-spawned: "&4&lBOSS SPAWNED!"
  time-running-out: "&eTime is running out! {time} remaining!"

# Information messages
info:
  dungeon-info: "&7Dungeon: &e{name} &7| Difficulty: &{difficulty-color}{difficulty} &7| Players: &a{players}/{max-players}"
  party-info: "&7Party: &e{leader}'s Party &7| Members: &a{members}/{max-members}"
  player-stats: "&7Level: &e{level} &7| Experience: &b{exp} &7| Dungeons Completed: &a{completed}"
  dungeon-stats: "&7Rooms: &e{rooms} &7| Estimated Time: &a{time} &7| Recommended Level: &6{level}"

# Placeholder messages (for PlaceholderAPI)
placeholders:
  in-dungeon: "Yes"
  not-in-dungeon: "No"
  no-party: "None"
  no-dungeon: "None"

# Integration messages
integrations:
  mythicmobs:
    not-found: "&cMythicMobs integration not available!"
    mob-spawned: "&7Spawned MythicMob: &e{mob}"
  
  vault:
    no-economy: "&cEconomy system not available!"
    insufficient-funds: "&cInsufficient funds! Required: &e${amount}"
    payment-success: "&aPaid &e${amount} &afor dungeon entry!"
  
  citizens:
    npc-created: "&aDungeon NPC created successfully!"
    npc-removed: "&cDungeon NPC removed!"

# Time formats
time:
  seconds: "{time}s"
  minutes: "{time}m"
  hours: "{time}h"
  days: "{time}d"
  format: "{hours}:{minutes}:{seconds}"

# Number formats
numbers:
  thousand: "k"
  million: "M"
  billion: "B"

# Color codes for different elements
colors:
  # Difficulty colors
  easy: "&a"
  normal: "&e"
  hard: "&6"
  expert: "&c"
  master: "&4"
  
  # Rarity colors
  common: "&f"
  uncommon: "&a"
  rare: "&9"
  epic: "&5"
  legendary: "&6"
  mythic: "&c"
  
  # Status colors
  online: "&a"
  offline: "&7"
  busy: "&e"
  away: "&6"
