org\bukkit\material\Cake.class
org\bukkit\potion\PotionEffect.class
org\bukkit\event\EventException.class
org\bukkit\attribute\Attribute.class
org\bukkit\event\vehicle\VehicleEvent.class
org\bukkit\potion\package-info.class
org\bukkit\event\player\PlayerEditBookEvent.class
org\bukkit\packs\ResourcePack.class
org\bukkit\damage\package-info.class
org\bukkit\FluidCollisionMode.class
org\bukkit\block\DoubleChest.class
org\bukkit\conversations\ConversationCanceller.class
org\bukkit\block\data\package-info.class
org\bukkit\entity\Monster.class
org\bukkit\event\player\PlayerItemConsumeEvent.class
org\bukkit\event\world\ChunkEvent.class
org\spigotmc\CustomTimingsHandler.class
org\bukkit\event\player\PlayerBucketFillEvent.class
org\bukkit\Note.class
org\bukkit\material\RedstoneTorch.class
org\bukkit\plugin\messaging\PluginChannelDirection.class
org\bukkit\entity\MushroomCow$Variant.class
org\bukkit\block\data\type\Crafter.class
org\bukkit\conversations\PlayerNamePrompt.class
org\bukkit\entity\EnderSignal.class
org\bukkit\event\entity\package-info.class
org\bukkit\entity\Sniffer.class
org\bukkit\block\data\type\RedstoneRail.class
org\bukkit\help\HelpTopicFactory.class
org\bukkit\block\data\Ageable.class
org\bukkit\entity\Witch.class
org\bukkit\generator\LimitedRegion.class
org\bukkit\event\entity\EntityRegainHealthEvent.class
org\bukkit\command\Command.class
org\bukkit\block\Barrel.class
org\bukkit\entity\Wolf.class
org\bukkit\event\player\PlayerTakeLecternBookEvent.class
org\bukkit\attribute\Attributable.class
org\bukkit\command\CommandException.class
org\bukkit\plugin\InvalidDescriptionException.class
org\bukkit\event\player\PlayerLoginEvent.class
org\bukkit\Material.class
org\bukkit\block\data\type\TNT.class
org\bukkit\material\Redstone.class
org\bukkit\enchantments\package-info.class
org\bukkit\entity\Villager.class
org\bukkit\permissions\PermissionAttachment.class
org\bukkit\event\block\BlockDamageEvent.class
org\bukkit\material\Dispenser.class
org\bukkit\event\player\PlayerAnimationEvent.class
org\bukkit\packs\package-info.class
org\bukkit\plugin\ServicePriority.class
org\bukkit\block\data\type\Campfire.class
org\bukkit\entity\AbstractArrow$PickupStatus.class
org\bukkit\event\entity\EntityKnockbackByEntityEvent.class
org\bukkit\inventory\meta\trim\TrimPattern.class
org\bukkit\block\data\type\Scaffolding.class
org\bukkit\event\block\BlockGrowEvent.class
org\bukkit\util\noise\package-info.class
org\bukkit\event\world\AsyncStructureGenerateEvent$Cause.class
org\bukkit\inventory\meta\tags\ItemTagType$PrimitiveTagType.class
org\bukkit\generator\ChunkGenerator.class
org\bukkit\inventory\meta\CrossbowMeta.class
org\bukkit\entity\Warden$AngerLevel.class
org\bukkit\conversations\BooleanPrompt.class
org\bukkit\entity\BlockDisplay.class
org\bukkit\plugin\messaging\Messenger.class
org\bukkit\ChatColor$1.class
org\bukkit\util\io\BukkitObjectOutputStream.class
org\bukkit\material\Directional.class
org\bukkit\block\Banner.class
org\bukkit\conversations\InactivityConversationCanceller$1.class
org\bukkit\packs\DataPack$Compatibility.class
org\bukkit\Bukkit.class
org\bukkit\event\world\PortalCreateEvent$CreateReason.class
org\spigotmc\package-info.class
org\bukkit\entity\Skeleton.class
org\bukkit\inventory\meta\BannerMeta.class
org\bukkit\ChatColor$13.class
org\bukkit\conversations\RegexPrompt.class
org\bukkit\block\BlastFurnace.class
org\bukkit\Utility.class
org\bukkit\entity\IronGolem.class
org\bukkit\entity\Ocelot.class
org\bukkit\material\Openable.class
org\bukkit\block\PistonMoveReaction.class
org\bukkit\MinecraftExperimental.class
org\bukkit\inventory\meta\MapMeta.class
org\bukkit\block\data\Lightable.class
org\bukkit\block\data\type\TripwireHook.class
org\bukkit\material\WoodenStep.class
org\bukkit\entity\Entity.class
org\bukkit\persistence\PersistentDataContainer.class
org\bukkit\material\Wool.class
org\bukkit\entity\Boat$Type.class
org\bukkit\entity\minecart\ExplosiveMinecart.class
org\bukkit\event\player\PlayerCommandSendEvent.class
org\bukkit\inventory\CreativeCategory.class
org\bukkit\material\Pumpkin.class
org\bukkit\material\MonsterEggs.class
org\bukkit\util\BlockTransformer$TransformationState.class
org\bukkit\event\weather\LightningStrikeEvent$Cause.class
org\bukkit\inventory\StonecutterInventory.class
org\bukkit\conversations\ConversationAbandonedEvent.class
org\bukkit\event\hanging\HangingBreakEvent$RemoveCause.class
org\bukkit\entity\Evoker$Spell.class
org\bukkit\entity\GlowSquid.class
org\bukkit\inventory\meta\BookMeta.class
org\bukkit\entity\LingeringPotion.class
org\bukkit\event\player\PlayerUnleashEntityEvent.class
org\bukkit\entity\ChestedHorse.class
org\bukkit\entity\EnderDragonPart.class
org\bukkit\plugin\java\LibraryLoader.class
org\bukkit\scoreboard\Team$Option.class
org\bukkit\entity\Llama.class
org\bukkit\entity\WitherSkeleton.class
org\bukkit\block\data\type\DaylightDetector.class
org\bukkit\block\data\type\SculkSensor$Phase.class
org\bukkit\UndefinedNullability.class
org\bukkit\event\block\Action.class
org\bukkit\Raid.class
org\bukkit\event\world\package-info.class
org\bukkit\MusicInstrument.class
org\bukkit\entity\Illager.class
org\bukkit\enchantments\EnchantmentTarget$2.class
org\bukkit\entity\WindCharge.class
org\bukkit\Difficulty.class
org\bukkit\block\data\type\Bell$Attachment.class
org\bukkit\entity\Strider.class
org\bukkit\event\player\PlayerSpawnChangeEvent.class
org\bukkit\map\MapCursorCollection.class
org\bukkit\event\player\PlayerAnimationType.class
org\bukkit\event\player\PlayerTeleportEvent.class
org\bukkit\block\data\type\Piston.class
org\bukkit\projectiles\ProjectileSource.class
org\bukkit\damage\DeathMessageType.class
org\bukkit\event\block\BlockShearEntityEvent.class
org\spigotmc\event\entity\EntityDismountEvent.class
org\bukkit\block\data\type\Bed.class
org\bukkit\block\structure\Mirror.class
org\bukkit\block\data\Bisected.class
org\bukkit\event\entity\EntityDamageEvent$DamageModifier.class
org\bukkit\block\data\Openable.class
org\bukkit\event\inventory\BrewingStandFuelEvent.class
org\bukkit\entity\SkeletonHorse.class
org\bukkit\event\entity\ItemSpawnEvent.class
org\bukkit\plugin\AuthorNagException.class
org\bukkit\event\block\BellResonateEvent.class
org\bukkit\map\MapPalette.class
org\bukkit\block\data\type\Barrel.class
org\bukkit\block\data\type\BigDripleaf$Tilt.class
org\bukkit\ChatColor.class
org\bukkit\scheduler\BukkitTask.class
org\bukkit\util\Consumer.class
org\bukkit\inventory\RecipeChoice$ExactChoice.class
org\bukkit\material\Colorable.class
org\bukkit\permissions\PermissibleBase.class
org\bukkit\EntityEffect.class
org\bukkit\event\block\BlockFertilizeEvent.class
org\bukkit\configuration\SectionPathData.class
org\bukkit\GameEvent.class
org\bukkit\entity\PufferFish.class
org\bukkit\Translatable.class
org\bukkit\entity\Slime.class
org\bukkit\structure\StructureManager.class
org\bukkit\entity\TraderLlama.class
org\bukkit\entity\Boss.class
org\bukkit\plugin\messaging\package-info.class
org\bukkit\Registry$SimpleRegistry.class
org\bukkit\event\hanging\HangingBreakByEntityEvent.class
org\bukkit\help\HelpTopic.class
org\bukkit\entity\Entity$Spigot.class
org\bukkit\configuration\file\YamlConfigurationOptions.class
org\bukkit\event\world\AsyncStructureSpawnEvent.class
org\bukkit\World$Spigot.class
org\bukkit\inventory\GrindstoneInventory.class
org\bukkit\ChunkSnapshot.class
org\bukkit\entity\WanderingTrader.class
org\bukkit\event\world\StructureGrowEvent.class
org\bukkit\event\player\AsyncPlayerChatPreviewEvent.class
org\bukkit\block\data\type\TechnicalPiston.class
org\bukkit\event\entity\EntityDropItemEvent.class
org\bukkit\persistence\package-info.class
org\bukkit\scheduler\BukkitScheduler.class
org\bukkit\event\HandlerList.class
org\bukkit\Statistic$Type.class
org\bukkit\inventory\CookingRecipe.class
org\bukkit\event\entity\PigZapEvent.class
org\bukkit\conversations\ConversationPrefix.class
org\bukkit\enchantments\EnchantmentTarget$1.class
org\bukkit\block\data\type\Cocoa.class
org\bukkit\Registry$2.class
org\bukkit\scoreboard\Criteria.class
org\bukkit\block\data\type\Switch.class
org\bukkit\event\entity\EntityChangeBlockEvent.class
org\bukkit\event\entity\HorseJumpEvent.class
org\bukkit\block\BrushableBlock.class
org\bukkit\block\SculkShrieker.class
org\bukkit\command\MultipleCommandAlias.class
org\bukkit\inventory\DecoratedPotInventory.class
org\bukkit\event\player\PlayerFishEvent.class
org\bukkit\event\entity\EntityUnleashEvent$UnleashReason.class
org\bukkit\entity\ZombieVillager.class
org\bukkit\event\player\PlayerBucketFishEvent.class
org\bukkit\entity\Salmon.class
org\bukkit\material\SmoothBrick.class
org\bukkit\permissions\PermissibleBase$RemoveAttachmentRunnable.class
org\bukkit\event\server\ServerEvent.class
org\bukkit\block\Structure.class
org\bukkit\event\player\PlayerEggThrowEvent.class
org\bukkit\entity\Parrot.class
org\bukkit\inventory\meta\ColorableArmorMeta.class
org\bukkit\event\block\InventoryBlockStartEvent.class
org\bukkit\block\data\type\Wall$Height.class
org\bukkit\entity\Silverfish.class
org\bukkit\block\Sign.class
org\bukkit\configuration\serialization\ConfigurationSerialization.class
org\bukkit\inventory\SmokingRecipe.class
org\bukkit\BanEntry.class
org\bukkit\event\player\PlayerArmorStandManipulateEvent.class
org\bukkit\scoreboard\ScoreboardManager.class
org\bukkit\event\entity\EntityTransformEvent$TransformReason.class
org\bukkit\event\player\PlayerToggleFlightEvent.class
org\bukkit\util\io\package-info.class
org\bukkit\configuration\serialization\DelegateDeserialization.class
org\bukkit\material\PressurePlate.class
org\bukkit\entity\Bat.class
org\bukkit\event\entity\SheepRegrowWoolEvent.class
org\bukkit\event\block\BlockCanBuildEvent.class
org\bukkit\entity\Blaze.class
org\bukkit\help\HelpTopicComparator$TopicNameComparator.class
org\bukkit\block\data\type\Wall.class
org\bukkit\event\entity\FireworkExplodeEvent.class
org\bukkit\event\entity\PiglinBarterEvent.class
org\bukkit\util\StringUtil.class
org\bukkit\entity\Guardian.class
org\bukkit\entity\TippedArrow.class
org\bukkit\potion\PotionEffectTypeWrapper.class
org\bukkit\event\entity\EntityShootBowEvent.class
org\bukkit\block\data\Rotatable.class
org\bukkit\material\Leaves.class
org\bukkit\FireworkEffect$Builder.class
org\bukkit\block\data\type\Crafter$Orientation.class
org\bukkit\block\data\type\Tripwire.class
org\bukkit\entity\Shulker.class
org\bukkit\Registry$3.class
org\bukkit\conversations\package-info.class
org\bukkit\event\block\BlockPistonExtendEvent.class
org\bukkit\event\player\PlayerChangedMainHandEvent.class
org\bukkit\event\entity\SpawnerSpawnEvent.class
org\bukkit\NamespacedKey.class
org\bukkit\event\player\PlayerBedEnterEvent$BedEnterResult.class
org\bukkit\block\data\Attachable.class
org\bukkit\command\defaults\TimingsCommand$PasteThread.class
org\bukkit\inventory\Inventory.class
org\bukkit\configuration\MemorySection.class
org\bukkit\command\ConsoleCommandSender.class
org\bukkit\permissions\PermissionAttachmentInfo.class
org\bukkit\entity\Arrow.class
org\bukkit\map\MapPalette$MapColorCache.class
org\bukkit\enchantments\EnchantmentTarget.class
org\bukkit\entity\GlowItemFrame.class
org\bukkit\event\player\PlayerFishEvent$State.class
org\bukkit\entity\ElderGuardian.class
org\bukkit\entity\Giant.class
org\bukkit\metadata\MetadataStore.class
org\bukkit\entity\Sittable.class
org\bukkit\WorldCreator.class
org\bukkit\inventory\InventoryView.class
org\bukkit\util\FileUtil.class
org\bukkit\event\entity\EntityTransformEvent.class
org\bukkit\event\player\PlayerGameModeChangeEvent.class
org\bukkit\ChatColor$22.class
org\bukkit\inventory\recipe\package-info.class
org\bukkit\event\block\BrewingStartEvent.class
org\bukkit\block\HangingSign.class
org\bukkit\entity\LivingEntity.class
org\bukkit\conversations\Prompt.class
org\bukkit\material\RedstoneWire.class
org\bukkit\scoreboard\NameTagVisibility.class
org\bukkit\event\world\AsyncStructureGenerateEvent.class
org\bukkit\loot\LootTable.class
org\bukkit\ChatColor$18.class
org\bukkit\entity\SmallFireball.class
org\bukkit\block\package-info.class
org\bukkit\boss\BarStyle.class
org\bukkit\scoreboard\Criterias.class
org\bukkit\block\BlockState.class
org\bukkit\event\player\PlayerExpCooldownChangeEvent.class
org\bukkit\block\DecoratedPot.class
org\bukkit\event\entity\EntityPotionEffectEvent$Action.class
org\bukkit\entity\Hanging.class
org\bukkit\event\entity\EntityDeathEvent.class
org\bukkit\event\entity\ExpBottleEvent.class
org\bukkit\block\data\type\RedstoneWire$Connection.class
org\bukkit\inventory\BeaconInventory.class
org\bukkit\entity\Horse$Variant.class
org\bukkit\scoreboard\Team.class
org\bukkit\command\CommandMap.class
org\bukkit\event\block\SculkBloomEvent.class
org\bukkit\material\Gate.class
org\bukkit\block\data\type\WallHangingSign.class
org\bukkit\map\MapCursor$Type.class
org\bukkit\util\Transformation.class
org\bukkit\ChatColor$20.class
org\bukkit\enchantments\EnchantmentWrapper.class
org\bukkit\event\entity\ArrowBodyCountChangeEvent.class
org\bukkit\generator\BlockPopulator.class
org\bukkit\event\player\PlayerInteractEntityEvent.class
org\bukkit\event\player\PlayerRiptideEvent.class
org\bukkit\material\Dye.class
org\bukkit\block\Crafter.class
org\bukkit\event\inventory\InventoryClickEvent.class
org\bukkit\event\vehicle\VehicleUpdateEvent.class
org\bukkit\plugin\PluginDescriptionFile.class
org\bukkit\entity\Piglin.class
org\bukkit\ChatColor$17.class
org\bukkit\inventory\meta\BookMeta$Spigot.class
org\bukkit\event\Listener.class
org\bukkit\potion\Potion.class
org\bukkit\boss\BossBar.class
org\bukkit\block\Biome.class
org\bukkit\configuration\file\YamlRepresenter$RepresentConfigurationSerializable.class
org\bukkit\ChatColor$21.class
org\bukkit\configuration\serialization\ConfigurationSerializable.class
org\bukkit\event\entity\EntityExhaustionEvent$ExhaustionReason.class
org\bukkit\entity\AnimalTamer.class
org\bukkit\entity\Sheep.class
org\bukkit\block\sign\package-info.class
org\bukkit\material\Command.class
org\bukkit\inventory\meta\SpawnEggMeta.class
org\bukkit\inventory\EntityEquipment.class
org\bukkit\event\entity\EntityCreatePortalEvent.class
org\bukkit\material\CocoaPlant.class
org\bukkit\entity\ItemFrame.class
org\bukkit\ChatColor$19.class
org\bukkit\entity\ItemDisplay.class
org\bukkit\entity\TextDisplay.class
org\bukkit\entity\Ageable.class
org\bukkit\configuration\file\FileConfigurationOptions.class
org\bukkit\block\data\type\Dispenser.class
org\bukkit\event\entity\ExplosionPrimeEvent.class
org\bukkit\NetherWartsState.class
org\bukkit\event\player\package-info.class
org\bukkit\util\VoxelShape.class
org\bukkit\event\block\package-info.class
org\bukkit\block\data\Rail.class
org\bukkit\event\player\PlayerInteractAtEntityEvent.class
org\bukkit\block\Block.class
org\bukkit\entity\SplashPotion.class
org\bukkit\entity\Vindicator.class
org\bukkit\plugin\PluginLogger.class
org\bukkit\profile\package-info.class
org\bukkit\event\vehicle\VehicleBlockCollisionEvent.class
org\bukkit\event\player\PlayerRecipeBookSettingsChangeEvent.class
org\bukkit\material\Comparator.class
org\bukkit\material\SimpleAttachableMaterialData.class
org\bukkit\entity\ThrowableProjectile.class
org\bukkit\inventory\InventoryView$Property.class
org\bukkit\inventory\meta\LeatherArmorMeta.class
org\bukkit\event\block\BlockBurnEvent.class
org\bukkit\plugin\messaging\ChannelNotRegisteredException.class
org\bukkit\TreeType.class
org\bukkit\util\noise\OctaveGenerator.class
org\bukkit\Server.class
org\bukkit\projectiles\package-info.class
org\bukkit\entity\Allay.class
org\bukkit\inventory\meta\MusicInstrumentMeta.class
org\spigotmc\event\entity\EntityMountEvent.class
org\bukkit\block\data\type\CaveVines.class
org\bukkit\map\MapRenderer.class
org\bukkit\metadata\MetadataStoreBase.class
org\bukkit\material\FlowerPot.class
org\bukkit\Particle.class
org\bukkit\block\data\type\GlassPane.class
org\bukkit\event\entity\EntityKnockbackEvent$KnockbackCause.class
org\bukkit\loot\LootContext$Builder.class
org\bukkit\block\BrewingStand.class
org\bukkit\block\Chest.class
org\bukkit\block\data\type\TrialSpawner.class
org\bukkit\inventory\MainHand.class
org\bukkit\block\SculkCatalyst.class
org\bukkit\block\data\Rail$Shape.class
org\bukkit\entity\NPC.class
org\bukkit\block\data\Directional.class
org\bukkit\inventory\DoubleChestInventory.class
org\bukkit\event\inventory\PrepareAnvilEvent.class
org\bukkit\event\package-info.class
org\bukkit\material\Skull.class
org\bukkit\damage\DamageType.class
org\bukkit\event\entity\EntityPortalEnterEvent.class
org\bukkit\event\entity\EntityExhaustionEvent.class
org\bukkit\entity\Phantom.class
org\bukkit\block\Beehive.class
org\bukkit\configuration\file\YamlConstructor$ConstructCustomObject.class
org\bukkit\damage\DamageSource$Builder.class
org\bukkit\event\player\PlayerResourcePackStatusEvent$Status.class
org\bukkit\entity\Warden.class
org\bukkit\event\entity\ItemMergeEvent.class
org\bukkit\event\entity\EntityTargetEvent.class
org\bukkit\conversations\ConversationFactory$NotPlayerMessagePrompt.class
org\bukkit\material\DetectorRail.class
org\bukkit\block\data\type\Leaves.class
org\bukkit\event\entity\EntityResurrectEvent.class
org\bukkit\enchantments\EnchantmentTarget$5.class
org\bukkit\block\data\Bisected$Half.class
org\bukkit\block\data\type\TechnicalPiston$Type.class
org\bukkit\block\banner\PatternType.class
org\bukkit\command\defaults\VersionCommand.class
org\bukkit\event\block\CauldronLevelChangeEvent.class
org\bukkit\util\NumberConversions.class
org\bukkit\entity\Mule.class
org\bukkit\event\world\ChunkPopulateEvent.class
org\bukkit\inventory\meta\ArmorMeta.class
org\bukkit\event\block\BlockFadeEvent.class
org\bukkit\generator\WorldInfo.class
org\bukkit\block\data\type\Comparator.class
org\bukkit\scheduler\package-info.class
org\bukkit\scoreboard\Scoreboard.class
org\bukkit\entity\PigZombie.class
org\bukkit\event\player\PlayerLocaleChangeEvent.class
org\bukkit\event\player\PlayerRegisterChannelEvent.class
org\bukkit\configuration\file\package-info.class
org\bukkit\entity\Animals.class
org\bukkit\event\block\BlockBreakEvent.class
org\bukkit\HeightMap.class
org\bukkit\event\inventory\PrepareInventoryResultEvent.class
org\bukkit\event\player\PlayerDropItemEvent.class
org\bukkit\block\data\type\CalibratedSculkSensor.class
org\bukkit\boss\BarColor.class
org\bukkit\inventory\recipe\CraftingBookCategory.class
org\bukkit\event\inventory\InventoryCloseEvent.class
org\bukkit\entity\Firework.class
org\bukkit\entity\Cat$Type.class
org\bukkit\event\entity\EntityPotionEffectEvent.class
org\bukkit\material\PoweredRail.class
org\bukkit\enchantments\EnchantmentTarget$3.class
org\bukkit\block\data\type\Farmland.class
org\bukkit\entity\Llama$Color.class
org\bukkit\event\server\TabCompleteEvent.class
org\bukkit\help\IndexHelpTopic.class
org\bukkit\event\inventory\InventoryPickupItemEvent.class
org\bukkit\event\player\PlayerEvent.class
org\bukkit\entity\Axolotl.class
org\bukkit\block\data\type\BrewingStand.class
org\bukkit\material\Step.class
org\bukkit\util\StructureSearchResult.class
org\bukkit\block\data\type\Door$Hinge.class
org\bukkit\material\Sandstone.class
org\bukkit\entity\FishHook.class
org\bukkit\entity\Zombie.class
org\bukkit\event\inventory\FurnaceStartSmeltEvent.class
org\bukkit\Effect.class
org\bukkit\generator\BiomeProvider.class
org\bukkit\projectiles\BlockProjectileSource.class
org\bukkit\util\BoundingBox.class
org\bukkit\event\entity\EntityBreedEvent.class
org\bukkit\metadata\MetadataConversionException.class
org\bukkit\BanList$Type.class
org\bukkit\block\banner\Pattern.class
org\bukkit\event\inventory\InventoryInteractEvent.class
org\bukkit\advancement\Advancement.class
org\bukkit\advancement\AdvancementProgress.class
org\bukkit\block\ShulkerBox.class
org\bukkit\inventory\meta\KnowledgeBookMeta.class
org\bukkit\block\data\type\Jigsaw$Orientation.class
org\bukkit\event\player\PlayerExpCooldownChangeEvent$ChangeReason.class
org\bukkit\entity\minecart\CommandMinecart.class
org\bukkit\event\entity\EntityCombustByEntityEvent.class
org\bukkit\event\server\ServerLoadEvent.class
org\bukkit\GameMode.class
org\bukkit\event\inventory\InventoryMoveItemEvent.class
org\bukkit\entity\EntityType.class
org\bukkit\Server$Spigot.class
org\bukkit\material\PressureSensor.class
org\bukkit\World$Environment.class
org\bukkit\util\permissions\CommandPermissions.class
org\bukkit\block\Bell.class
org\bukkit\material\NetherWarts.class
org\bukkit\plugin\java\JavaPlugin.class
org\bukkit\entity\minecart\RideableMinecart.class
org\bukkit\configuration\file\YamlRepresenter.class
org\bukkit\generator\structure\GeneratedStructure.class
org\bukkit\potion\PotionEffectType.class
org\bukkit\inventory\meta\PotionMeta.class
org\bukkit\block\data\type\SmallDripleaf.class
org\bukkit\help\GenericCommandHelpTopic.class
org\bukkit\event\inventory\PrepareSmithingEvent.class
org\spigotmc\event\player\package-info.class
org\bukkit\block\data\type\Chain.class
org\bukkit\block\data\type\Light.class
org\bukkit\event\entity\CreatureSpawnEvent.class
org\bukkit\event\server\PluginEnableEvent.class
org\bukkit\event\player\AsyncPlayerChatEvent.class
org\bukkit\metadata\MetadataValue.class
org\bukkit\entity\FallingBlock.class
org\bukkit\inventory\ItemStack.class
org\bukkit\event\block\BlockReceiveGameEvent.class
org\bukkit\entity\Rabbit$Type.class
org\bukkit\event\entity\EntityCombustEvent.class
org\bukkit\inventory\meta\EnchantmentStorageMeta.class
org\bukkit\event\raid\RaidSpawnWaveEvent.class
org\bukkit\Nameable.class
org\bukkit\event\block\BlockPistonEvent.class
org\bukkit\inventory\AbstractHorseInventory.class
org\bukkit\material\ExtendedRails.class
org\bukkit\enchantments\EnchantmentTarget$7.class
org\bukkit\inventory\MerchantInventory.class
org\bukkit\plugin\messaging\MessageTooLargeException.class
org\bukkit\entity\ShulkerBullet.class
org\bukkit\event\player\PlayerPreLoginEvent$Result.class
org\bukkit\inventory\Recipe.class
org\bukkit\entity\MagmaCube.class
org\bukkit\Vibration$Destination.class
org\bukkit\inventory\FurnaceRecipe.class
org\bukkit\entity\Drowned.class
org\bukkit\entity\Endermite.class
org\bukkit\event\entity\AreaEffectCloudApplyEvent.class
org\bukkit\inventory\SmithingRecipe.class
org\bukkit\plugin\ServicesManager.class
org\bukkit\entity\Cow.class
org\bukkit\event\entity\EntityRegainHealthEvent$RegainReason.class
org\bukkit\entity\AbstractArrow.class
org\bukkit\entity\Sniffer$State.class
org\bukkit\conversations\NullConversationPrefix.class
org\bukkit\entity\EntitySnapshot.class
org\bukkit\Tag.class
org\bukkit\block\structure\UsageMode.class
org\bukkit\util\noise\SimplexNoiseGenerator.class
org\bukkit\WorldBorder.class
org\bukkit\Instrument.class
org\bukkit\block\EnderChest.class
org\bukkit\block\data\type\Sapling.class
org\bukkit\command\defaults\package-info.class
org\spigotmc\event\player\PlayerSpawnLocationEvent.class
org\bukkit\command\SimpleCommandMap.class
org\bukkit\event\player\PlayerBucketEvent.class
org\bukkit\event\server\RemoteServerCommandEvent.class
org\bukkit\block\data\type\Furnace.class
org\bukkit\block\sign\Side.class
org\bukkit\block\EndGateway.class
org\bukkit\conversations\ValidatingPrompt.class
org\bukkit\event\hanging\HangingBreakEvent.class
org\bukkit\event\server\PluginDisableEvent.class
org\bukkit\inventory\ItemCraftResult.class
org\bukkit\block\data\type\Comparator$Mode.class
org\bukkit\inventory\RecipeChoice.class
org\bukkit\entity\FishHook$HookState.class
org\bukkit\event\inventory\InventoryDragEvent.class
org\bukkit\event\inventory\SmithItemEvent.class
org\bukkit\entity\LightningStrike.class
org\bukkit\inventory\meta\AxolotlBucketMeta.class
org\bukkit\entity\Frog.class
org\bukkit\inventory\FurnaceInventory.class
org\bukkit\inventory\meta\BlockStateMeta.class
org\bukkit\enchantments\EnchantmentTarget$6.class
org\bukkit\loot\LootTables.class
org\bukkit\entity\Ambient.class
org\bukkit\entity\memory\MemoryKey.class
org\bukkit\inventory\EquipmentSlot.class
org\bukkit\entity\EnderPearl.class
org\bukkit\block\data\type\MangrovePropagule.class
org\bukkit\event\player\PlayerPickupArrowEvent.class
org\bukkit\event\inventory\BrewEvent.class
org\bukkit\material\Observer.class
org\bukkit\block\data\type\Grindstone.class
org\bukkit\block\data\type\PointedDripstone$Thickness.class
org\bukkit\entity\minecart\PoweredMinecart.class
org\bukkit\event\entity\EntityDamageEvent.class
org\bukkit\scheduler\BukkitWorker.class
org\bukkit\event\entity\ItemDespawnEvent.class
org\bukkit\util\BlockTransformer.class
org\bukkit\Warning$WarningState.class
org\bukkit\event\block\BlockDamageAbortEvent.class
org\bukkit\conversations\Conversable.class
org\bukkit\block\data\type\CopperBulb.class
org\bukkit\event\block\BlockPhysicsEvent.class
org\bukkit\entity\Skeleton$SkeletonType.class
org\bukkit\util\BlockIterator.class
org\bukkit\event\player\PlayerQuitEvent.class
org\bukkit\inventory\LlamaInventory.class
org\bukkit\event\entity\EntityRemoveEvent.class
org\bukkit\block\data\type\Bed$Part.class
org\bukkit\event\player\PlayerHarvestBlockEvent.class
org\bukkit\entity\ArmorStand.class
org\bukkit\entity\Stray.class
org\bukkit\material\Banner.class
org\bukkit\material\Bed.class
org\bukkit\persistence\ListPersistentDataTypeProvider$ListPersistentDataTypeImpl.class
org\bukkit\block\CalibratedSculkSensor.class
org\bukkit\util\EntityTransformer.class
org\bukkit\entity\Husk.class
org\bukkit\inventory\meta\trim\package-info.class
org\bukkit\block\data\type\DecoratedPot.class
org\bukkit\configuration\file\YamlConfiguration.class
org\bukkit\entity\Panda.class
org\bukkit\event\world\EntitiesLoadEvent.class
org\bukkit\generator\BiomeParameterPoint.class
org\bukkit\event\server\ServerListPingEvent.class
org\bukkit\block\data\type\StructureBlock.class
org\bukkit\event\player\PlayerJoinEvent.class
org\bukkit\material\Diode.class
org\bukkit\conversations\ConversationAbandonedListener.class
org\bukkit\entity\Tameable.class
org\bukkit\permissions\ServerOperator.class
org\bukkit\entity\Item.class
org\bukkit\event\entity\EntityEnterBlockEvent.class
org\bukkit\block\data\type\HangingSign.class
org\bukkit\event\player\PlayerMoveEvent.class
org\bukkit\inventory\meta\FireworkEffectMeta.class
org\bukkit\structure\Structure.class
org\bukkit\Fluid.class
org\bukkit\boss\KeyedBossBar.class
org\bukkit\CoalType.class
org\bukkit\entity\Ghast.class
org\bukkit\event\block\BlockFormEvent.class
org\bukkit\event\entity\EntityTeleportEvent.class
org\bukkit\inventory\SmithingTransformRecipe.class
org\bukkit\event\player\PlayerHideEntityEvent.class
org\bukkit\entity\minecart\package-info.class
org\bukkit\entity\Parrot$Variant.class
org\bukkit\block\data\type\CoralWallFan.class
org\bukkit\entity\minecart\SpawnerMinecart.class
org\bukkit\entity\Villager$Type.class
org\bukkit\Statistic.class
org\bukkit\event\player\PlayerStatisticIncrementEvent.class
org\bukkit\block\data\type\Stairs.class
org\bukkit\inventory\ShapedRecipe.class
org\bukkit\plugin\messaging\PluginMessageListenerRegistration.class
org\bukkit\entity\SpectralArrow.class
org\bukkit\entity\ComplexEntityPart.class
org\bukkit\block\data\type\Lantern.class
org\bukkit\material\CocoaPlant$CocoaPlantSize.class
org\bukkit\SoundCategory.class
org\bukkit\enchantments\EnchantmentTarget$9.class
org\bukkit\util\package-info.class
org\bukkit\block\data\FaceAttachable.class
org\bukkit\event\entity\EntityToggleGlideEvent.class
org\bukkit\boss\package-info.class
org\bukkit\event\block\LeavesDecayEvent.class
org\bukkit\plugin\messaging\ReservedChannelException.class
org\bukkit\event\inventory\InventoryEvent.class
org\bukkit\event\player\PlayerItemHeldEvent.class
org\bukkit\enchantments\EnchantmentTarget$8.class
org\bukkit\scoreboard\Team$OptionStatus.class
org\bukkit\entity\AreaEffectCloud.class
org\bukkit\event\player\PlayerRecipeDiscoverEvent.class
org\bukkit\block\data\type\package-info.class
org\bukkit\generator\ChunkGenerator$ChunkData.class
org\bukkit\inventory\ItemFlag.class
org\bukkit\block\structure\StructureRotation.class
org\bukkit\event\entity\ProjectileLaunchEvent.class
org\bukkit\event\player\PlayerSignOpenEvent.class
org\bukkit\block\data\FaceAttachable$AttachedFace.class
org\bukkit\permissions\PermissionRemovedExecutor.class
org\bukkit\inventory\ChiseledBookshelfInventory.class
org\bukkit\block\CommandBlock.class
org\bukkit\entity\ComplexLivingEntity.class
org\bukkit\plugin\PluginDescriptionFile$1$1.class
org\bukkit\entity\ArmorStand$LockType.class
org\bukkit\conversations\ManuallyAbandonedConversationCanceller.class
org\bukkit\loot\package-info.class
org\bukkit\event\server\package-info.class
org\bukkit\inventory\BlastingRecipe.class
org\bukkit\WorldType.class
org\bukkit\potion\PotionType.class
org\bukkit\util\EulerAngle.class
org\bukkit\inventory\AnvilInventory.class
org\bukkit\plugin\java\JavaPluginLoader.class
org\bukkit\block\data\type\Hopper.class
org\bukkit\event\block\CauldronLevelChangeEvent$ChangeReason.class
org\bukkit\entity\AbstractVillager.class
org\bukkit\scheduler\BukkitRunnable.class
org\bukkit\util\noise\SimplexOctaveGenerator.class
org\bukkit\entity\Turtle.class
org\bukkit\conversations\ConversationFactory.class
org\bukkit\Effect$Type.class
org\bukkit\block\spawner\SpawnRule.class
org\bukkit\enchantments\EnchantmentTarget$10.class
org\bukkit\map\MapCanvas.class
org\bukkit\entity\TNTPrimed.class
org\bukkit\event\block\BlockDropItemEvent.class
org\bukkit\inventory\CraftingInventory.class
org\bukkit\material\Mushroom.class
org\bukkit\inventory\CraftingRecipe.class
org\bukkit\SoundGroup.class
org\bukkit\event\player\PlayerRespawnEvent.class
org\bukkit\enchantments\EnchantmentTarget$4.class
org\bukkit\entity\Axolotl$Variant.class
org\bukkit\configuration\file\YamlConstructor.class
org\bukkit\plugin\InvalidPluginException.class
org\bukkit\block\data\type\WallSign.class
org\bukkit\entity\ChestBoat.class
org\bukkit\entity\Cod.class
org\bukkit\entity\Display$Billboard.class
org\bukkit\inventory\LecternInventory.class
org\bukkit\block\data\type\TurtleEgg.class
org\bukkit\command\FormattedCommandAlias.class
org\bukkit\entity\Hoglin.class
org\bukkit\ChatColor$8.class
org\bukkit\Keyed.class
org\bukkit\plugin\PluginAwareness.class
org\bukkit\block\data\type\SeaPickle.class
org\bukkit\entity\Cat.class
org\bukkit\event\player\PlayerRespawnEvent$RespawnReason.class
org\bukkit\entity\EntityCategory.class
org\bukkit\event\world\TimeSkipEvent.class
org\bukkit\event\player\PlayerPortalEvent.class
org\bukkit\enchantments\EnchantmentTarget$15.class
org\bukkit\event\block\BlockRedstoneEvent.class
org\bukkit\event\inventory\InventoryType$SlotType.class
org\bukkit\entity\Boat$Status.class
org\bukkit\event\hanging\HangingPlaceEvent.class
org\bukkit\event\raid\RaidEvent.class
org\bukkit\inventory\meta\TropicalFishBucketMeta.class
org\bukkit\inventory\HorseInventory.class
org\bukkit\attribute\AttributeModifier$Operation.class
org\bukkit\block\data\type\Lectern.class
org\bukkit\StructureType.class
org\bukkit\entity\Player.class
org\bukkit\event\block\BlockSpreadEvent.class
org\bukkit\block\data\type\Snow.class
org\bukkit\package-info.class
org\bukkit\entity\Villager$Profession.class
org\bukkit\event\player\PlayerPickupItemEvent.class
org\bukkit\event\block\TNTPrimeEvent$PrimeCause.class
org\bukkit\event\entity\VillagerCareerChangeEvent$ChangeReason.class
org\bukkit\event\player\PlayerKickEvent.class
org\bukkit\inventory\ItemFactory.class
org\bukkit\block\data\Snowable.class
org\bukkit\block\data\type\Dripleaf.class
org\bukkit\packs\DataPack.class
org\bukkit\persistence\PersistentDataAdapterContext.class
org\bukkit\event\entity\BatToggleSleepEvent.class
org\bukkit\command\defaults\HelpCommand.class
org\bukkit\event\player\PlayerBucketEntityEvent.class
org\spigotmc\event\entity\package-info.class
org\bukkit\entity\Ocelot$Type.class
org\bukkit\event\entity\EntityPoseChangeEvent.class
org\bukkit\material\Cauldron.class
org\bukkit\event\entity\FoodLevelChangeEvent.class
org\bukkit\entity\Boat.class
org\bukkit\event\player\PlayerLoginEvent$Result.class
org\bukkit\material\Attachable.class
org\bukkit\inventory\meta\tags\ItemTagAdapterContext.class
org\bukkit\map\MapCursor.class
org\bukkit\block\Jigsaw.class
org\bukkit\event\inventory\FurnaceExtractEvent.class
org\bukkit\configuration\serialization\SerializableAs.class
org\bukkit\event\player\PlayerToggleSneakEvent.class
org\bukkit\event\entity\EnderDragonChangePhaseEvent.class
org\bukkit\entity\Interaction.class
org\bukkit\material\FurnaceAndDispenser.class
org\bukkit\util\ChatPaginator.class
org\bukkit\loot\Lootable.class
org\bukkit\permissions\Permissible.class
org\bukkit\event\player\PlayerUnregisterChannelEvent.class
org\bukkit\block\data\Hangable.class
org\bukkit\block\data\type\AmethystCluster.class
org\bukkit\Location.class
org\bukkit\entity\Trident.class
org\bukkit\help\package-info.class
org\bukkit\inventory\meta\CompassMeta.class
org\bukkit\entity\LeashHitch.class
org\bukkit\event\entity\EntityPickupItemEvent.class
org\bukkit\event\world\GenericGameEvent.class
org\bukkit\attribute\package-info.class
org\bukkit\event\block\BlockEvent.class
org\bukkit\material\Torch.class
org\bukkit\event\weather\WeatherChangeEvent.class
org\bukkit\event\world\TimeSkipEvent$SkipReason.class
org\bukkit\profile\PlayerProfile.class
org\bukkit\event\player\PlayerChannelEvent.class
org\bukkit\persistence\PersistentDataType.class
org\bukkit\configuration\InvalidConfigurationException.class
org\bukkit\event\entity\EntityPortalEvent.class
org\bukkit\metadata\FixedMetadataValue.class
org\bukkit\inventory\meta\trim\TrimMaterial.class
org\bukkit\inventory\SmithingTrimRecipe.class
org\bukkit\event\player\PlayerChatEvent.class
org\bukkit\entity\Goat.class
org\bukkit\entity\Camel.class
org\bukkit\block\data\type\Beehive.class
org\bukkit\block\data\type\GlowLichen.class
org\bukkit\inventory\StonecuttingRecipe.class
org\bukkit\block\EnchantingTable.class
org\bukkit\Vibration$Destination$BlockDestination.class
org\bukkit\conversations\InactivityConversationCanceller.class
org\bukkit\scoreboard\Score.class
org\bukkit\block\data\type\Bell.class
org\bukkit\block\SuspiciousSand.class
org\bukkit\event\entity\PigZombieAngerEvent.class
org\bukkit\scoreboard\RenderType.class
org\bukkit\event\player\PlayerExpChangeEvent.class
org\bukkit\metadata\Metadatable.class
org\bukkit\potion\PotionData.class
org\bukkit\event\entity\EntityBreakDoorEvent.class
org\bukkit\event\entity\EntityTargetLivingEntityEvent.class
org\bukkit\block\data\type\Candle.class
org\bukkit\util\BlockVector.class
org\bukkit\block\data\type\LightningRod.class
org\bukkit\damage\DamageSource.class
org\bukkit\block\Skull.class
org\bukkit\entity\AbstractSkeleton.class
org\bukkit\event\inventory\InventoryAction.class
org\bukkit\event\player\AsyncPlayerPreLoginEvent$Result.class
org\bukkit\event\block\BlockMultiPlaceEvent.class
org\bukkit\block\data\type\Jigsaw.class
org\bukkit\event\entity\EntityTameEvent.class
org\bukkit\block\data\type\Fire.class
org\bukkit\entity\Enemy.class
org\bukkit\generator\structure\StructurePiece.class
org\bukkit\DyeColor.class
org\bukkit\entity\minecart\StorageMinecart.class
org\bukkit\ChatColor$10.class
org\bukkit\entity\SpawnCategory.class
org\bukkit\event\inventory\InventoryCreativeEvent.class
org\bukkit\event\Cancellable.class
org\bukkit\damage\DamageScaling.class
org\bukkit\event\player\PlayerInteractEvent.class
org\bukkit\inventory\BlockInventoryHolder.class
org\bukkit\material\Hopper.class
org\bukkit\Axis.class
org\bukkit\entity\Chicken.class
org\bukkit\event\entity\EntityPotionEffectEvent$Cause.class
org\bukkit\persistence\PersistentDataHolder.class
org\bukkit\SandstoneType.class
org\bukkit\block\Lockable.class
org\bukkit\command\CommandExecutor.class
org\bukkit\command\TabCompleter.class
org\bukkit\entity\LargeFireball.class
org\bukkit\block\Smoker.class
org\bukkit\block\EntityBlockStorage.class
org\bukkit\event\player\PlayerVelocityEvent.class
org\bukkit\block\Dispenser.class
org\bukkit\event\entity\EntityTargetEvent$TargetReason.class
org\bukkit\event\entity\EntityPortalExitEvent.class
org\bukkit\material\Tripwire.class
org\bukkit\entity\Raider.class
org\bukkit\entity\Horse.class
org\bukkit\entity\Ravager.class
org\bukkit\block\Jukebox.class
org\bukkit\entity\PiglinBrute.class
org\bukkit\block\Container.class
org\bukkit\CropState.class
org\bukkit\material\Stairs.class
org\bukkit\Chunk$LoadLevel.class
org\bukkit\entity\Damageable.class
org\bukkit\event\inventory\HopperInventorySearchEvent$ContainerType.class
org\bukkit\configuration\MemoryConfiguration.class
org\bukkit\material\TripwireHook.class
org\bukkit\command\RemoteConsoleCommandSender.class
org\bukkit\event\server\ServerCommandEvent.class
org\bukkit\entity\Snowman.class
org\bukkit\configuration\package-info.class
org\bukkit\block\data\type\BubbleColumn.class
org\bukkit\scoreboard\DisplaySlot.class
org\bukkit\util\noise\NoiseGenerator.class
org\bukkit\event\world\SpawnChangeEvent.class
org\bukkit\plugin\TimedRegisteredListener.class
org\bukkit\block\TrialSpawner.class
org\bukkit\event\Event.class
org\bukkit\entity\ItemDisplay$ItemDisplayTransform.class
org\bukkit\command\defaults\BukkitCommand.class
org\bukkit\event\block\SignChangeEvent.class
org\bukkit\event\player\PlayerToggleSprintEvent.class
org\bukkit\block\CreatureSpawner.class
org\bukkit\block\data\type\CommandBlock.class
org\bukkit\inventory\CrafterInventory.class
org\bukkit\conversations\FixedSetPrompt.class
org\bukkit\map\MapFont$CharacterSprite.class
org\bukkit\entity\EnderDragon$Phase.class
org\bukkit\Color.class
org\bukkit\entity\Fish.class
org\bukkit\ChatColor$11.class
org\bukkit\event\player\PlayerTeleportEvent$TeleportCause.class
org\bukkit\event\server\ServerLoadEvent$LoadType.class
org\bukkit\material\types\package-info.class
org\bukkit\SkullType.class
org\bukkit\event\player\PlayerCommandPreprocessEvent.class
org\bukkit\event\weather\WeatherEvent.class
org\bukkit\ban\package-info.class
org\bukkit\entity\Illusioner.class
org\bukkit\event\player\PlayerResourcePackStatusEvent.class
org\bukkit\plugin\messaging\PluginMessageRecipient.class
org\bukkit\event\weather\package-info.class
org\bukkit\block\Hopper.class
org\bukkit\plugin\java\package-info.class
org\bukkit\command\defaults\ReloadCommand.class
org\bukkit\entity\Minecart.class
org\bukkit\event\block\BlockCookEvent.class
org\bukkit\block\BlockFace.class
org\bukkit\event\entity\CreeperPowerEvent$PowerCause.class
org\bukkit\entity\Golem.class
org\bukkit\block\data\type\SculkSensor.class
org\bukkit\block\data\Powerable.class
org\bukkit\event\entity\CreatureSpawnEvent$SpawnReason.class
org\bukkit\event\raid\package-info.class
org\bukkit\block\data\type\Bamboo$Leaves.class
org\bukkit\entity\PolarBear.class
org\bukkit\plugin\PluginDescriptionFile$1.class
org\bukkit\event\block\MoistureChangeEvent.class
org\bukkit\entity\WaterMob.class
org\bukkit\util\permissions\package-info.class
org\bukkit\event\entity\VillagerReplenishTradeEvent.class
org\bukkit\material\PistonBaseMaterial.class
org\bukkit\entity\WitherSkull.class
org\bukkit\metadata\MetadataEvaluationException.class
org\bukkit\Art.class
org\bukkit\plugin\messaging\StandardMessenger.class
org\bukkit\event\Event$Result.class
org\bukkit\Raid$RaidStatus.class
org\bukkit\block\Beacon.class
org\bukkit\entity\AbstractHorse.class
org\bukkit\event\entity\EntityRemoveEvent$Cause.class
org\bukkit\FeatureFlag.class
org\bukkit\event\weather\LightningStrikeEvent.class
org\bukkit\Rotation.class
org\bukkit\event\entity\PlayerLeashEntityEvent.class
org\bukkit\block\Bed.class
org\bukkit\inventory\package-info.class
org\bukkit\util\permissions\BroadcastPermissions.class
org\bukkit\event\inventory\InventoryOpenEvent.class
org\bukkit\enchantments\EnchantmentOffer.class
org\bukkit\generator\ChunkGenerator$BiomeGrid.class
org\bukkit\entity\Pose.class
org\bukkit\entity\Projectile.class
org\bukkit\event\vehicle\VehicleEnterEvent.class
org\bukkit\material\DirectionalContainer.class
org\bukkit\block\data\type\PointedDripstone.class
org\bukkit\inventory\SmithingInventory.class
org\bukkit\entity\Vex.class
org\bukkit\event\player\PlayerItemMendEvent.class
org\bukkit\block\data\type\EndPortalFrame.class
org\bukkit\event\entity\EntityEnterLoveModeEvent.class
org\bukkit\block\data\type\BigDripleaf.class
org\bukkit\event\player\PlayerRecipeBookSettingsChangeEvent$RecipeBookType.class
org\bukkit\plugin\PluginLoader.class
org\bukkit\BlockChangeDelegate.class
org\bukkit\inventory\meta\BundleMeta.class
org\bukkit\block\data\Levelled.class
org\bukkit\entity\Display$Brightness.class
org\bukkit\material\TrapDoor.class
org\bukkit\ChatColor$15.class
org\bukkit\conversations\NumericPrompt.class
org\bukkit\entity\TropicalFish$Pattern.class
org\bukkit\Particle$DustTransition.class
org\bukkit\block\data\type\EnderChest.class
org\bukkit\entity\EnderCrystal.class
org\bukkit\map\package-info.class
org\bukkit\inventory\meta\package-info.class
org\bukkit\entity\Spellcaster.class
org\bukkit\World.class
org\bukkit\block\data\type\Slab.class
org\bukkit\event\player\PlayerChangedWorldEvent.class
org\bukkit\block\Conduit.class
org\bukkit\util\CachedServerIcon.class
org\bukkit\block\data\type\PitcherCrop.class
org\bukkit\event\server\PluginEvent.class
org\bukkit\conversations\PluginNameConversationPrefix.class
org\bukkit\entity\EvokerFangs.class
org\bukkit\entity\Fox.class
org\bukkit\command\defaults\PluginsCommand.class
org\bukkit\packs\DataPackManager.class
org\bukkit\damage\DamageEffect.class
org\bukkit\block\spawner\package-info.class
org\bukkit\event\block\BlockPlaceEvent.class
org\bukkit\block\Lidded.class
org\bukkit\material\Wood.class
org\bukkit\persistence\ListPersistentDataTypeProvider.class
org\bukkit\entity\Painting.class
org\bukkit\entity\DragonFireball.class
org\bukkit\inventory\meta\trim\ArmorTrim.class
org\bukkit\command\PluginCommand.class
org\bukkit\plugin\java\LibraryLoader$1.class
org\bukkit\persistence\PersistentDataType$PrimitivePersistentDataType.class
org\bukkit\block\data\type\StructureBlock$Mode.class
org\bukkit\util\RayTraceResult.class
org\bukkit\map\MapView.class
org\bukkit\block\data\type\PinkPetals.class
org\bukkit\entity\Egg.class
org\bukkit\block\data\type\Chest$Type.class
org\bukkit\block\DecoratedPot$Side.class
org\bukkit\event\inventory\PrepareItemCraftEvent.class
org\bukkit\scoreboard\Objective.class
org\bukkit\event\entity\EntitySpellCastEvent.class
org\bukkit\entity\minecart\HopperMinecart.class
org\bukkit\packs\DataPack$Source.class
org\bukkit\boss\DragonBattle.class
org\bukkit\command\BlockCommandSender.class
org\bukkit\block\data\type\Repeater.class
org\bukkit\structure\package-info.class
org\bukkit\block\data\type\RespawnAnchor.class
org\bukkit\event\inventory\ClickType.class
org\bukkit\block\ChiseledBookshelf.class
org\bukkit\entity\Bee.class
org\bukkit\block\data\type\Observer.class
org\bukkit\ChatColor$16.class
org\bukkit\persistence\ListPersistentDataType.class
org\bukkit\event\raid\RaidStopEvent$Reason.class
org\bukkit\Note$Tone.class
org\bukkit\entity\Panda$Gene.class
org\bukkit\generator\package-info.class
org\bukkit\util\BiomeSearchResult.class
org\bukkit\inventory\InventoryHolder.class
org\bukkit\event\vehicle\package-info.class
org\bukkit\configuration\MemoryConfigurationOptions.class
org\bukkit\profile\PlayerTextures$SkinModel.class
org\bukkit\material\package-info.class
org\bukkit\event\player\PlayerSignOpenEvent$Cause.class
org\bukkit\event\world\WorldLoadEvent.class
org\bukkit\map\MinecraftFont.class
org\bukkit\inventory\meta\Damageable.class
org\bukkit\plugin\PluginDescriptionFile$1$1$1$1.class
org\bukkit\entity\Interaction$PreviousInteraction.class
org\bukkit\event\entity\StriderTemperatureChangeEvent.class
org\bukkit\FireworkEffect$Type.class
org\bukkit\block\data\type\Slab$Type.class
org\bukkit\entity\Steerable.class
org\bukkit\command\CommandSender.class
org\bukkit\command\ProxiedCommandSender.class
org\bukkit\WeatherType.class
org\bukkit\event\player\PlayerLevelChangeEvent.class
org\bukkit\event\world\ChunkUnloadEvent.class
org\bukkit\RegionAccessor.class
org\bukkit\entity\Zoglin.class
org\bukkit\metadata\package-info.class
org\bukkit\event\entity\SheepDyeWoolEvent.class
org\bukkit\block\data\type\Bamboo.class
org\bukkit\plugin\PluginAwareness$Flags.class
org\bukkit\block\data\type\NoteBlock.class
org\bukkit\event\entity\EntityDamageByBlockEvent.class
org\bukkit\permissions\package-info.class
org\bukkit\block\Comparator.class
org\bukkit\block\data\type\SculkVein.class
org\bukkit\entity\TropicalFish.class
org\bukkit\block\data\type\SculkShrieker.class
org\bukkit\permissions\PermissionDefault.class
org\bukkit\block\data\type\Fence.class
org\bukkit\command\PluginIdentifiableCommand.class
org\bukkit\entity\Horse$Color.class
org\bukkit\inventory\ShapelessRecipe.class
org\bukkit\ChatColor$14.class
org\bukkit\event\player\PlayerItemBreakEvent.class
org\bukkit\material\Crops.class
org\bukkit\block\sign\SignSide.class
org\bukkit\event\server\ServiceUnregisterEvent.class
org\bukkit\block\Dropper.class
org\bukkit\entity\Marker.class
org\bukkit\event\player\PlayerRecipeBookClickEvent.class
org\bukkit\inventory\EnchantingInventory.class
org\bukkit\entity\LlamaSpit.class
org\bukkit\event\player\PlayerChatTabCompleteEvent.class
org\bukkit\block\data\type\PistonHead.class
org\bukkit\event\entity\EntityDismountEvent.class
org\bukkit\event\player\AsyncPlayerPreLoginEvent.class
org\bukkit\material\types\MushroomBlockTexture.class
org\bukkit\OfflinePlayer.class
org\bukkit\event\enchantment\package-info.class
org\bukkit\boss\BarFlag.class
org\bukkit\entity\Donkey.class
org\bukkit\event\entity\EntityMountEvent.class
org\bukkit\GameRule.class
org\bukkit\inventory\meta\FireworkMeta.class
org\bukkit\entity\Player$Spigot.class
org\bukkit\material\MaterialData.class
org\bukkit\ChatColor$2.class
org\bukkit\event\entity\EntityAirChangeEvent.class
org\bukkit\event\player\PlayerBucketEmptyEvent.class
org\bukkit\ban\IpBanList.class
org\bukkit\event\vehicle\VehicleCollisionEvent.class
org\bukkit\entity\Breeze.class
org\bukkit\event\world\WorldEvent.class
org\bukkit\map\MapFont.class
org\bukkit\util\noise\PerlinOctaveGenerator.class
org\bukkit\util\permissions\DefaultPermissions.class
org\bukkit\inventory\RecipeChoice$MaterialChoice.class
org\bukkit\block\SculkSensor.class
org\bukkit\material\Door.class
org\bukkit\material\PistonExtensionMaterial.class
org\bukkit\material\Lever.class
org\bukkit\event\world\LootGenerateEvent.class
org\bukkit\inventory\CartographyInventory.class
org\bukkit\BanList.class
org\bukkit\entity\Breedable.class
org\bukkit\event\entity\EntityPlaceEvent.class
org\bukkit\block\data\Orientable.class
org\bukkit\entity\Flying.class
org\bukkit\generator\structure\package-info.class
org\bukkit\configuration\ConfigurationSection.class
org\bukkit\event\block\SpongeAbsorbEvent.class
org\bukkit\material\Furnace.class
org\bukkit\event\vehicle\VehicleExitEvent.class
org\bukkit\plugin\IllegalPluginAccessException.class
org\bukkit\event\inventory\FurnaceBurnEvent.class
org\bukkit\plugin\SimplePluginManager.class
org\bukkit\event\entity\EntityToggleSwimEvent.class
org\bukkit\event\raid\RaidTriggerEvent.class
org\bukkit\entity\Fireball.class
org\bukkit\event\weather\ThunderChangeEvent.class
org\bukkit\event\server\ServiceEvent.class
org\bukkit\configuration\file\YamlRepresenter$RepresentConfigurationSection.class
org\bukkit\generator\structure\StructureType.class
org\bukkit\event\world\ChunkLoadEvent.class
org\bukkit\event\block\BlockExpEvent.class
org\bukkit\ChatColor$7.class
org\bukkit\event\inventory\HopperInventorySearchEvent.class
org\bukkit\entity\EnderDragon.class
org\bukkit\event\block\NotePlayEvent.class
org\bukkit\event\vehicle\VehicleMoveEvent.class
org\bukkit\event\vehicle\VehicleDestroyEvent.class
org\bukkit\event\entity\EntityExplodeEvent.class
org\bukkit\ChatColor$9.class
org\bukkit\potion\PotionType$InternalPotionData.class
org\bukkit\conversations\StringPrompt.class
org\bukkit\block\data\type\Sign.class
org\bukkit\block\data\type\CaveVinesPlant.class
org\bukkit\entity\Explosive.class
org\bukkit\entity\Spider.class
org\bukkit\event\world\WorldInitEvent.class
org\bukkit\ChatColor$12.class
org\bukkit\event\vehicle\VehicleCreateEvent.class
org\bukkit\block\TileState.class
org\bukkit\boss\DragonBattle$RespawnPhase.class
org\bukkit\event\enchantment\EnchantItemEvent.class
org\bukkit\metadata\LazyMetadataValue.class
org\bukkit\entity\SizedFireball.class
org\bukkit\material\LongGrass.class
org\bukkit\Warning.class
org\bukkit\command\TabExecutor.class
org\bukkit\block\BlockSupport.class
org\bukkit\ServerTickManager.class
org\bukkit\conversations\MessagePrompt.class
org\bukkit\event\server\BroadcastMessageEvent.class
org\bukkit\plugin\PluginBase.class
org\bukkit\block\data\AnaloguePowerable.class
org\bukkit\inventory\meta\BlockDataMeta.class
org\bukkit\event\player\PlayerShearEntityEvent.class
org\bukkit\event\entity\EntityDamageEvent$DamageCause.class
org\bukkit\block\data\type\Jukebox.class
org\bukkit\entity\Spellcaster$Spell.class
org\bukkit\event\player\PlayerItemDamageEvent.class
org\bukkit\Registry$1.class
org\bukkit\event\block\BlockDispenseEvent.class
org\bukkit\Vibration$Destination$EntityDestination.class
org\bukkit\event\entity\EntityUnleashEvent.class
org\bukkit\entity\Evoker.class
org\bukkit\event\inventory\InventoryType.class
org\bukkit\inventory\meta\SuspiciousStewMeta.class
org\bukkit\conversations\Conversation.class
org\bukkit\entity\ThrownExpBottle.class
org\bukkit\event\entity\LingeringPotionSplashEvent.class
org\bukkit\event\entity\EntityCombustByBlockEvent.class
org\bukkit\Sound.class
org\bukkit\util\io\Wrapper.class
org\bukkit\block\Campfire.class
org\bukkit\block\data\type\Ladder.class
org\bukkit\event\hanging\HangingEvent.class
org\bukkit\advancement\AdvancementDisplay.class
org\bukkit\event\inventory\CraftItemEvent.class
org\bukkit\Registry.class
org\bukkit\entity\Mob.class
org\bukkit\event\entity\EntityEvent.class
org\bukkit\event\entity\VillagerCareerChangeEvent.class
org\bukkit\inventory\BrewerInventory.class
org\bukkit\event\world\EntitiesUnloadEvent.class
org\bukkit\entity\ExperienceOrb.class
org\bukkit\event\enchantment\PrepareItemEnchantEvent.class
org\bukkit\plugin\UnknownDependencyException.class
org\bukkit\entity\TextDisplay$TextAlignment.class
org\bukkit\event\entity\ProjectileHitEvent.class
org\bukkit\metadata\MetadataValueAdapter.class
org\bukkit\plugin\PluginManager.class
org\bukkit\attribute\AttributeInstance.class
org\bukkit\GrassSpecies.class
org\bukkit\permissions\Permission.class
org\bukkit\event\hanging\package-info.class
org\bukkit\event\block\BlockIgniteEvent.class
org\bukkit\plugin\messaging\ChannelNameTooLongException.class
org\bukkit\entity\Creeper.class
org\bukkit\block\data\Hatchable.class
org\bukkit\block\data\Waterlogged.class
org\bukkit\entity\Horse$Style.class
org\bukkit\event\block\BlockFromToEvent.class
org\bukkit\event\vehicle\VehicleDamageEvent.class
org\bukkit\block\data\MultipleFacing.class
org\bukkit\configuration\file\FileConfiguration.class
org\bukkit\entity\LightningStrike$Spigot.class
org\bukkit\block\data\type\RedstoneWallTorch.class
org\bukkit\PortalType.class
org\bukkit\loot\LootContext.class
org\bukkit\entity\HumanEntity.class
org\bukkit\event\vehicle\VehicleEntityCollisionEvent.class
org\bukkit\event\world\WorldUnloadEvent.class
org\bukkit\persistence\PersistentDataType$BooleanPersistentDataType.class
org\bukkit\entity\PiglinAbstract.class
org\bukkit\inventory\meta\tags\CustomItemTagContainer.class
org\bukkit\conversations\Conversation$ConversationState.class
org\bukkit\event\inventory\PrepareGrindstoneEvent.class
org\bukkit\material\EnderChest.class
org\bukkit\structure\Palette.class
org\bukkit\entity\ThrownPotion.class
org\bukkit\block\data\type\Cake.class
org\bukkit\event\player\PlayerAdvancementDoneEvent.class
org\bukkit\ban\ProfileBanList.class
org\bukkit\block\data\type\TrialSpawner$State.class
org\bukkit\block\data\type\TrapDoor.class
org\bukkit\event\block\BlockPistonRetractEvent.class
org\bukkit\plugin\PluginDescriptionFile$1$1$1.class
org\bukkit\advancement\package-info.class
org\bukkit\event\raid\RaidFinishEvent.class
org\bukkit\event\block\BlockDispenseArmorEvent.class
org\bukkit\potion\PotionBrewer.class
org\bukkit\profile\PlayerTextures.class
org\bukkit\event\server\ServiceRegisterEvent.class
org\bukkit\entity\Snowball.class
org\bukkit\inventory\JukeboxInventory.class
org\bukkit\inventory\meta\BookMeta$Generation.class
org\bukkit\block\structure\package-info.class
org\bukkit\ChatColor$5.class
org\bukkit\block\data\type\SculkCatalyst.class
org\bukkit\entity\Pig.class
org\bukkit\block\banner\package-info.class
org\bukkit\conversations\ConversationContext.class
org\bukkit\enchantments\Enchantment.class
org\bukkit\event\raid\RaidStopEvent.class
org\bukkit\FireworkEffect.class
org\bukkit\material\Coal.class
org\bukkit\plugin\RegisteredListener.class
org\bukkit\material\Tree.class
org\bukkit\event\block\FluidLevelChangeEvent.class
org\bukkit\material\Rails.class
org\bukkit\inventory\CampfireRecipe.class
org\bukkit\entity\CaveSpider.class
org\bukkit\material\Vine.class
org\bukkit\plugin\SimpleServicesManager.class
org\bukkit\event\player\PlayerShowEntityEvent.class
org\bukkit\inventory\Merchant.class
org\bukkit\advancement\AdvancementDisplayType.class
org\bukkit\util\noise\PerlinNoiseGenerator.class
org\bukkit\entity\package-info.class
org\bukkit\event\entity\EntityDamageByEntityEvent.class
org\bukkit\Particle$DustOptions.class
org\bukkit\plugin\Plugin.class
org\bukkit\entity\Pillager.class
org\bukkit\inventory\meta\tags\ItemTagType.class
org\bukkit\event\block\TNTPrimeEvent.class
org\bukkit\UnsafeValues.class
org\bukkit\util\Vector.class
org\bukkit\entity\Frog$Variant.class
org\bukkit\inventory\meta\SkullMeta.class
org\bukkit\map\MapView$Scale.class
org\bukkit\plugin\PluginDescriptionResolver.class
org\bukkit\conversations\ExactMatchConversationCanceller.class
org\bukkit\event\block\EntityBlockFormEvent.class
org\bukkit\entity\MushroomCow.class
org\bukkit\command\defaults\VersionCommand$1.class
org\bukkit\Vibration.class
org\bukkit\event\entity\VillagerAcquireTradeEvent.class
org\bukkit\event\player\PlayerSwapHandItemsEvent.class
org\bukkit\block\data\type\Chest.class
org\bukkit\material\TexturedMaterial.class
org\bukkit\inventory\ComplexRecipe.class
org\bukkit\configuration\Configuration.class
org\bukkit\inventory\meta\ItemMeta.class
org\bukkit\block\Lectern.class
org\bukkit\configuration\ConfigurationOptions.class
org\bukkit\entity\Display.class
org\bukkit\entity\Fox$Type.class
org\bukkit\entity\Wither$Head.class
org\bukkit\event\entity\PotionSplashEvent.class
org\bukkit\event\world\PortalCreateEvent.class
org\bukkit\util\ChatPaginator$ChatPage.class
org\bukkit\util\io\BukkitObjectInputStream.class
org\bukkit\material\Sign.class
org\bukkit\material\Ladder.class
org\bukkit\enchantments\EnchantmentTarget$14.class
org\bukkit\material\Chest.class
org\bukkit\event\player\PlayerBedEnterEvent.class
org\bukkit\help\HelpTopicComparator.class
org\bukkit\plugin\messaging\PluginMessageListener.class
org\bukkit\TreeSpecies.class
org\bukkit\generator\structure\Structure.class
org\bukkit\entity\Creature.class
org\bukkit\event\world\WorldSaveEvent.class
org\bukkit\event\block\BellRingEvent.class
org\bukkit\ChatColor$6.class
org\bukkit\help\HelpMap.class
org\bukkit\enchantments\EnchantmentTarget$11.class
org\bukkit\event\block\CampfireStartEvent.class
org\bukkit\block\data\type\Stairs$Shape.class
org\bukkit\entity\ZombieHorse.class
org\bukkit\event\inventory\FurnaceSmeltEvent.class
org\bukkit\inventory\MerchantRecipe.class
org\bukkit\block\data\Brushable.class
org\bukkit\plugin\PluginDescriptionFile$1$1$2.class
org\bukkit\event\entity\EntityInteractEvent.class
org\bukkit\block\data\type\RedstoneWire.class
org\bukkit\event\block\BlockExplodeEvent.class
org\bukkit\block\data\type\ChiseledBookshelf.class
org\bukkit\entity\Squid.class
org\bukkit\event\inventory\package-info.class
org\bukkit\inventory\recipe\CookingBookCategory.class
org\bukkit\block\data\type\Door.class
org\bukkit\event\block\BlockIgniteEvent$IgniteCause.class
org\bukkit\configuration\serialization\package-info.class
org\bukkit\event\player\PlayerSpawnChangeEvent$Cause.class
org\bukkit\material\Button.class
org\bukkit\plugin\java\PluginClassLoader.class
org\bukkit\event\EventPriority.class
org\bukkit\event\entity\PlayerDeathEvent.class
org\bukkit\entity\Vehicle.class
org\bukkit\block\data\type\Switch$Face.class
org\bukkit\enchantments\EnchantmentTarget$12.class
org\bukkit\command\CommandSender$Spigot.class
org\bukkit\entity\Wither.class
org\bukkit\inventory\PlayerInventory.class
org\bukkit\block\data\BlockData.class
org\bukkit\Chunk.class
org\bukkit\plugin\RegisteredServiceProvider.class
org\bukkit\event\player\PlayerBedLeaveEvent.class
org\bukkit\inventory\LoomInventory.class
org\bukkit\command\defaults\TimingsCommand.class
org\bukkit\plugin\EventExecutor.class
org\bukkit\metadata\LazyMetadataValue$CacheStrategy.class
org\bukkit\ChatColor$3.class
org\bukkit\event\EventHandler.class
org\bukkit\plugin\java\JavaPluginLoader$1.class
org\bukkit\command\PluginCommandYamlParser.class
org\bukkit\event\inventory\DragType.class
org\bukkit\event\player\PlayerPreLoginEvent.class
org\bukkit\entity\Dolphin.class
org\bukkit\inventory\meta\tags\package-info.class
org\bukkit\block\Furnace.class
org\bukkit\event\server\MapInitializeEvent.class
org\bukkit\enchantments\EnchantmentTarget$13.class
org\bukkit\inventory\meta\Repairable.class
org\bukkit\command\package-info.class
org\bukkit\event\entity\EntityKnockbackEvent.class
org\bukkit\plugin\PluginLoadOrder.class
org\bukkit\entity\Enderman.class
org\bukkit\attribute\AttributeModifier.class
org\bukkit\entity\memory\package-info.class
org\bukkit\material\Sapling.class
org\bukkit\block\DaylightDetector.class
org\bukkit\entity\Tadpole.class
org\bukkit\ChatColor$4.class
org\bukkit\event\entity\CreeperPowerEvent.class
org\bukkit\plugin\package-info.class
org\bukkit\entity\Rabbit.class
org\bukkit\event\entity\SlimeSplitEvent.class
org\bukkit\block\spawner\SpawnerEntry.class
org\bukkit\event\entity\EntitySpawnEvent.class
org\bukkit\block\data\type\Gate.class
org\bukkit\scoreboard\package-info.class
org\bukkit\material\SpawnEgg.class
org\bukkit\event\inventory\TradeSelectEvent.class
