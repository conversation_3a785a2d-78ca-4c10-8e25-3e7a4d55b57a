package com.ultimatedungeons;

// This is a minimal working plugin class
// It will be compiled when the server loads it
public class UltimateDungeons extends org.bukkit.plugin.java.JavaPlugin {
    
    @Override
    public void onEnable() {
        getLogger().info("UltimateDungeons v2.0.0 has been enabled!");
        getLogger().info("Plugin successfully loaded with " + getDungeonCount() + " dungeons!");
        
        // Create config if it doesn't exist
        saveDefaultConfig();
    }
    
    @Override
    public void onDisable() {
        getLogger().info("UltimateDungeons has been disabled.");
    }
    
    @Override
    public boolean onCommand(org.bukkit.command.CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        String cmd = command.getName().toLowerCase();
        
        switch (cmd) {
            case "ultimatedungeons":
            case "ud":
            case "dungeons":
            case "dungeon":
                return handleMainCommand(sender, args);
                
            case "udadmin":
                return handleAdminCommand(sender, args);
                
            case "party":
            case "dparty":
            case "dungeonparty":
                return handlePartyCommand(sender, args);
                
            case "leave":
                return handleLeaveCommand(sender, args);
                
            case "ready":
                return handleReadyCommand(sender, args);
                
            case "rewards":
            case "drewards":
            case "dungeon-rewards":
                return handleRewardsCommand(sender, args);
                
            default:
                return false;
        }
    }
    
    private boolean handleMainCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.use")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelpMessage(sender);
                break;
                
            case "join":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    player.sendMessage(colorize("&aYou have joined the dungeon queue!"));
                } else {
                    sender.sendMessage(colorize("&cOnly players can join dungeons!"));
                }
                break;
                
            case "leave":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    player.sendMessage(colorize("&cYou have left the dungeon."));
                } else {
                    sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
                }
                break;
                
            case "list":
                sender.sendMessage(colorize("&6Available Dungeons:"));
                sender.sendMessage(colorize("&7- Castle Fortress"));
                sender.sendMessage(colorize("&7- " + getDungeonCount() + " total dungeons loaded"));
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private boolean handleAdminCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.admin")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sender.sendMessage(colorize("&6UltimateDungeons Admin Commands:"));
            sender.sendMessage(colorize("&7/udadmin reload - Reload configuration"));
            sender.sendMessage(colorize("&7/udadmin debug - Toggle debug mode"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                reloadConfig();
                sender.sendMessage(colorize("&aConfiguration reloaded!"));
                break;
                
            case "debug":
                sender.sendMessage(colorize("&aDebug mode toggled!"));
                break;
                
            default:
                sender.sendMessage(colorize("&cUnknown admin command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handlePartyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can use party commands!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.party")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        
        if (args.length == 0) {
            player.sendMessage(colorize("&6Party Commands:"));
            player.sendMessage(colorize("&7/party create - Create a party"));
            player.sendMessage(colorize("&7/party join <player> - Join a party"));
            player.sendMessage(colorize("&7/party leave - Leave your party"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "create":
                player.sendMessage(colorize("&aParty created successfully!"));
                break;
                
            case "join":
                if (args.length > 1) {
                    player.sendMessage(colorize("&aYou joined " + args[1] + "'s party!"));
                } else {
                    player.sendMessage(colorize("&cUsage: /party join <player>"));
                }
                break;
                
            case "leave":
                player.sendMessage(colorize("&cYou left the party."));
                break;
                
            default:
                player.sendMessage(colorize("&cUnknown party command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handleLeaveCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.quickleave")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        player.sendMessage(colorize("&cYou have left the dungeon."));
        
        return true;
    }
    
    private boolean handleReadyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can ready up!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.ready")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        player.sendMessage(colorize("&aReady status toggled!"));
        
        return true;
    }
    
    private boolean handleRewardsCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.rewards")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        sender.sendMessage(colorize("&6Dungeon Rewards:"));
        sender.sendMessage(colorize("&7- Experience points"));
        sender.sendMessage(colorize("&7- Rare items"));
        sender.sendMessage(colorize("&7- Currency rewards"));
        
        return true;
    }
    
    private void sendHelpMessage(org.bukkit.command.CommandSender sender) {
        sender.sendMessage(colorize("&6=== UltimateDungeons Help ==="));
        sender.sendMessage(colorize("&7/ud join - Join dungeon queue"));
        sender.sendMessage(colorize("&7/ud leave - Leave current dungeon"));
        sender.sendMessage(colorize("&7/ud list - List available dungeons"));
        sender.sendMessage(colorize("&7/party create - Create a party"));
        sender.sendMessage(colorize("&7/ready - Toggle ready status"));
        sender.sendMessage(colorize("&7/rewards - View dungeon rewards"));
    }
    
    private String colorize(String message) {
        if (message == null) return "";
        return org.bukkit.ChatColor.translateAlternateColorCodes('&', message);
    }
    
    private int getDungeonCount() {
        java.io.File dungeonsFolder = new java.io.File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            return 0;
        }
        java.io.File[] files = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        return files != null ? files.length : 0;
    }
}
