package com.ultimatedungeons;

// This is a minimal working plugin class
// It will be compiled when the server loads it
public class UltimateDungeons extends org.bukkit.plugin.java.JavaPlugin {
    
    @Override
    public void onEnable() {
        getLogger().info("UltimateDungeons v2.0.0 has been enabled!");
        getLogger().info("Plugin successfully loaded with " + getDungeonCount() + " dungeons!");

        // Create config if it doesn't exist
        saveDefaultConfig();

        // Register event listeners for GUI
        getServer().getPluginManager().registerEvents(new GUIListener(), this);
    }
    
    @Override
    public void onDisable() {
        getLogger().info("UltimateDungeons has been disabled.");
    }
    
    @Override
    public boolean onCommand(org.bukkit.command.CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        String cmd = command.getName().toLowerCase();
        
        switch (cmd) {
            case "ultimatedungeons":
            case "ud":
            case "dungeons":
            case "dungeon":
                return handleMainCommand(sender, args);
                
            case "udadmin":
                return handleAdminCommand(sender, args);
                
            case "party":
            case "dparty":
            case "dungeonparty":
                return handlePartyCommand(sender, args);
                
            case "leave":
                return handleLeaveCommand(sender, args);
                
            case "ready":
                return handleReadyCommand(sender, args);
                
            case "rewards":
            case "drewards":
            case "dungeon-rewards":
                return handleRewardsCommand(sender, args);
                
            default:
                return false;
        }
    }
    
    private boolean handleMainCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.use")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelpMessage(sender);
                break;
                
            case "join":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    player.sendMessage(colorize("&aYou have joined the dungeon queue!"));
                } else {
                    sender.sendMessage(colorize("&cOnly players can join dungeons!"));
                }
                break;
                
            case "leave":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    player.sendMessage(colorize("&cYou have left the dungeon."));
                } else {
                    sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
                }
                break;
                
            case "list":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    openDungeonListGUI(player);
                } else {
                    sender.sendMessage(colorize("&6Available Dungeons:"));
                    sender.sendMessage(colorize("&7- Castle Fortress"));
                    sender.sendMessage(colorize("&7- " + getDungeonCount() + " total dungeons loaded"));
                }
                break;

            case "gui":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    openMainGUI(player);
                } else {
                    sender.sendMessage(colorize("&cOnly players can use the GUI!"));
                }
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private boolean handleAdminCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.admin")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sender.sendMessage(colorize("&6UltimateDungeons Admin Commands:"));
            sender.sendMessage(colorize("&7/udadmin reload - Reload configuration"));
            sender.sendMessage(colorize("&7/udadmin debug - Toggle debug mode"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                reloadConfig();
                sender.sendMessage(colorize("&aConfiguration reloaded!"));
                break;
                
            case "debug":
                sender.sendMessage(colorize("&aDebug mode toggled!"));
                break;
                
            default:
                sender.sendMessage(colorize("&cUnknown admin command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handlePartyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can use party commands!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.party")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        
        if (args.length == 0) {
            player.sendMessage(colorize("&6Party Commands:"));
            player.sendMessage(colorize("&7/party create - Create a party"));
            player.sendMessage(colorize("&7/party join <player> - Join a party"));
            player.sendMessage(colorize("&7/party leave - Leave your party"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "create":
                player.sendMessage(colorize("&aParty created successfully!"));
                break;
                
            case "join":
                if (args.length > 1) {
                    player.sendMessage(colorize("&aYou joined " + args[1] + "'s party!"));
                } else {
                    player.sendMessage(colorize("&cUsage: /party join <player>"));
                }
                break;
                
            case "leave":
                player.sendMessage(colorize("&cYou left the party."));
                break;
                
            default:
                player.sendMessage(colorize("&cUnknown party command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handleLeaveCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.quickleave")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        player.sendMessage(colorize("&cYou have left the dungeon."));
        
        return true;
    }
    
    private boolean handleReadyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can ready up!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.ready")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        player.sendMessage(colorize("&aReady status toggled!"));
        
        return true;
    }
    
    private boolean handleRewardsCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.rewards")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        sender.sendMessage(colorize("&6Dungeon Rewards:"));
        sender.sendMessage(colorize("&7- Experience points"));
        sender.sendMessage(colorize("&7- Rare items"));
        sender.sendMessage(colorize("&7- Currency rewards"));
        
        return true;
    }
    
    private void sendHelpMessage(org.bukkit.command.CommandSender sender) {
        sender.sendMessage(colorize("&6=== UltimateDungeons Help ==="));
        sender.sendMessage(colorize("&e/ud gui &7- Open the main GUI menu"));
        sender.sendMessage(colorize("&7/ud join - Join dungeon queue"));
        sender.sendMessage(colorize("&7/ud leave - Leave current dungeon"));
        sender.sendMessage(colorize("&7/ud list - List available dungeons"));
        sender.sendMessage(colorize("&7/party create - Create a party"));
        sender.sendMessage(colorize("&7/ready - Toggle ready status"));
        sender.sendMessage(colorize("&7/rewards - View dungeon rewards"));
    }
    
    private String colorize(String message) {
        if (message == null) return "";
        return org.bukkit.ChatColor.translateAlternateColorCodes('&', message);
    }
    
    private int getDungeonCount() {
        java.io.File dungeonsFolder = new java.io.File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            return 0;
        }
        java.io.File[] files = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        return files != null ? files.length : 0;
    }

    // GUI Methods
    private void openMainGUI(org.bukkit.entity.Player player) {
        org.bukkit.inventory.Inventory gui = org.bukkit.Bukkit.createInventory(null, 27, colorize("&6&lUltimate Dungeons"));

        // Join Queue Button
        org.bukkit.inventory.ItemStack joinItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.DIAMOND_SWORD);
        org.bukkit.inventory.meta.ItemMeta joinMeta = joinItem.getItemMeta();
        if (joinMeta != null) {
            joinMeta.setDisplayName(colorize("&a&lJoin Dungeon Queue"));
            joinMeta.setLore(java.util.Arrays.asList(
                colorize("&7Click to join the dungeon queue"),
                colorize("&7and start your adventure!"),
                "",
                colorize("&eClick to join!")
            ));
            joinItem.setItemMeta(joinMeta);
        }
        gui.setItem(11, joinItem);

        // Dungeon List Button
        org.bukkit.inventory.ItemStack listItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BOOK);
        org.bukkit.inventory.meta.ItemMeta listMeta = listItem.getItemMeta();
        if (listMeta != null) {
            listMeta.setDisplayName(colorize("&b&lAvailable Dungeons"));
            listMeta.setLore(java.util.Arrays.asList(
                colorize("&7View all available dungeons"),
                colorize("&7Total dungeons: &a" + getDungeonCount()),
                "",
                colorize("&eClick to browse!")
            ));
            listItem.setItemMeta(listMeta);
        }
        gui.setItem(13, listItem);

        // Party Management Button
        org.bukkit.inventory.ItemStack partyItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.PLAYER_HEAD);
        org.bukkit.inventory.meta.ItemMeta partyMeta = partyItem.getItemMeta();
        if (partyMeta != null) {
            partyMeta.setDisplayName(colorize("&d&lParty Management"));
            partyMeta.setLore(java.util.Arrays.asList(
                colorize("&7Create or manage your party"),
                colorize("&7Team up with friends!"),
                "",
                colorize("&eClick to manage!")
            ));
            partyItem.setItemMeta(partyMeta);
        }
        gui.setItem(15, partyItem);

        // Rewards Button
        org.bukkit.inventory.ItemStack rewardsItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.CHEST);
        org.bukkit.inventory.meta.ItemMeta rewardsMeta = rewardsItem.getItemMeta();
        if (rewardsMeta != null) {
            rewardsMeta.setDisplayName(colorize("&6&lDungeon Rewards"));
            rewardsMeta.setLore(java.util.Arrays.asList(
                colorize("&7View possible dungeon rewards"),
                colorize("&7and your statistics"),
                "",
                colorize("&eClick to view!")
            ));
            rewardsItem.setItemMeta(rewardsMeta);
        }
        gui.setItem(22, rewardsItem);

        player.openInventory(gui);
    }

    private void openDungeonListGUI(org.bukkit.entity.Player player) {
        org.bukkit.inventory.Inventory gui = org.bukkit.Bukkit.createInventory(null, 54, colorize("&6&lAvailable Dungeons"));

        // Get dungeon files
        java.io.File dungeonsFolder = new java.io.File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            // No dungeons folder, show empty GUI
            org.bukkit.inventory.ItemStack noItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BARRIER);
            org.bukkit.inventory.meta.ItemMeta noMeta = noItem.getItemMeta();
            if (noMeta != null) {
                noMeta.setDisplayName(colorize("&c&lNo Dungeons Found"));
                noMeta.setLore(java.util.Arrays.asList(
                    colorize("&7No dungeon files were found"),
                    colorize("&7Contact an administrator")
                ));
                noItem.setItemMeta(noMeta);
            }
            gui.setItem(22, noItem);
            player.openInventory(gui);
            return;
        }

        java.io.File[] dungeonFiles = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        if (dungeonFiles == null || dungeonFiles.length == 0) {
            // No dungeon files
            org.bukkit.inventory.ItemStack noItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BARRIER);
            org.bukkit.inventory.meta.ItemMeta noMeta = noItem.getItemMeta();
            if (noMeta != null) {
                noMeta.setDisplayName(colorize("&c&lNo Dungeons Available"));
                noMeta.setLore(java.util.Arrays.asList(
                    colorize("&7No dungeon schematics found"),
                    colorize("&7Contact an administrator")
                ));
                noItem.setItemMeta(noMeta);
            }
            gui.setItem(22, noItem);
            player.openInventory(gui);
            return;
        }

        // Add dungeon items
        int slot = 0;
        for (java.io.File dungeonFile : dungeonFiles) {
            if (slot >= 45) break; // Leave space for navigation

            String dungeonName = dungeonFile.getName().replace(".schematic", "");
            String displayName = formatDungeonName(dungeonName);

            org.bukkit.inventory.ItemStack dungeonItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.STRUCTURE_BLOCK);
            org.bukkit.inventory.meta.ItemMeta dungeonMeta = dungeonItem.getItemMeta();
            if (dungeonMeta != null) {
                dungeonMeta.setDisplayName(colorize("&a&l" + displayName));
                dungeonMeta.setLore(java.util.Arrays.asList(
                    colorize("&7Dungeon: &f" + displayName),
                    colorize("&7Difficulty: &eNormal"),
                    colorize("&7Players: &a1-5"),
                    "",
                    colorize("&eClick to join this dungeon!")
                ));
                dungeonItem.setItemMeta(dungeonMeta);
            }
            gui.setItem(slot, dungeonItem);
            slot++;
        }

        // Back button
        org.bukkit.inventory.ItemStack backItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.ARROW);
        org.bukkit.inventory.meta.ItemMeta backMeta = backItem.getItemMeta();
        if (backMeta != null) {
            backMeta.setDisplayName(colorize("&c&lBack to Main Menu"));
            backMeta.setLore(java.util.Arrays.asList(
                colorize("&7Return to the main menu")
            ));
            backItem.setItemMeta(backMeta);
        }
        gui.setItem(49, backItem);

        player.openInventory(gui);
    }

    private String formatDungeonName(String fileName) {
        // Convert file names like "castle_fortress" to "Castle Fortress"
        return java.util.Arrays.stream(fileName.split("_"))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
            .reduce((a, b) -> a + " " + b)
            .orElse(fileName);
    }

    // GUI Event Listener
    public class GUIListener implements org.bukkit.event.Listener {

        @org.bukkit.event.EventHandler
        public void onInventoryClick(org.bukkit.event.inventory.InventoryClickEvent event) {
            if (!(event.getWhoClicked() instanceof org.bukkit.entity.Player)) return;

            org.bukkit.entity.Player player = (org.bukkit.entity.Player) event.getWhoClicked();
            String title = event.getView().getTitle();

            // Check if it's one of our GUIs
            if (!title.contains("Ultimate Dungeons") && !title.contains("Available Dungeons")) {
                return;
            }

            event.setCancelled(true); // Prevent item taking

            if (event.getCurrentItem() == null || event.getCurrentItem().getType() == org.bukkit.Material.AIR) {
                return;
            }

            org.bukkit.inventory.ItemStack clickedItem = event.getCurrentItem();
            String itemName = clickedItem.getItemMeta() != null ? clickedItem.getItemMeta().getDisplayName() : "";

            // Handle main GUI clicks
            if (title.contains("Ultimate Dungeons") && !title.contains("Available")) {
                handleMainGUIClick(player, itemName, clickedItem);
            }
            // Handle dungeon list GUI clicks
            else if (title.contains("Available Dungeons")) {
                handleDungeonListGUIClick(player, itemName, clickedItem);
            }
        }

        private void handleMainGUIClick(org.bukkit.entity.Player player, String itemName, org.bukkit.inventory.ItemStack item) {
            if (itemName.contains("Join Dungeon Queue")) {
                player.closeInventory();
                player.performCommand("ud join");
            }
            else if (itemName.contains("Available Dungeons")) {
                openDungeonListGUI(player);
            }
            else if (itemName.contains("Party Management")) {
                player.closeInventory();
                player.performCommand("party create");
            }
            else if (itemName.contains("Dungeon Rewards")) {
                player.closeInventory();
                player.performCommand("ud rewards");
            }
        }

        private void handleDungeonListGUIClick(org.bukkit.entity.Player player, String itemName, org.bukkit.inventory.ItemStack item) {
            if (itemName.contains("Back to Main Menu")) {
                openMainGUI(player);
            }
            else if (item.getType() == org.bukkit.Material.STRUCTURE_BLOCK) {
                // Extract dungeon name and join
                String dungeonName = itemName.replace("§a§l", "").replace("&a&l", "");
                player.closeInventory();
                player.sendMessage(colorize("&aJoining dungeon: &f" + dungeonName));
                player.performCommand("ud join");
            }
        }
    }
}
