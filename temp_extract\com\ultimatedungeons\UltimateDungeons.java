package com.ultimatedungeons;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.io.File;

// Complete UltimateDungeons plugin with all features
public class UltimateDungeons extends org.bukkit.plugin.java.JavaPlugin {

    // Core Data Structures
    private Map<String, DungeonTemplate> dungeonTemplates = new ConcurrentHashMap<>();
    private Map<UUID, DungeonInstance> activeDungeons = new ConcurrentHashMap<>();
    private Map<UUID, Party> parties = new ConcurrentHashMap<>();
    private Map<UUID, PlayerData> playerData = new ConcurrentHashMap<>();
    private Queue<QueueEntry> dungeonQueue = new LinkedList<>();
    private Map<String, LootTable> lootTables = new ConcurrentHashMap<>();
    private Map<UUID, Long> playerCooldowns = new ConcurrentHashMap<>();

    // Managers
    private DungeonManager dungeonManager;
    private PartyManager partyManager;
    private QueueManager queueManager;
    private RewardManager rewardManager;
    private StatisticsManager statsManager;
    private BuilderManager builderManager;

    // Builder Data
    private Map<UUID, BuildSession> activeBuildSessions = new ConcurrentHashMap<>();
    private Map<UUID, SchematicPreview> activepreviews = new ConcurrentHashMap<>();
    private Map<org.bukkit.Location, DungeonBlock> dungeonBlocks = new ConcurrentHashMap<>();
    
    @Override
    public void onEnable() {
        getLogger().info("UltimateDungeons v3.0.0 - Complete Edition starting up...");

        // Create plugin folders
        createDirectories();

        // Initialize configuration
        saveDefaultConfig();

        // Initialize managers
        initializeManagers();

        // Load data
        loadAllData();

        // Register event listeners
        getServer().getPluginManager().registerEvents(new GUIListener(), this);

        // Start background tasks
        startBackgroundTasks();

        getLogger().info("UltimateDungeons v3.0.0 enabled successfully!");
        getLogger().info("Loaded " + dungeonTemplates.size() + " dungeon templates");
        getLogger().info("Loaded " + lootTables.size() + " loot tables");
        getLogger().info("Loaded " + playerData.size() + " player profiles");
    }

    private void createDirectories() {
        new File(getDataFolder(), "dungeons").mkdirs();
        new File(getDataFolder(), "loot").mkdirs();
        new File(getDataFolder(), "playerdata").mkdirs();
    }

    private void initializeManagers() {
        dungeonManager = new DungeonManager();
        builderManager = new BuilderManager();
        partyManager = new PartyManager();
        queueManager = new QueueManager();
        rewardManager = new RewardManager();
        statsManager = new StatisticsManager();
    }

    private void loadAllData() {
        dungeonManager.loadDungeonTemplates();
        rewardManager.loadLootTables();
        statsManager.loadPlayerData();
    }

    private void startBackgroundTasks() {
        // Queue processing task
        getServer().getScheduler().runTaskTimer(this, () -> {
            queueManager.processQueue();
        }, 20L, 100L); // Every 5 seconds

        // Auto-save task
        getServer().getScheduler().runTaskTimer(this, () -> {
            statsManager.saveAllPlayerData();
        }, 6000L, 6000L); // Every 5 minutes

        // Cleanup task
        getServer().getScheduler().runTaskTimer(this, () -> {
            cleanupExpiredData();
        }, 12000L, 12000L); // Every 10 minutes
    }

    private void cleanupExpiredData() {
        // Remove expired cooldowns
        long currentTime = System.currentTimeMillis();
        playerCooldowns.entrySet().removeIf(entry -> entry.getValue() <= currentTime);

        // Clean up old party invites (older than 5 minutes)
        for (Party party : parties.values()) {
            party.getInvites().removeIf(invite -> {
                // In a real implementation, you'd track invite timestamps
                return false; // Placeholder
            });
        }
    }
    
    @Override
    public void onDisable() {
        getLogger().info("UltimateDungeons has been disabled.");
    }
    
    @Override
    public boolean onCommand(org.bukkit.command.CommandSender sender, org.bukkit.command.Command command, String label, String[] args) {
        String cmd = command.getName().toLowerCase();
        
        switch (cmd) {
            case "ultimatedungeons":
            case "ud":
            case "dungeons":
            case "dungeon":
                return handleMainCommand(sender, args);
                
            case "udadmin":
                return handleAdminCommand(sender, args);

            case "udtools":
            case "dungeontools":
            case "dtools":
                return handleBuilderCommand(sender, args);
                
            case "party":
            case "dparty":
            case "dungeonparty":
                return handlePartyCommand(sender, args);
                
            case "leave":
                return handleLeaveCommand(sender, args);
                
            case "ready":
                return handleReadyCommand(sender, args);
                
            case "rewards":
            case "drewards":
            case "dungeon-rewards":
                return handleRewardsCommand(sender, args);
                
            default:
                return false;
        }
    }
    
    private boolean handleMainCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.use")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "help":
                sendHelpMessage(sender);
                break;
                
            case "join":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    return handleJoinQueue(player, Arrays.copyOfRange(args, 1, args.length));
                } else {
                    sender.sendMessage(colorize("&cOnly players can join dungeons!"));
                }
                break;
                
            case "leave":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    player.sendMessage(colorize("&cYou have left the dungeon."));
                } else {
                    sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
                }
                break;
                
            case "list":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    openDungeonListGUI(player);
                } else {
                    sender.sendMessage(colorize("&6Available Dungeons:"));
                    sender.sendMessage(colorize("&7- Castle Fortress"));
                    sender.sendMessage(colorize("&7- " + getDungeonCount() + " total dungeons loaded"));
                }
                break;

            case "gui":
                if (sender instanceof org.bukkit.entity.Player) {
                    org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
                    openMainGUI(player);
                } else {
                    sender.sendMessage(colorize("&cOnly players can use the GUI!"));
                }
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private boolean handleAdminCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.admin")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        if (args.length == 0) {
            sender.sendMessage(colorize("&6UltimateDungeons Admin Commands:"));
            sender.sendMessage(colorize("&7/udadmin reload - Reload configuration"));
            sender.sendMessage(colorize("&7/udadmin debug - Toggle debug mode"));
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "reload":
                reloadConfig();
                sender.sendMessage(colorize("&aConfiguration reloaded!"));
                break;
                
            case "debug":
                sender.sendMessage(colorize("&aDebug mode toggled!"));
                break;
                
            default:
                sender.sendMessage(colorize("&cUnknown admin command!"));
                break;
        }
        
        return true;
    }
    
    private boolean handlePartyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can use party commands!"));
            return true;
        }

        if (!sender.hasPermission("ultimatedungeons.party")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }

        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        UUID playerId = player.getUniqueId();

        if (args.length == 0) {
            Party party = partyManager.getPlayerParty(playerId);
            if (party != null) {
                showPartyInfo(player, party);
            } else {
                showPartyHelp(player);
            }
            return true;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "create":
                return handlePartyCreate(player);
            case "invite":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /party invite <player>"));
                    return true;
                }
                return handlePartyInvite(player, args[1]);
            case "accept":
                return handlePartyAccept(player);
            case "decline":
                return handlePartyDecline(player);
            case "leave":
                return handlePartyLeave(player);
            case "kick":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /party kick <player>"));
                    return true;
                }
                return handlePartyKick(player, args[1]);
            case "promote":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /party promote <player>"));
                    return true;
                }
                return handlePartyPromote(player, args[1]);
            case "settings":
                return handlePartySettings(player, Arrays.copyOfRange(args, 1, args.length));
            case "list":
                return handlePartyList(player);
            case "info":
                Party party = partyManager.getPlayerParty(playerId);
                if (party != null) {
                    showPartyInfo(player, party);
                } else {
                    player.sendMessage(colorize("&cYou are not in a party!"));
                }
                return true;
            default:
                showPartyHelp(player);
                return true;
        }
    }
    
    private boolean handleLeaveCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can leave dungeons!"));
            return true;
        }
        
        if (!sender.hasPermission("ultimatedungeons.quickleave")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }
        
        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        player.sendMessage(colorize("&cYou have left the dungeon."));
        
        return true;
    }
    
    private boolean handleReadyCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can ready up!"));
            return true;
        }

        if (!sender.hasPermission("ultimatedungeons.ready")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }

        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        UUID playerId = player.getUniqueId();

        Party party = partyManager.getPlayerParty(playerId);
        if (party == null) {
            player.sendMessage(colorize("&cYou must be in a party to ready up!"));
            player.sendMessage(colorize("&7Create a party with &e/party create"));
            return true;
        }

        boolean currentReady = party.getReadyStatus().getOrDefault(playerId, false);
        partyManager.setReady(playerId, !currentReady);

        return true;
    }
    
    private boolean handleRewardsCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!sender.hasPermission("ultimatedungeons.rewards")) {
            sender.sendMessage(colorize("&cYou don't have permission to use this command!"));
            return true;
        }

        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&6Available Dungeon Rewards:"));
            for (DungeonTemplate template : dungeonManager.getTemplates()) {
                sender.sendMessage(colorize("&7" + template.getDisplayName() + ":"));
                for (String reward : template.getRewards()) {
                    sender.sendMessage(colorize("  &e- " + reward));
                }
            }
            return true;
        }

        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        UUID playerId = player.getUniqueId();

        if (args.length > 0 && args[0].equalsIgnoreCase("stats")) {
            showPlayerStats(player);
        } else {
            showAvailableRewards(player);
        }

        return true;
    }

    private void showPlayerStats(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();
        PlayerData data = statsManager.getPlayerData(playerId);

        player.sendMessage(colorize("&6=== Your Dungeon Statistics ==="));
        player.sendMessage(colorize("&7Dungeons Completed: &a" + data.getDungeonsCompleted()));
        player.sendMessage(colorize("&7Dungeons Failed: &c" + data.getDungeonsFailed()));

        if (data.getDungeonsCompleted() + data.getDungeonsFailed() > 0) {
            double successRate = (double) data.getDungeonsCompleted() /
                (data.getDungeonsCompleted() + data.getDungeonsFailed()) * 100;
            player.sendMessage(colorize("&7Success Rate: &e" + String.format("%.1f%%", successRate)));
        }

        long hours = data.getTotalPlayTime() / (60 * 60 * 1000);
        long minutes = (data.getTotalPlayTime() % (60 * 60 * 1000)) / (60 * 1000);
        player.sendMessage(colorize("&7Total Play Time: &f" + hours + "h " + minutes + "m"));

        if (!data.getAchievements().isEmpty()) {
            player.sendMessage(colorize("&7Achievements: &6" + data.getAchievements().size()));
            for (String achievement : data.getAchievements()) {
                player.sendMessage(colorize("  &e✓ " + formatAchievementName(achievement)));
            }
        }

        if (!data.getDungeonStats().isEmpty()) {
            player.sendMessage(colorize("&7Dungeon Completions:"));
            for (Map.Entry<String, Integer> entry : data.getDungeonStats().entrySet()) {
                DungeonTemplate template = dungeonManager.getTemplate(entry.getKey());
                String displayName = template != null ? template.getDisplayName() : entry.getKey();
                player.sendMessage(colorize("  &f" + displayName + ": &a" + entry.getValue()));
            }
        }
    }

    private void showAvailableRewards(org.bukkit.entity.Player player) {
        player.sendMessage(colorize("&6=== Available Dungeon Rewards ==="));

        Collection<DungeonTemplate> templates = dungeonManager.getTemplates();
        if (templates.isEmpty()) {
            player.sendMessage(colorize("&cNo dungeons available."));
            return;
        }

        for (DungeonTemplate template : templates) {
            player.sendMessage(colorize("&e" + template.getDisplayName() + " &7(" + template.getDifficulty() + ")"));

            if (!template.getRewards().isEmpty()) {
                for (String reward : template.getRewards()) {
                    player.sendMessage(colorize("  &7- " + formatRewardDisplay(reward)));
                }
            }

            // Show loot table info
            LootTable lootTable = rewardManager.getLootTable(template.getName().toLowerCase());
            if (lootTable == null) {
                lootTable = rewardManager.getLootTable("default");
            }

            if (lootTable != null && !lootTable.getEntries().isEmpty()) {
                player.sendMessage(colorize("  &7Possible loot:"));
                for (LootEntry entry : lootTable.getEntries().values()) {
                    player.sendMessage(colorize("    &f" + entry.getItemId() + " &7x" +
                        entry.getMinAmount() + "-" + entry.getMaxAmount()));
                }
            }

            player.sendMessage(colorize("  &7Experience: &a" + rewardManager.calculateExpReward(template.getName()) + " XP"));
        }

        player.sendMessage(colorize("&7Use &e/ud rewards stats &7to view your statistics"));
    }

    private String formatRewardDisplay(String reward) {
        if (reward.startsWith("money:")) {
            return "Money: $" + reward.substring(6);
        } else if (reward.startsWith("exp:")) {
            return "Experience: " + reward.substring(4) + " XP";
        } else if (reward.startsWith("item:")) {
            String[] parts = reward.split(":");
            if (parts.length >= 3) {
                return parts[2] + "x " + parts[1];
            }
        }
        return reward;
    }

    private String formatAchievementName(String achievement) {
        return Arrays.stream(achievement.split("_"))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
            .reduce((a, b) -> a + " " + b)
            .orElse(achievement);
    }
    
    private void sendHelpMessage(org.bukkit.command.CommandSender sender) {
        sender.sendMessage(colorize("&6=== UltimateDungeons Help ==="));
        sender.sendMessage(colorize("&e/ud gui &7- Open the main GUI menu"));
        sender.sendMessage(colorize("&7/ud join - Join dungeon queue"));
        sender.sendMessage(colorize("&7/ud leave - Leave current dungeon"));
        sender.sendMessage(colorize("&7/ud list - List available dungeons"));
        sender.sendMessage(colorize("&7/party create - Create a party"));
        sender.sendMessage(colorize("&7/ready - Toggle ready status"));
        sender.sendMessage(colorize("&7/rewards - View dungeon rewards"));
    }
    
    private String colorize(String message) {
        if (message == null) return "";
        return org.bukkit.ChatColor.translateAlternateColorCodes('&', message);
    }
    
    private int getDungeonCount() {
        java.io.File dungeonsFolder = new java.io.File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            return 0;
        }
        java.io.File[] files = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        return files != null ? files.length : 0;
    }

    // GUI Methods
    private void openMainGUI(org.bukkit.entity.Player player) {
        org.bukkit.inventory.Inventory gui = org.bukkit.Bukkit.createInventory(null, 27, colorize("&6&lUltimate Dungeons"));

        // Join Queue Button
        org.bukkit.inventory.ItemStack joinItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.DIAMOND_SWORD);
        org.bukkit.inventory.meta.ItemMeta joinMeta = joinItem.getItemMeta();
        if (joinMeta != null) {
            joinMeta.setDisplayName(colorize("&a&lJoin Dungeon Queue"));
            joinMeta.setLore(java.util.Arrays.asList(
                colorize("&7Click to join the dungeon queue"),
                colorize("&7and start your adventure!"),
                "",
                colorize("&eClick to join!")
            ));
            joinItem.setItemMeta(joinMeta);
        }
        gui.setItem(11, joinItem);

        // Dungeon List Button
        org.bukkit.inventory.ItemStack listItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BOOK);
        org.bukkit.inventory.meta.ItemMeta listMeta = listItem.getItemMeta();
        if (listMeta != null) {
            listMeta.setDisplayName(colorize("&b&lAvailable Dungeons"));
            listMeta.setLore(java.util.Arrays.asList(
                colorize("&7View all available dungeons"),
                colorize("&7Total dungeons: &a" + getDungeonCount()),
                "",
                colorize("&eClick to browse!")
            ));
            listItem.setItemMeta(listMeta);
        }
        gui.setItem(13, listItem);

        // Party Management Button
        org.bukkit.inventory.ItemStack partyItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.PLAYER_HEAD);
        org.bukkit.inventory.meta.ItemMeta partyMeta = partyItem.getItemMeta();
        if (partyMeta != null) {
            partyMeta.setDisplayName(colorize("&d&lParty Management"));
            partyMeta.setLore(java.util.Arrays.asList(
                colorize("&7Create or manage your party"),
                colorize("&7Team up with friends!"),
                "",
                colorize("&eClick to manage!")
            ));
            partyItem.setItemMeta(partyMeta);
        }
        gui.setItem(15, partyItem);

        // Rewards Button
        org.bukkit.inventory.ItemStack rewardsItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.CHEST);
        org.bukkit.inventory.meta.ItemMeta rewardsMeta = rewardsItem.getItemMeta();
        if (rewardsMeta != null) {
            rewardsMeta.setDisplayName(colorize("&6&lDungeon Rewards"));
            rewardsMeta.setLore(java.util.Arrays.asList(
                colorize("&7View possible dungeon rewards"),
                colorize("&7and your statistics"),
                "",
                colorize("&eClick to view!")
            ));
            rewardsItem.setItemMeta(rewardsMeta);
        }
        gui.setItem(22, rewardsItem);

        player.openInventory(gui);
    }

    private void openDungeonListGUI(org.bukkit.entity.Player player) {
        org.bukkit.inventory.Inventory gui = org.bukkit.Bukkit.createInventory(null, 54, colorize("&6&lAvailable Dungeons"));

        // Get dungeon files
        java.io.File dungeonsFolder = new java.io.File(getDataFolder(), "dungeons");
        if (!dungeonsFolder.exists()) {
            // No dungeons folder, show empty GUI
            org.bukkit.inventory.ItemStack noItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BARRIER);
            org.bukkit.inventory.meta.ItemMeta noMeta = noItem.getItemMeta();
            if (noMeta != null) {
                noMeta.setDisplayName(colorize("&c&lNo Dungeons Found"));
                noMeta.setLore(java.util.Arrays.asList(
                    colorize("&7No dungeon files were found"),
                    colorize("&7Contact an administrator")
                ));
                noItem.setItemMeta(noMeta);
            }
            gui.setItem(22, noItem);
            player.openInventory(gui);
            return;
        }

        java.io.File[] dungeonFiles = dungeonsFolder.listFiles((dir, name) -> name.endsWith(".schematic"));
        if (dungeonFiles == null || dungeonFiles.length == 0) {
            // No dungeon files
            org.bukkit.inventory.ItemStack noItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BARRIER);
            org.bukkit.inventory.meta.ItemMeta noMeta = noItem.getItemMeta();
            if (noMeta != null) {
                noMeta.setDisplayName(colorize("&c&lNo Dungeons Available"));
                noMeta.setLore(java.util.Arrays.asList(
                    colorize("&7No dungeon schematics found"),
                    colorize("&7Contact an administrator")
                ));
                noItem.setItemMeta(noMeta);
            }
            gui.setItem(22, noItem);
            player.openInventory(gui);
            return;
        }

        // Add dungeon items
        int slot = 0;
        for (java.io.File dungeonFile : dungeonFiles) {
            if (slot >= 45) break; // Leave space for navigation

            String dungeonName = dungeonFile.getName().replace(".schematic", "");
            String displayName = formatDungeonName(dungeonName);

            org.bukkit.inventory.ItemStack dungeonItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.STRUCTURE_BLOCK);
            org.bukkit.inventory.meta.ItemMeta dungeonMeta = dungeonItem.getItemMeta();
            if (dungeonMeta != null) {
                dungeonMeta.setDisplayName(colorize("&a&l" + displayName));
                dungeonMeta.setLore(java.util.Arrays.asList(
                    colorize("&7Dungeon: &f" + displayName),
                    colorize("&7Difficulty: &eNormal"),
                    colorize("&7Players: &a1-5"),
                    "",
                    colorize("&eClick to join this dungeon!")
                ));
                dungeonItem.setItemMeta(dungeonMeta);
            }
            gui.setItem(slot, dungeonItem);
            slot++;
        }

        // Back button
        org.bukkit.inventory.ItemStack backItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.ARROW);
        org.bukkit.inventory.meta.ItemMeta backMeta = backItem.getItemMeta();
        if (backMeta != null) {
            backMeta.setDisplayName(colorize("&c&lBack to Main Menu"));
            backMeta.setLore(java.util.Arrays.asList(
                colorize("&7Return to the main menu")
            ));
            backItem.setItemMeta(backMeta);
        }
        gui.setItem(49, backItem);

        player.openInventory(gui);
    }

    private String formatDungeonName(String fileName) {
        // Convert file names like "castle_fortress" to "Castle Fortress"
        return java.util.Arrays.stream(fileName.split("_"))
            .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
            .reduce((a, b) -> a + " " + b)
            .orElse(fileName);
    }

    // GUI Event Listener
    public class GUIListener implements org.bukkit.event.Listener {

        @org.bukkit.event.EventHandler
        public void onInventoryClick(org.bukkit.event.inventory.InventoryClickEvent event) {
            if (!(event.getWhoClicked() instanceof org.bukkit.entity.Player)) return;

            org.bukkit.entity.Player player = (org.bukkit.entity.Player) event.getWhoClicked();
            String title = event.getView().getTitle();

            // Check if it's one of our GUIs
            if (!title.contains("Ultimate Dungeons") && !title.contains("Available Dungeons")) {
                return;
            }

            event.setCancelled(true); // Prevent item taking

            if (event.getCurrentItem() == null || event.getCurrentItem().getType() == org.bukkit.Material.AIR) {
                return;
            }

            org.bukkit.inventory.ItemStack clickedItem = event.getCurrentItem();
            String itemName = clickedItem.getItemMeta() != null ? clickedItem.getItemMeta().getDisplayName() : "";

            // Handle main GUI clicks
            if (title.contains("Ultimate Dungeons") && !title.contains("Available")) {
                handleMainGUIClick(player, itemName, clickedItem);
            }
            // Handle dungeon list GUI clicks
            else if (title.contains("Available Dungeons")) {
                handleDungeonListGUIClick(player, itemName, clickedItem);
            }
        }

        private void handleMainGUIClick(org.bukkit.entity.Player player, String itemName, org.bukkit.inventory.ItemStack item) {
            if (itemName.contains("Join Dungeon Queue")) {
                player.closeInventory();
                player.performCommand("ud join");
            }
            else if (itemName.contains("Available Dungeons")) {
                openDungeonListGUI(player);
            }
            else if (itemName.contains("Party Management")) {
                player.closeInventory();
                player.performCommand("party create");
            }
            else if (itemName.contains("Dungeon Rewards")) {
                player.closeInventory();
                player.performCommand("ud rewards");
            }
        }

        private void handleDungeonListGUIClick(org.bukkit.entity.Player player, String itemName, org.bukkit.inventory.ItemStack item) {
            if (itemName.contains("Back to Main Menu")) {
                openMainGUI(player);
            }
            else if (item.getType() == org.bukkit.Material.STRUCTURE_BLOCK) {
                // Extract dungeon name and join
                String dungeonName = itemName.replace("§a§l", "").replace("&a&l", "");
                player.closeInventory();
                player.sendMessage(colorize("&aJoining dungeon: &f" + dungeonName));
                player.performCommand("ud join");
            }
        }
    }

    // ===== BUILDER COMMAND HANDLER =====

    private boolean handleBuilderCommand(org.bukkit.command.CommandSender sender, String[] args) {
        if (!(sender instanceof org.bukkit.entity.Player)) {
            sender.sendMessage(colorize("&cOnly players can use builder tools!"));
            return true;
        }

        if (!sender.hasPermission("ultimatedungeons.builder")) {
            sender.sendMessage(colorize("&cYou don't have permission to use builder tools!"));
            return true;
        }

        org.bukkit.entity.Player player = (org.bukkit.entity.Player) sender;
        UUID playerId = player.getUniqueId();

        if (args.length == 0) {
            openBuilderMainGUI(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "gui":
                openBuilderMainGUI(player);
                break;
            case "wand":
                giveBuilderWand(player);
                break;
            case "create":
                return handleCreateDungeon(player, Arrays.copyOfRange(args, 1, args.length));
            case "edit":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /udtools edit <dungeon>"));
                    return true;
                }
                return handleEditDungeon(player, args[1]);
            case "save":
                return handleSaveDungeon(player, Arrays.copyOfRange(args, 1, args.length));
            case "load":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /udtools load <schematic>"));
                    return true;
                }
                return handleLoadSchematic(player, args[1]);
            case "preview":
                if (args.length < 2) {
                    player.sendMessage(colorize("&cUsage: /udtools preview <schematic>"));
                    return true;
                }
                return handlePreviewSchematic(player, args[1]);
            case "place":
                return handlePlaceSchematic(player);
            case "cancel":
                return handleCancelPreview(player);
            case "setstart":
                return handleSetStartPoint(player);
            case "setend":
                return handleSetEndPoint(player);
            case "addchest":
                return handleAddChest(player, Arrays.copyOfRange(args, 1, args.length));
            case "addspawner":
                return handleAddSpawner(player, Arrays.copyOfRange(args, 1, args.length));
            case "addboss":
                return handleAddBoss(player, Arrays.copyOfRange(args, 1, args.length));
            case "test":
                return handleTestDungeon(player);
            case "help":
            default:
                showBuilderHelp(player);
                break;
        }

        return true;
    }

    private void showBuilderHelp(org.bukkit.entity.Player player) {
        player.sendMessage(colorize("&6=== UltimateDungeons Builder Tools ==="));
        player.sendMessage(colorize("&e/udtools gui &7- Open builder interface"));
        player.sendMessage(colorize("&e/udtools wand &7- Get selection wand"));
        player.sendMessage(colorize("&e/udtools create <name> &7- Create new dungeon"));
        player.sendMessage(colorize("&e/udtools edit <dungeon> &7- Edit existing dungeon"));
        player.sendMessage(colorize("&e/udtools save <name> &7- Save current build"));
        player.sendMessage(colorize("&e/udtools load <schematic> &7- Load schematic"));
        player.sendMessage(colorize("&e/udtools preview <schematic> &7- 3D preview"));
        player.sendMessage(colorize("&e/udtools place &7- Place previewed schematic"));
        player.sendMessage(colorize("&e/udtools setstart &7- Set dungeon start point"));
        player.sendMessage(colorize("&e/udtools setend &7- Set dungeon end point"));
        player.sendMessage(colorize("&e/udtools addchest <loot_table> &7- Add loot chest"));
        player.sendMessage(colorize("&e/udtools addspawner <mob> &7- Add mob spawner"));
        player.sendMessage(colorize("&e/udtools addboss <boss> &7- Add boss spawner"));
        player.sendMessage(colorize("&e/udtools test &7- Test current dungeon"));
    }

    // ===== BUILDER HANDLER METHODS =====

    private void openBuilderMainGUI(org.bukkit.entity.Player player) {
        org.bukkit.inventory.Inventory gui = org.bukkit.Bukkit.createInventory(null, 54, colorize("&6&lDungeon Builder Tools"));

        // Builder Wand
        org.bukkit.inventory.ItemStack wandItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.GOLDEN_AXE);
        org.bukkit.inventory.meta.ItemMeta wandMeta = wandItem.getItemMeta();
        if (wandMeta != null) {
            wandMeta.setDisplayName(colorize("&6&lBuilder Wand"));
            wandMeta.setLore(Arrays.asList(
                colorize("&7Get the builder wand for"),
                colorize("&7selecting areas and building"),
                "",
                colorize("&eClick to get!")
            ));
            wandItem.setItemMeta(wandMeta);
        }
        gui.setItem(10, wandItem);

        // Create New Dungeon
        org.bukkit.inventory.ItemStack createItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.EMERALD_BLOCK);
        org.bukkit.inventory.meta.ItemMeta createMeta = createItem.getItemMeta();
        if (createMeta != null) {
            createMeta.setDisplayName(colorize("&a&lCreate New Dungeon"));
            createMeta.setLore(Arrays.asList(
                colorize("&7Start building a new dungeon"),
                colorize("&7from scratch"),
                "",
                colorize("&eClick to create!")
            ));
            createItem.setItemMeta(createMeta);
        }
        gui.setItem(12, createItem);

        // Load Schematic
        org.bukkit.inventory.ItemStack loadItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.CHEST);
        org.bukkit.inventory.meta.ItemMeta loadMeta = loadItem.getItemMeta();
        if (loadMeta != null) {
            loadMeta.setDisplayName(colorize("&b&lLoad Schematic"));
            loadMeta.setLore(Arrays.asList(
                colorize("&7Load an existing schematic"),
                colorize("&7for editing or placement"),
                "",
                colorize("&eClick to browse!")
            ));
            loadItem.setItemMeta(loadMeta);
        }
        gui.setItem(14, loadItem);

        // Dungeon Blocks
        org.bukkit.inventory.ItemStack blocksItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.SPAWNER);
        org.bukkit.inventory.meta.ItemMeta blocksMeta = blocksItem.getItemMeta();
        if (blocksMeta != null) {
            blocksMeta.setDisplayName(colorize("&d&lDungeon Blocks"));
            blocksMeta.setLore(Arrays.asList(
                colorize("&7Add special dungeon blocks:"),
                colorize("&7- Start/End points"),
                colorize("&7- Loot chests"),
                colorize("&7- Mob spawners"),
                "",
                colorize("&eClick to manage!")
            ));
            blocksItem.setItemMeta(blocksMeta);
        }
        gui.setItem(16, blocksItem);

        // Current Build Session Info
        BuildSession session = activeBuildSessions.get(player.getUniqueId());
        if (session != null) {
            org.bukkit.inventory.ItemStack sessionItem = new org.bukkit.inventory.ItemStack(org.bukkit.Material.PAPER);
            org.bukkit.inventory.meta.ItemMeta sessionMeta = sessionItem.getItemMeta();
            if (sessionMeta != null) {
                sessionMeta.setDisplayName(colorize("&e&lCurrent Session"));
                sessionMeta.setLore(Arrays.asList(
                    colorize("&7Dungeon: &f" + session.getDungeonName()),
                    colorize("&7Mode: &f" + session.getMode()),
                    colorize("&7Selection: " + (session.hasSelection() ? "&aYes" : "&cNo")),
                    colorize("&7Volume: &f" + session.getVolume() + " blocks"),
                    colorize("&7Changes: " + (session.hasChanges() ? "&eUnsaved" : "&aSaved")),
                    "",
                    colorize("&eClick for options!")
                ));
                sessionItem.setItemMeta(sessionMeta);
            }
            gui.setItem(22, sessionItem);
        }

        player.openInventory(gui);
    }

    private void giveBuilderWand(org.bukkit.entity.Player player) {
        builderManager.giveBuilderWand(player);
    }

    private boolean handleCreateDungeon(org.bukkit.entity.Player player, String[] args) {
        if (args.length == 0) {
            player.sendMessage(colorize("&cUsage: /udtools create <name>"));
            return true;
        }

        String dungeonName = args[0];
        UUID playerId = player.getUniqueId();

        // Check if already in a build session
        if (activeBuildSessions.containsKey(playerId)) {
            player.sendMessage(colorize("&cYou already have an active build session!"));
            player.sendMessage(colorize("&7Use &e/udtools save &7to save it first."));
            return true;
        }

        builderManager.createBuildSession(playerId, dungeonName);
        return true;
    }

    private boolean handleEditDungeon(org.bukkit.entity.Player player, String dungeonName) {
        // Load existing dungeon for editing
        DungeonTemplate template = dungeonManager.getTemplate(dungeonName);
        if (template == null) {
            player.sendMessage(colorize("&cDungeon not found: " + dungeonName));
            return true;
        }

        UUID playerId = player.getUniqueId();
        BuildSession session = builderManager.createBuildSession(playerId, dungeonName);

        // Load existing dungeon data
        loadDungeonForEditing(session, dungeonName);

        player.sendMessage(colorize("&aLoaded dungeon for editing: &f" + template.getDisplayName()));
        return true;
    }

    private void loadDungeonForEditing(BuildSession session, String dungeonName) {
        // Load dungeon blocks and configuration
        File blocksFile = new File(getDataFolder(), "dungeons/" + dungeonName + "_blocks.yml");
        if (blocksFile.exists()) {
            try {
                org.bukkit.configuration.file.YamlConfiguration config =
                    org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(blocksFile);

                if (config.contains("blocks")) {
                    for (String key : config.getConfigurationSection("blocks").getKeys(false)) {
                        String path = "blocks." + key;

                        double x = config.getDouble(path + ".x");
                        double y = config.getDouble(path + ".y");
                        double z = config.getDouble(path + ".z");
                        org.bukkit.Location location = new org.bukkit.Location(session.getPos1().getWorld(), x, y, z);

                        DungeonBlockType type = DungeonBlockType.valueOf(config.getString(path + ".type"));
                        DungeonBlock block = new DungeonBlock(location, type);

                        if (config.contains(path + ".loot-table")) {
                            block.setLootTable(config.getString(path + ".loot-table"));
                        }

                        if (config.contains(path + ".mob-type")) {
                            block.setMobType(config.getString(path + ".mob-type"));
                            block.setBoss(config.getBoolean(path + ".is-boss", false));
                        }

                        session.getDungeonBlocks().add(block);
                        dungeonBlocks.put(location, block);
                    }
                }
            } catch (Exception e) {
                getLogger().warning("Failed to load dungeon blocks: " + e.getMessage());
            }
        }
    }

    private boolean handleSaveDungeon(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        String fileName = args.length > 0 ? args[0] : session.getDungeonName();
        builderManager.saveBuildSession(playerId, fileName);
        return true;
    }

    private boolean handleLoadSchematic(org.bukkit.entity.Player player, String schematicName) {
        File schematicFile = new File(getDataFolder(), "dungeons/" + schematicName + ".schematic");
        if (!schematicFile.exists()) {
            schematicFile = new File(getDataFolder(), "dungeons/" + schematicName + ".schem");
        }

        if (!schematicFile.exists()) {
            player.sendMessage(colorize("&cSchematic not found: " + schematicName));
            return true;
        }

        player.sendMessage(colorize("&aLoaded schematic: &f" + schematicName));
        player.sendMessage(colorize("&7Use &e/udtools preview " + schematicName + " &7to preview it"));
        return true;
    }

    private boolean handlePreviewSchematic(org.bukkit.entity.Player player, String schematicName) {
        File schematicFile = new File(getDataFolder(), "dungeons/" + schematicName + ".schematic");
        if (!schematicFile.exists()) {
            schematicFile = new File(getDataFolder(), "dungeons/" + schematicName + ".schem");
        }

        if (!schematicFile.exists()) {
            player.sendMessage(colorize("&cSchematic not found: " + schematicName));
            return true;
        }

        org.bukkit.Location previewLocation = player.getLocation();
        builderManager.createPreview(player.getUniqueId(), schematicName, previewLocation);
        return true;
    }

    private boolean handlePlaceSchematic(org.bukkit.entity.Player player) {
        builderManager.placeSchematic(player.getUniqueId());
        return true;
    }

    private boolean handleCancelPreview(org.bukkit.entity.Player player) {
        builderManager.removePreview(player.getUniqueId());
        return true;
    }

    private boolean handleSetStartPoint(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        org.bukkit.Location location = player.getLocation();
        session.setStartPoint(location);

        // Add visual marker
        DungeonBlock startBlock = new DungeonBlock(location, DungeonBlockType.START_POINT);
        session.getDungeonBlocks().add(startBlock);
        dungeonBlocks.put(location, startBlock);

        player.sendMessage(colorize("&aSet dungeon start point at your location!"));
        return true;
    }

    private boolean handleSetEndPoint(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        org.bukkit.Location location = player.getLocation();
        session.setEndPoint(location);

        // Add visual marker
        DungeonBlock endBlock = new DungeonBlock(location, DungeonBlockType.END_POINT);
        session.getDungeonBlocks().add(endBlock);
        dungeonBlocks.put(location, endBlock);

        player.sendMessage(colorize("&aSet dungeon end point at your location!"));
        return true;
    }

    private boolean handleAddChest(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        String lootTable = args.length > 0 ? args[0] : "default";
        org.bukkit.Location location = player.getLocation();

        // Place chest block
        location.getBlock().setType(org.bukkit.Material.CHEST);

        // Add dungeon block
        DungeonBlock chestBlock = new DungeonBlock(location, DungeonBlockType.LOOT_CHEST);
        chestBlock.setLootTable(lootTable);
        session.getDungeonBlocks().add(chestBlock);
        dungeonBlocks.put(location, chestBlock);

        player.sendMessage(colorize("&aAdded loot chest with table: &f" + lootTable));
        return true;
    }

    private boolean handleAddSpawner(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        String mobType = args.length > 0 ? args[0] : "ZOMBIE";
        org.bukkit.Location location = player.getLocation();

        // Place spawner block
        location.getBlock().setType(org.bukkit.Material.SPAWNER);

        // Add dungeon block
        DungeonBlock spawnerBlock = new DungeonBlock(location, DungeonBlockType.MOB_SPAWNER);
        spawnerBlock.setMobType(mobType.toUpperCase());
        spawnerBlock.setBoss(false);
        session.getDungeonBlocks().add(spawnerBlock);
        dungeonBlocks.put(location, spawnerBlock);

        player.sendMessage(colorize("&aAdded mob spawner: &f" + mobType));
        return true;
    }

    private boolean handleAddBoss(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        String bossType = args.length > 0 ? args[0] : "WITHER_SKELETON";
        org.bukkit.Location location = player.getLocation();

        // Place boss spawner block
        location.getBlock().setType(org.bukkit.Material.SPAWNER);

        // Add dungeon block
        DungeonBlock bossBlock = new DungeonBlock(location, DungeonBlockType.BOSS_SPAWNER);
        bossBlock.setMobType(bossType.toUpperCase());
        bossBlock.setBoss(true);
        session.getDungeonBlocks().add(bossBlock);
        dungeonBlocks.put(location, bossBlock);

        player.sendMessage(colorize("&aAdded boss spawner: &f" + bossType));
        return true;
    }

    private boolean handleTestDungeon(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();
        BuildSession session = activeBuildSessions.get(playerId);

        if (session == null) {
            player.sendMessage(colorize("&cYou don't have an active build session!"));
            return true;
        }

        if (!session.hasSelection()) {
            player.sendMessage(colorize("&cYou must select an area first!"));
            return true;
        }

        if (session.getStartPoint() == null) {
            player.sendMessage(colorize("&cYou must set a start point first!"));
            return true;
        }

        // Create test instance
        List<UUID> testPlayers = Arrays.asList(playerId);
        DungeonInstance testInstance = dungeonManager.createInstance(session.getDungeonName(), testPlayers);

        if (testInstance != null) {
            player.sendMessage(colorize("&aStarted test run of your dungeon!"));
            session.setMode(BuildMode.TESTING);
        } else {
            player.sendMessage(colorize("&cFailed to start test run!"));
        }

        return true;
    }

    // ===== QUEUE MANAGEMENT METHODS =====

    private boolean handleJoinQueue(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();

        // Check if player is already in queue
        if (queueManager.isPlayerInQueue(playerId)) {
            player.sendMessage(colorize("&cYou are already in the dungeon queue!"));
            player.sendMessage(colorize("&7Position: &e" + queueManager.getQueuePosition(playerId)));
            return true;
        }

        // Check cooldown
        if (queueManager.isPlayerOnCooldown(playerId)) {
            long remaining = queueManager.getCooldownRemaining(playerId);
            long minutes = remaining / (60 * 1000);
            long seconds = (remaining % (60 * 1000)) / 1000;
            player.sendMessage(colorize("&cYou must wait " + minutes + "m " + seconds + "s before joining another dungeon!"));
            return true;
        }

        String dungeonName = null;
        String difficulty = "normal";

        // Parse arguments
        if (args.length > 0) {
            dungeonName = args[0];
            if (args.length > 1) {
                difficulty = args[1];
            }
        }

        // Check if player is in a party
        Party party = partyManager.getPlayerParty(playerId);
        if (party != null) {
            // Party leader can queue the whole party
            if (party.getLeaderId().equals(playerId)) {
                if (!party.isAllReady()) {
                    player.sendMessage(colorize("&cAll party members must be ready before joining queue!"));
                    player.sendMessage(colorize("&7Use &e/ready &7to toggle ready status"));
                    return true;
                }

                if (queueManager.addPartyToQueue(party.getPartyId(), dungeonName, difficulty)) {
                    player.sendMessage(colorize("&aParty added to dungeon queue!"));
                } else {
                    player.sendMessage(colorize("&cFailed to join queue. Check that all members are available."));
                }
            } else {
                player.sendMessage(colorize("&cOnly the party leader can queue the party for dungeons!"));
                player.sendMessage(colorize("&7Ask your party leader to use &e/ud join"));
            }
        } else {
            // Solo queue
            if (queueManager.addToQueue(playerId, dungeonName, difficulty)) {
                player.sendMessage(colorize("&aAdded to dungeon queue!"));
                if (dungeonName != null) {
                    player.sendMessage(colorize("&7Searching for: &f" + dungeonName + " &7(" + difficulty + ")"));
                } else {
                    player.sendMessage(colorize("&7Searching for any available dungeon"));
                }
            } else {
                player.sendMessage(colorize("&cFailed to join queue. You may already be queued."));
            }
        }

        return true;
    }

    private boolean handleLeaveQueue(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        if (queueManager.removeFromQueue(playerId)) {
            player.sendMessage(colorize("&cRemoved from dungeon queue."));
        } else {
            player.sendMessage(colorize("&cYou are not in the dungeon queue!"));
        }

        return true;
    }

    private void showQueueStatus(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        if (queueManager.isPlayerInQueue(playerId)) {
            int position = queueManager.getQueuePosition(playerId);
            int totalSize = queueManager.getQueueSize();

            player.sendMessage(colorize("&6=== Queue Status ==="));
            player.sendMessage(colorize("&7Position: &e" + position + "&7/&e" + totalSize));
            player.sendMessage(colorize("&7Use &c/ud leave &7to exit queue"));
        } else if (queueManager.isPlayerOnCooldown(playerId)) {
            long remaining = queueManager.getCooldownRemaining(playerId);
            long minutes = remaining / (60 * 1000);
            long seconds = (remaining % (60 * 1000)) / 1000;

            player.sendMessage(colorize("&6=== Cooldown Active ==="));
            player.sendMessage(colorize("&7Time remaining: &e" + minutes + "m " + seconds + "s"));
        } else {
            player.sendMessage(colorize("&6=== Available for Queue ==="));
            player.sendMessage(colorize("&7Use &a/ud join &7to enter dungeon queue"));

            Party party = partyManager.getPlayerParty(playerId);
            if (party != null) {
                boolean allReady = party.isAllReady();
                player.sendMessage(colorize("&7Party status: " + (allReady ? "&aReady" : "&cNot all ready")));
                if (!allReady) {
                    player.sendMessage(colorize("&7Use &e/ready &7to toggle ready status"));
                }
            }
        }
    }

    // ===== PARTY HELPER METHODS =====

    private boolean handlePartyCreate(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        if (partyManager.getPlayerParty(playerId) != null) {
            player.sendMessage(colorize("&cYou are already in a party! Leave your current party first."));
            return true;
        }

        Party party = partyManager.createParty(playerId);
        player.sendMessage(colorize("&a&lParty Created! &7You are now the party leader."));
        player.sendMessage(colorize("&7Use &e/party invite <player> &7to invite others."));
        player.sendMessage(colorize("&7Party ID: &f" + party.getPartyId().toString().substring(0, 8)));
        return true;
    }

    private boolean handlePartyInvite(org.bukkit.entity.Player player, String targetName) {
        UUID playerId = player.getUniqueId();
        Party party = partyManager.getPlayerParty(playerId);

        if (party == null) {
            player.sendMessage(colorize("&cYou are not in a party! Create one with /party create"));
            return true;
        }

        if (!party.getLeaderId().equals(playerId)) {
            player.sendMessage(colorize("&cOnly the party leader can invite players!"));
            return true;
        }

        org.bukkit.entity.Player target = org.bukkit.Bukkit.getPlayer(targetName);
        if (target == null) {
            player.sendMessage(colorize("&cPlayer not found: " + targetName));
            return true;
        }

        if (target.getUniqueId().equals(playerId)) {
            player.sendMessage(colorize("&cYou cannot invite yourself!"));
            return true;
        }

        if (partyManager.getPlayerParty(target.getUniqueId()) != null) {
            player.sendMessage(colorize("&c" + targetName + " is already in a party!"));
            return true;
        }

        if (partyManager.invitePlayer(party.getPartyId(), target.getUniqueId())) {
            player.sendMessage(colorize("&aInvited &f" + targetName + " &ato your party!"));
        } else {
            player.sendMessage(colorize("&cFailed to invite " + targetName + ". Party may be full or invites disabled."));
        }

        return true;
    }

    private boolean handlePartyAccept(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        // Find party with pending invite
        Party invitingParty = null;
        for (Party party : partyManager.getAllParties()) {
            if (party.getInvites().contains(playerId)) {
                invitingParty = party;
                break;
            }
        }

        if (invitingParty == null) {
            player.sendMessage(colorize("&cYou have no pending party invites!"));
            return true;
        }

        if (partyManager.acceptInvite(playerId, invitingParty.getPartyId())) {
            player.sendMessage(colorize("&aYou joined the party!"));
        } else {
            player.sendMessage(colorize("&cFailed to join party. It may be full."));
        }

        return true;
    }

    private boolean handlePartyDecline(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        // Find and remove all pending invites
        boolean hadInvites = false;
        for (Party party : partyManager.getAllParties()) {
            if (party.getInvites().remove(playerId)) {
                hadInvites = true;

                // Notify party leader
                org.bukkit.entity.Player leader = org.bukkit.Bukkit.getPlayer(party.getLeaderId());
                if (leader != null) {
                    leader.sendMessage(colorize("&c" + player.getName() + " declined your party invite."));
                }
            }
        }

        if (hadInvites) {
            player.sendMessage(colorize("&cDeclined all party invites."));
        } else {
            player.sendMessage(colorize("&cYou have no pending party invites!"));
        }

        return true;
    }

    private boolean handlePartyLeave(org.bukkit.entity.Player player) {
        UUID playerId = player.getUniqueId();

        if (partyManager.leaveParty(playerId)) {
            player.sendMessage(colorize("&cYou left the party."));
        } else {
            player.sendMessage(colorize("&cYou are not in a party!"));
        }

        return true;
    }

    private boolean handlePartyKick(org.bukkit.entity.Player player, String targetName) {
        UUID playerId = player.getUniqueId();
        Party party = partyManager.getPlayerParty(playerId);

        if (party == null) {
            player.sendMessage(colorize("&cYou are not in a party!"));
            return true;
        }

        if (!party.getLeaderId().equals(playerId)) {
            player.sendMessage(colorize("&cOnly the party leader can kick players!"));
            return true;
        }

        org.bukkit.entity.Player target = org.bukkit.Bukkit.getPlayer(targetName);
        if (target == null) {
            player.sendMessage(colorize("&cPlayer not found: " + targetName));
            return true;
        }

        UUID targetId = target.getUniqueId();
        if (!party.getMembers().contains(targetId)) {
            player.sendMessage(colorize("&c" + targetName + " is not in your party!"));
            return true;
        }

        if (targetId.equals(playerId)) {
            player.sendMessage(colorize("&cYou cannot kick yourself! Use /party leave instead."));
            return true;
        }

        if (partyManager.leaveParty(targetId)) {
            player.sendMessage(colorize("&cKicked " + targetName + " from the party."));
            target.sendMessage(colorize("&cYou were kicked from the party by " + player.getName()));
        }

        return true;
    }

    private boolean handlePartyPromote(org.bukkit.entity.Player player, String targetName) {
        UUID playerId = player.getUniqueId();
        Party party = partyManager.getPlayerParty(playerId);

        if (party == null) {
            player.sendMessage(colorize("&cYou are not in a party!"));
            return true;
        }

        if (!party.getLeaderId().equals(playerId)) {
            player.sendMessage(colorize("&cOnly the party leader can promote players!"));
            return true;
        }

        org.bukkit.entity.Player target = org.bukkit.Bukkit.getPlayer(targetName);
        if (target == null) {
            player.sendMessage(colorize("&cPlayer not found: " + targetName));
            return true;
        }

        UUID targetId = target.getUniqueId();
        if (!party.getMembers().contains(targetId)) {
            player.sendMessage(colorize("&c" + targetName + " is not in your party!"));
            return true;
        }

        party.setLeaderId(targetId);

        // Notify all party members
        for (UUID memberId : party.getMembers()) {
            org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(colorize("&6" + targetName + " is now the party leader!"));
            }
        }

        return true;
    }

    private boolean handlePartySettings(org.bukkit.entity.Player player, String[] args) {
        UUID playerId = player.getUniqueId();
        Party party = partyManager.getPlayerParty(playerId);

        if (party == null) {
            player.sendMessage(colorize("&cYou are not in a party!"));
            return true;
        }

        if (!party.getLeaderId().equals(playerId)) {
            player.sendMessage(colorize("&cOnly the party leader can change settings!"));
            return true;
        }

        if (args.length == 0) {
            showPartySettings(player, party);
            return true;
        }

        String setting = args[0].toLowerCase();
        switch (setting) {
            case "public":
                if (args.length > 1) {
                    boolean isPublic = Boolean.parseBoolean(args[1]);
                    party.getSettings().setPublicParty(isPublic);
                    player.sendMessage(colorize("&aParty is now " + (isPublic ? "public" : "private")));
                } else {
                    player.sendMessage(colorize("&cUsage: /party settings public <true/false>"));
                }
                break;
            case "invites":
                if (args.length > 1) {
                    boolean allowInvites = Boolean.parseBoolean(args[1]);
                    party.getSettings().setAllowInvites(allowInvites);
                    player.sendMessage(colorize("&aInvites are now " + (allowInvites ? "enabled" : "disabled")));
                } else {
                    player.sendMessage(colorize("&cUsage: /party settings invites <true/false>"));
                }
                break;
            case "maxsize":
                if (args.length > 1) {
                    try {
                        int maxSize = Integer.parseInt(args[1]);
                        if (maxSize >= party.getSize() && maxSize <= 10) {
                            party.getSettings().setMaxSize(maxSize);
                            player.sendMessage(colorize("&aMax party size set to " + maxSize));
                        } else {
                            player.sendMessage(colorize("&cMax size must be between " + party.getSize() + " and 10"));
                        }
                    } catch (NumberFormatException e) {
                        player.sendMessage(colorize("&cInvalid number: " + args[1]));
                    }
                } else {
                    player.sendMessage(colorize("&cUsage: /party settings maxsize <number>"));
                }
                break;
            default:
                showPartySettings(player, party);
                break;
        }

        return true;
    }

    private boolean handlePartyList(org.bukkit.entity.Player player) {
        Collection<Party> allParties = partyManager.getAllParties();

        if (allParties.isEmpty()) {
            player.sendMessage(colorize("&cNo active parties found."));
            return true;
        }

        player.sendMessage(colorize("&6=== Active Parties ==="));
        int count = 0;
        for (Party party : allParties) {
            if (party.getSettings().isPublicParty()) {
                org.bukkit.entity.Player leader = org.bukkit.Bukkit.getPlayer(party.getLeaderId());
                String leaderName = leader != null ? leader.getName() : "Unknown";

                player.sendMessage(colorize("&7" + (++count) + ". &f" + leaderName + "'s party &7(" +
                    party.getSize() + "/" + party.getSettings().getMaxSize() + ")"));
            }
        }

        if (count == 0) {
            player.sendMessage(colorize("&cNo public parties found."));
        }

        return true;
    }

    private void showPartyInfo(org.bukkit.entity.Player player, Party party) {
        player.sendMessage(colorize("&6=== Party Information ==="));

        org.bukkit.entity.Player leader = org.bukkit.Bukkit.getPlayer(party.getLeaderId());
        String leaderName = leader != null ? leader.getName() : "Unknown";
        player.sendMessage(colorize("&7Leader: &f" + leaderName));
        player.sendMessage(colorize("&7Size: &f" + party.getSize() + "/" + party.getSettings().getMaxSize()));
        player.sendMessage(colorize("&7Created: &f" + formatTime(party.getCreatedTime())));

        player.sendMessage(colorize("&7Members:"));
        for (UUID memberId : party.getMembers()) {
            org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
            String memberName = member != null ? member.getName() : "Unknown";
            boolean isReady = party.getReadyStatus().getOrDefault(memberId, false);
            String readyStatus = isReady ? "&a✓" : "&c✗";
            String roleIndicator = memberId.equals(party.getLeaderId()) ? " &6[Leader]" : "";

            player.sendMessage(colorize("  " + readyStatus + " &f" + memberName + roleIndicator));
        }

        if (!party.getInvites().isEmpty()) {
            player.sendMessage(colorize("&7Pending Invites: &f" + party.getInvites().size()));
        }
    }

    private void showPartyHelp(org.bukkit.entity.Player player) {
        player.sendMessage(colorize("&6=== Party Commands ==="));
        player.sendMessage(colorize("&e/party create &7- Create a new party"));
        player.sendMessage(colorize("&e/party invite <player> &7- Invite a player"));
        player.sendMessage(colorize("&e/party accept &7- Accept party invite"));
        player.sendMessage(colorize("&e/party decline &7- Decline party invite"));
        player.sendMessage(colorize("&e/party leave &7- Leave your party"));
        player.sendMessage(colorize("&e/party kick <player> &7- Kick a player (leader only)"));
        player.sendMessage(colorize("&e/party promote <player> &7- Promote to leader"));
        player.sendMessage(colorize("&e/party settings &7- View/change party settings"));
        player.sendMessage(colorize("&e/party list &7- List public parties"));
        player.sendMessage(colorize("&e/party info &7- Show party information"));
    }

    private void showPartySettings(org.bukkit.entity.Player player, Party party) {
        PartySettings settings = party.getSettings();
        player.sendMessage(colorize("&6=== Party Settings ==="));
        player.sendMessage(colorize("&7Public Party: &f" + settings.isPublicParty()));
        player.sendMessage(colorize("&7Allow Invites: &f" + settings.isAllowInvites()));
        player.sendMessage(colorize("&7Max Size: &f" + settings.getMaxSize()));
        player.sendMessage(colorize("&7Preferred Difficulty: &f" + settings.getPreferredDifficulty()));
        player.sendMessage(colorize("&7Use &e/party settings <setting> <value> &7to change"));
    }

    private String formatTime(long timestamp) {
        long diff = System.currentTimeMillis() - timestamp;
        long minutes = diff / (60 * 1000);
        if (minutes < 60) {
            return minutes + " minutes ago";
        } else {
            long hours = minutes / 60;
            return hours + " hours ago";
        }
    }

    // ===== DATA CLASSES =====

    public static class DungeonTemplate {
        private String name;
        private String displayName;
        private String description;
        private int minPlayers;
        private int maxPlayers;
        private int timeLimit;
        private String difficulty;
        private List<String> rewards;
        private File schematicFile;
        private Map<String, Object> settings;

        public DungeonTemplate(String name, File schematicFile) {
            this.name = name;
            this.schematicFile = schematicFile;
            this.displayName = formatName(name);
            this.description = "A challenging dungeon adventure";
            this.minPlayers = 1;
            this.maxPlayers = 5;
            this.timeLimit = 1800; // 30 minutes
            this.difficulty = "Normal";
            this.rewards = new ArrayList<>();
            this.settings = new HashMap<>();
        }

        private String formatName(String name) {
            return Arrays.stream(name.split("_"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .reduce((a, b) -> a + " " + b)
                .orElse(name);
        }

        // Getters and setters
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        public int getMinPlayers() { return minPlayers; }
        public int getMaxPlayers() { return maxPlayers; }
        public int getTimeLimit() { return timeLimit; }
        public String getDifficulty() { return difficulty; }
        public List<String> getRewards() { return rewards; }
        public File getSchematicFile() { return schematicFile; }
        public Map<String, Object> getSettings() { return settings; }

        public void setDisplayName(String displayName) { this.displayName = displayName; }
        public void setDescription(String description) { this.description = description; }
        public void setMinPlayers(int minPlayers) { this.minPlayers = minPlayers; }
        public void setMaxPlayers(int maxPlayers) { this.maxPlayers = maxPlayers; }
        public void setTimeLimit(int timeLimit) { this.timeLimit = timeLimit; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
        public void setRewards(List<String> rewards) { this.rewards = rewards; }
    }

    public static class DungeonInstance {
        private UUID instanceId;
        private String templateName;
        private List<UUID> players;
        private UUID partyId;
        private long startTime;
        private long endTime;
        private DungeonState state;
        private Map<String, Object> data;
        private org.bukkit.World world;

        public DungeonInstance(String templateName, List<UUID> players) {
            this.instanceId = UUID.randomUUID();
            this.templateName = templateName;
            this.players = new ArrayList<>(players);
            this.startTime = System.currentTimeMillis();
            this.state = DungeonState.STARTING;
            this.data = new HashMap<>();
        }

        // Getters and setters
        public UUID getInstanceId() { return instanceId; }
        public String getTemplateName() { return templateName; }
        public List<UUID> getPlayers() { return players; }
        public UUID getPartyId() { return partyId; }
        public long getStartTime() { return startTime; }
        public long getEndTime() { return endTime; }
        public DungeonState getState() { return state; }
        public Map<String, Object> getData() { return data; }
        public org.bukkit.World getWorld() { return world; }

        public void setPartyId(UUID partyId) { this.partyId = partyId; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public void setState(DungeonState state) { this.state = state; }
        public void setWorld(org.bukkit.World world) { this.world = world; }
    }

    public enum DungeonState {
        STARTING, ACTIVE, COMPLETED, FAILED, ABANDONED
    }

    public static class Party {
        private UUID partyId;
        private UUID leaderId;
        private List<UUID> members;
        private List<UUID> invites;
        private Map<UUID, Boolean> readyStatus;
        private long createdTime;
        private PartySettings settings;

        public Party(UUID leaderId) {
            this.partyId = UUID.randomUUID();
            this.leaderId = leaderId;
            this.members = new ArrayList<>();
            this.invites = new ArrayList<>();
            this.readyStatus = new HashMap<>();
            this.createdTime = System.currentTimeMillis();
            this.settings = new PartySettings();

            members.add(leaderId);
            readyStatus.put(leaderId, false);
        }

        // Getters and setters
        public UUID getPartyId() { return partyId; }
        public UUID getLeaderId() { return leaderId; }
        public List<UUID> getMembers() { return members; }
        public List<UUID> getInvites() { return invites; }
        public Map<UUID, Boolean> getReadyStatus() { return readyStatus; }
        public long getCreatedTime() { return createdTime; }
        public PartySettings getSettings() { return settings; }

        public void setLeaderId(UUID leaderId) { this.leaderId = leaderId; }

        public boolean isAllReady() {
            return readyStatus.values().stream().allMatch(ready -> ready);
        }

        public int getSize() { return members.size(); }
    }

    public static class PartySettings {
        private boolean publicParty = false;
        private boolean allowInvites = true;
        private int maxSize = 5;
        private String preferredDifficulty = "Normal";

        // Getters and setters
        public boolean isPublicParty() { return publicParty; }
        public boolean isAllowInvites() { return allowInvites; }
        public int getMaxSize() { return maxSize; }
        public String getPreferredDifficulty() { return preferredDifficulty; }

        public void setPublicParty(boolean publicParty) { this.publicParty = publicParty; }
        public void setAllowInvites(boolean allowInvites) { this.allowInvites = allowInvites; }
        public void setMaxSize(int maxSize) { this.maxSize = maxSize; }
        public void setPreferredDifficulty(String preferredDifficulty) { this.preferredDifficulty = preferredDifficulty; }
    }

    public static class PlayerData {
        private UUID playerId;
        private String playerName;
        private int dungeonsCompleted;
        private int dungeonsFailed;
        private long totalPlayTime;
        private Map<String, Integer> dungeonStats;
        private List<String> achievements;
        private long lastPlayed;
        private Map<String, Object> customData;

        public PlayerData(UUID playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
            this.dungeonsCompleted = 0;
            this.dungeonsFailed = 0;
            this.totalPlayTime = 0;
            this.dungeonStats = new HashMap<>();
            this.achievements = new ArrayList<>();
            this.lastPlayed = System.currentTimeMillis();
            this.customData = new HashMap<>();
        }

        // Getters and setters
        public UUID getPlayerId() { return playerId; }
        public String getPlayerName() { return playerName; }
        public int getDungeonsCompleted() { return dungeonsCompleted; }
        public int getDungeonsFailed() { return dungeonsFailed; }
        public long getTotalPlayTime() { return totalPlayTime; }
        public Map<String, Integer> getDungeonStats() { return dungeonStats; }
        public List<String> getAchievements() { return achievements; }
        public long getLastPlayed() { return lastPlayed; }
        public Map<String, Object> getCustomData() { return customData; }

        public void setPlayerName(String playerName) { this.playerName = playerName; }
        public void setDungeonsCompleted(int dungeonsCompleted) { this.dungeonsCompleted = dungeonsCompleted; }
        public void setDungeonsFailed(int dungeonsFailed) { this.dungeonsFailed = dungeonsFailed; }
        public void setTotalPlayTime(long totalPlayTime) { this.totalPlayTime = totalPlayTime; }
        public void setLastPlayed(long lastPlayed) { this.lastPlayed = lastPlayed; }

        public void incrementCompleted() { this.dungeonsCompleted++; }
        public void incrementFailed() { this.dungeonsFailed++; }
        public void addPlayTime(long time) { this.totalPlayTime += time; }
    }

    public static class QueueEntry {
        private UUID playerId;
        private UUID partyId;
        private String preferredDungeon;
        private String difficulty;
        private long queueTime;
        private QueueType type;

        public QueueEntry(UUID playerId, String preferredDungeon, String difficulty) {
            this.playerId = playerId;
            this.preferredDungeon = preferredDungeon;
            this.difficulty = difficulty;
            this.queueTime = System.currentTimeMillis();
            this.type = QueueType.SOLO;
        }

        public QueueEntry(UUID partyId, UUID playerId, String preferredDungeon, String difficulty) {
            this.partyId = partyId;
            this.playerId = playerId;
            this.preferredDungeon = preferredDungeon;
            this.difficulty = difficulty;
            this.queueTime = System.currentTimeMillis();
            this.type = QueueType.PARTY;
        }

        // Getters
        public UUID getPlayerId() { return playerId; }
        public UUID getPartyId() { return partyId; }
        public String getPreferredDungeon() { return preferredDungeon; }
        public String getDifficulty() { return difficulty; }
        public long getQueueTime() { return queueTime; }
        public QueueType getType() { return type; }

        public long getWaitTime() { return System.currentTimeMillis() - queueTime; }
    }

    public enum QueueType {
        SOLO, PARTY
    }

    public static class LootTable {
        private String name;
        private Map<String, LootEntry> entries;
        private double totalWeight;

        public LootTable(String name) {
            this.name = name;
            this.entries = new HashMap<>();
            this.totalWeight = 0.0;
        }

        public void addEntry(String itemId, double weight, int minAmount, int maxAmount) {
            LootEntry entry = new LootEntry(itemId, weight, minAmount, maxAmount);
            entries.put(itemId, entry);
            totalWeight += weight;
        }

        public List<LootDrop> generateLoot(Random random) {
            List<LootDrop> drops = new ArrayList<>();
            for (LootEntry entry : entries.values()) {
                if (random.nextDouble() * totalWeight <= entry.getWeight()) {
                    int amount = entry.getMinAmount() + random.nextInt(entry.getMaxAmount() - entry.getMinAmount() + 1);
                    drops.add(new LootDrop(entry.getItemId(), amount));
                }
            }
            return drops;
        }

        // Getters
        public String getName() { return name; }
        public Map<String, LootEntry> getEntries() { return entries; }
        public double getTotalWeight() { return totalWeight; }
    }

    public static class LootEntry {
        private String itemId;
        private double weight;
        private int minAmount;
        private int maxAmount;

        public LootEntry(String itemId, double weight, int minAmount, int maxAmount) {
            this.itemId = itemId;
            this.weight = weight;
            this.minAmount = minAmount;
            this.maxAmount = maxAmount;
        }

        // Getters
        public String getItemId() { return itemId; }
        public double getWeight() { return weight; }
        public int getMinAmount() { return minAmount; }
        public int getMaxAmount() { return maxAmount; }
    }

    public static class LootDrop {
        private String itemId;
        private int amount;

        public LootDrop(String itemId, int amount) {
            this.itemId = itemId;
            this.amount = amount;
        }

        // Getters
        public String getItemId() { return itemId; }
        public int getAmount() { return amount; }
    }

    // ===== MANAGER CLASSES =====

    public class DungeonManager {

        public void loadDungeonTemplates() {
            File dungeonsFolder = new File(getDataFolder(), "dungeons");
            if (!dungeonsFolder.exists()) {
                dungeonsFolder.mkdirs();
                getLogger().info("Created dungeons folder");
                return;
            }

            File[] schematicFiles = dungeonsFolder.listFiles((dir, name) ->
                name.endsWith(".schematic") || name.endsWith(".schem"));

            if (schematicFiles == null) return;

            for (File file : schematicFiles) {
                String name = file.getName().replaceAll("\\.(schematic|schem)$", "");
                DungeonTemplate template = new DungeonTemplate(name, file);
                loadTemplateConfig(template);
                dungeonTemplates.put(name, template);
                getLogger().info("Loaded dungeon template: " + template.getDisplayName());
            }
        }

        private void loadTemplateConfig(DungeonTemplate template) {
            File configFile = new File(getDataFolder(), "dungeons/" + template.getName() + ".yml");
            if (configFile.exists()) {
                try {
                    org.bukkit.configuration.file.YamlConfiguration config =
                        org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(configFile);

                    template.setDisplayName(config.getString("display-name", template.getDisplayName()));
                    template.setDescription(config.getString("description", template.getDescription()));
                    template.setMinPlayers(config.getInt("min-players", template.getMinPlayers()));
                    template.setMaxPlayers(config.getInt("max-players", template.getMaxPlayers()));
                    template.setTimeLimit(config.getInt("time-limit", template.getTimeLimit()));
                    template.setDifficulty(config.getString("difficulty", template.getDifficulty()));
                    template.setRewards(config.getStringList("rewards"));
                } catch (Exception e) {
                    getLogger().warning("Failed to load config for dungeon: " + template.getName());
                }
            }
        }

        public DungeonInstance createInstance(String templateName, List<UUID> players) {
            DungeonTemplate template = dungeonTemplates.get(templateName);
            if (template == null) return null;

            DungeonInstance instance = new DungeonInstance(templateName, players);

            // Create world for dungeon instance
            String worldName = "dungeon_" + instance.getInstanceId().toString().substring(0, 8);
            org.bukkit.World world = createDungeonWorld(worldName, template);
            instance.setWorld(world);

            activeDungeons.put(instance.getInstanceId(), instance);

            // Teleport players to dungeon
            for (UUID playerId : players) {
                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    teleportToDungeon(player, instance);
                }
            }

            instance.setState(DungeonState.ACTIVE);
            return instance;
        }

        private org.bukkit.World createDungeonWorld(String worldName, DungeonTemplate template) {
            // Create empty world
            org.bukkit.WorldCreator creator = new org.bukkit.WorldCreator(worldName);
            creator.type(org.bukkit.WorldType.FLAT);
            creator.generateStructures(false);
            org.bukkit.World world = creator.createWorld();

            // Load schematic (simplified - would need WorldEdit integration)
            loadSchematic(world, template.getSchematicFile());

            return world;
        }

        private void loadSchematic(org.bukkit.World world, File schematicFile) {
            // Placeholder for schematic loading
            // In a real implementation, this would use WorldEdit API
            getLogger().info("Loading schematic: " + schematicFile.getName() + " into world: " + world.getName());
        }

        private void teleportToDungeon(org.bukkit.entity.Player player, DungeonInstance instance) {
            org.bukkit.Location spawnLocation = new org.bukkit.Location(instance.getWorld(), 0, 100, 0);
            player.teleport(spawnLocation);
            player.sendMessage(colorize("&aWelcome to the dungeon: &f" +
                dungeonTemplates.get(instance.getTemplateName()).getDisplayName()));
        }

        public void completeDungeon(UUID instanceId, boolean success) {
            DungeonInstance instance = activeDungeons.get(instanceId);
            if (instance == null) return;

            instance.setState(success ? DungeonState.COMPLETED : DungeonState.FAILED);
            instance.setEndTime(System.currentTimeMillis());

            // Handle rewards and statistics
            for (UUID playerId : instance.getPlayers()) {
                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    if (success) {
                        rewardManager.giveRewards(player, instance.getTemplateName());
                        statsManager.recordCompletion(playerId, instance.getTemplateName());
                        player.sendMessage(colorize("&a&lDungeon Completed! &7Congratulations!"));
                    } else {
                        statsManager.recordFailure(playerId, instance.getTemplateName());
                        player.sendMessage(colorize("&c&lDungeon Failed! &7Better luck next time!"));
                    }

                    // Teleport back to spawn
                    player.teleport(player.getWorld().getSpawnLocation());
                }
            }

            // Clean up world after delay
            org.bukkit.Bukkit.getScheduler().runTaskLater(UltimateDungeons.this, () -> {
                cleanupDungeonWorld(instance);
            }, 200L); // 10 seconds delay
        }

        private void cleanupDungeonWorld(DungeonInstance instance) {
            if (instance.getWorld() != null) {
                org.bukkit.Bukkit.unloadWorld(instance.getWorld(), false);
                // Delete world files (simplified)
                getLogger().info("Cleaned up dungeon world: " + instance.getWorld().getName());
            }
            activeDungeons.remove(instance.getInstanceId());
        }

        public Collection<DungeonTemplate> getTemplates() {
            return dungeonTemplates.values();
        }

        public DungeonTemplate getTemplate(String name) {
            return dungeonTemplates.get(name);
        }

        public Collection<DungeonInstance> getActiveInstances() {
            return activeDungeons.values();
        }

        public DungeonInstance getInstance(UUID instanceId) {
            return activeDungeons.get(instanceId);
        }
    }

    public class PartyManager {

        public Party createParty(UUID leaderId) {
            Party party = new Party(leaderId);
            parties.put(party.getPartyId(), party);
            return party;
        }

        public boolean invitePlayer(UUID partyId, UUID playerId) {
            Party party = parties.get(partyId);
            if (party == null) return false;

            if (party.getMembers().size() >= party.getSettings().getMaxSize()) {
                return false; // Party full
            }

            if (!party.getSettings().isAllowInvites()) {
                return false; // Invites disabled
            }

            party.getInvites().add(playerId);

            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null) {
                org.bukkit.entity.Player leader = org.bukkit.Bukkit.getPlayer(party.getLeaderId());
                String leaderName = leader != null ? leader.getName() : "Unknown";
                player.sendMessage(colorize("&a&lParty Invite! &7You've been invited to join " + leaderName + "'s party!"));
                player.sendMessage(colorize("&7Use &e/party accept &7to join or &c/party decline &7to refuse"));
            }

            return true;
        }

        public boolean acceptInvite(UUID playerId, UUID partyId) {
            Party party = parties.get(partyId);
            if (party == null || !party.getInvites().contains(playerId)) {
                return false;
            }

            party.getInvites().remove(playerId);
            party.getMembers().add(playerId);
            party.getReadyStatus().put(playerId, false);

            // Notify all party members
            for (UUID memberId : party.getMembers()) {
                org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
                if (member != null) {
                    org.bukkit.entity.Player newMember = org.bukkit.Bukkit.getPlayer(playerId);
                    String newMemberName = newMember != null ? newMember.getName() : "Unknown";
                    member.sendMessage(colorize("&a" + newMemberName + " &7joined the party! &a(" +
                        party.getMembers().size() + "/" + party.getSettings().getMaxSize() + ")"));
                }
            }

            return true;
        }

        public boolean leaveParty(UUID playerId) {
            Party party = getPlayerParty(playerId);
            if (party == null) return false;

            party.getMembers().remove(playerId);
            party.getReadyStatus().remove(playerId);

            if (party.getLeaderId().equals(playerId)) {
                // Transfer leadership or disband
                if (!party.getMembers().isEmpty()) {
                    party.setLeaderId(party.getMembers().get(0));
                    org.bukkit.entity.Player newLeader = org.bukkit.Bukkit.getPlayer(party.getLeaderId());
                    if (newLeader != null) {
                        newLeader.sendMessage(colorize("&6You are now the party leader!"));
                    }
                } else {
                    parties.remove(party.getPartyId());
                    return true;
                }
            }

            // Notify remaining members
            for (UUID memberId : party.getMembers()) {
                org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
                if (member != null) {
                    org.bukkit.entity.Player leftPlayer = org.bukkit.Bukkit.getPlayer(playerId);
                    String leftPlayerName = leftPlayer != null ? leftPlayer.getName() : "Unknown";
                    member.sendMessage(colorize("&c" + leftPlayerName + " &7left the party. &c(" +
                        party.getMembers().size() + "/" + party.getSettings().getMaxSize() + ")"));
                }
            }

            return true;
        }

        public void setReady(UUID playerId, boolean ready) {
            Party party = getPlayerParty(playerId);
            if (party == null) return;

            party.getReadyStatus().put(playerId, ready);

            // Notify party members
            for (UUID memberId : party.getMembers()) {
                org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
                if (member != null) {
                    org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                    String playerName = player != null ? player.getName() : "Unknown";
                    String status = ready ? "&aready" : "&cnot ready";
                    member.sendMessage(colorize("&7" + playerName + " is now " + status));

                    if (party.isAllReady()) {
                        member.sendMessage(colorize("&a&lAll party members are ready! &7Leader can start the dungeon."));
                    }
                }
            }
        }

        public Party getPlayerParty(UUID playerId) {
            return parties.values().stream()
                .filter(party -> party.getMembers().contains(playerId))
                .findFirst()
                .orElse(null);
        }

        public Party getParty(UUID partyId) {
            return parties.get(partyId);
        }

        public Collection<Party> getAllParties() {
            return parties.values();
        }
    }

    public class QueueManager {

        public boolean addToQueue(UUID playerId, String dungeonName, String difficulty) {
            // Check if player is already in queue
            if (isPlayerInQueue(playerId)) {
                return false;
            }

            // Check cooldown
            if (isPlayerOnCooldown(playerId)) {
                return false;
            }

            QueueEntry entry = new QueueEntry(playerId, dungeonName, difficulty);
            dungeonQueue.offer(entry);

            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(colorize("&aAdded to dungeon queue! &7Position: &e" + getQueuePosition(playerId)));
                player.sendMessage(colorize("&7Searching for: &f" + (dungeonName != null ? dungeonName : "Any dungeon")));
            }

            // Try to match immediately
            processQueue();
            return true;
        }

        public boolean addPartyToQueue(UUID partyId, String dungeonName, String difficulty) {
            Party party = partyManager.getParty(partyId);
            if (party == null || !party.isAllReady()) {
                return false;
            }

            // Check if any party member is in queue or on cooldown
            for (UUID memberId : party.getMembers()) {
                if (isPlayerInQueue(memberId) || isPlayerOnCooldown(memberId)) {
                    return false;
                }
            }

            QueueEntry entry = new QueueEntry(partyId, party.getLeaderId(), dungeonName, difficulty);
            dungeonQueue.offer(entry);

            // Notify all party members
            for (UUID memberId : party.getMembers()) {
                org.bukkit.entity.Player member = org.bukkit.Bukkit.getPlayer(memberId);
                if (member != null) {
                    member.sendMessage(colorize("&aParty added to dungeon queue!"));
                }
            }

            processQueue();
            return true;
        }

        public boolean removeFromQueue(UUID playerId) {
            QueueEntry toRemove = null;
            for (QueueEntry entry : dungeonQueue) {
                if (entry.getPlayerId().equals(playerId) ||
                    (entry.getType() == QueueType.PARTY &&
                     partyManager.getParty(entry.getPartyId()).getMembers().contains(playerId))) {
                    toRemove = entry;
                    break;
                }
            }

            if (toRemove != null) {
                dungeonQueue.remove(toRemove);

                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    player.sendMessage(colorize("&cRemoved from dungeon queue."));
                }
                return true;
            }
            return false;
        }

        public void processQueue() {
            List<QueueEntry> processed = new ArrayList<>();

            while (!dungeonQueue.isEmpty()) {
                QueueEntry entry = dungeonQueue.poll();

                if (entry.getType() == QueueType.SOLO) {
                    // Try to match solo player
                    List<UUID> players = Arrays.asList(entry.getPlayerId());
                    if (tryCreateDungeon(players, entry.getPreferredDungeon())) {
                        processed.add(entry);
                    } else {
                        dungeonQueue.offer(entry); // Put back in queue
                        break;
                    }
                } else {
                    // Try to match party
                    Party party = partyManager.getParty(entry.getPartyId());
                    if (party != null && party.isAllReady()) {
                        if (tryCreateDungeon(party.getMembers(), entry.getPreferredDungeon())) {
                            processed.add(entry);
                        } else {
                            dungeonQueue.offer(entry); // Put back in queue
                            break;
                        }
                    }
                }
            }
        }

        private boolean tryCreateDungeon(List<UUID> players, String preferredDungeon) {
            String dungeonName = preferredDungeon;

            // If no preference, pick random available dungeon
            if (dungeonName == null || dungeonName.equals("any")) {
                Collection<DungeonTemplate> templates = dungeonManager.getTemplates();
                if (templates.isEmpty()) return false;

                DungeonTemplate[] templateArray = templates.toArray(new DungeonTemplate[0]);
                dungeonName = templateArray[new Random().nextInt(templateArray.length)].getName();
            }

            DungeonTemplate template = dungeonManager.getTemplate(dungeonName);
            if (template == null) return false;

            // Check player count requirements
            if (players.size() < template.getMinPlayers() || players.size() > template.getMaxPlayers()) {
                return false;
            }

            // Create dungeon instance
            DungeonInstance instance = dungeonManager.createInstance(dungeonName, players);
            if (instance != null) {
                // Set cooldowns
                for (UUID playerId : players) {
                    playerCooldowns.put(playerId, System.currentTimeMillis() + 300000); // 5 minute cooldown
                }
                return true;
            }

            return false;
        }

        public boolean isPlayerInQueue(UUID playerId) {
            return dungeonQueue.stream().anyMatch(entry ->
                entry.getPlayerId().equals(playerId) ||
                (entry.getType() == QueueType.PARTY &&
                 partyManager.getParty(entry.getPartyId()).getMembers().contains(playerId)));
        }

        public boolean isPlayerOnCooldown(UUID playerId) {
            Long cooldownEnd = playerCooldowns.get(playerId);
            if (cooldownEnd == null) return false;

            if (System.currentTimeMillis() >= cooldownEnd) {
                playerCooldowns.remove(playerId);
                return false;
            }
            return true;
        }

        public long getCooldownRemaining(UUID playerId) {
            Long cooldownEnd = playerCooldowns.get(playerId);
            if (cooldownEnd == null) return 0;

            long remaining = cooldownEnd - System.currentTimeMillis();
            return Math.max(0, remaining);
        }

        public int getQueuePosition(UUID playerId) {
            int position = 1;
            for (QueueEntry entry : dungeonQueue) {
                if (entry.getPlayerId().equals(playerId)) {
                    return position;
                }
                position++;
            }
            return -1;
        }

        public int getQueueSize() {
            return dungeonQueue.size();
        }
    }

    public class RewardManager {

        public void loadLootTables() {
            File lootFolder = new File(getDataFolder(), "loot");
            if (!lootFolder.exists()) {
                lootFolder.mkdirs();
                createDefaultLootTables();
                return;
            }

            File[] lootFiles = lootFolder.listFiles((dir, name) -> name.endsWith(".yml"));
            if (lootFiles == null) return;

            for (File file : lootFiles) {
                String name = file.getName().replace(".yml", "");
                loadLootTable(name, file);
            }
        }

        private void createDefaultLootTables() {
            // Create default loot table
            LootTable defaultTable = new LootTable("default");
            defaultTable.addEntry("DIAMOND", 10.0, 1, 3);
            defaultTable.addEntry("GOLD_INGOT", 25.0, 2, 5);
            defaultTable.addEntry("IRON_INGOT", 40.0, 3, 8);
            defaultTable.addEntry("EMERALD", 15.0, 1, 2);
            lootTables.put("default", defaultTable);

            // Save to file
            saveLootTable(defaultTable);
        }

        private void loadLootTable(String name, File file) {
            try {
                org.bukkit.configuration.file.YamlConfiguration config =
                    org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(file);

                LootTable table = new LootTable(name);

                if (config.contains("items")) {
                    for (String itemKey : config.getConfigurationSection("items").getKeys(false)) {
                        String path = "items." + itemKey;
                        double weight = config.getDouble(path + ".weight", 1.0);
                        int minAmount = config.getInt(path + ".min-amount", 1);
                        int maxAmount = config.getInt(path + ".max-amount", 1);

                        table.addEntry(itemKey, weight, minAmount, maxAmount);
                    }
                }

                lootTables.put(name, table);
                getLogger().info("Loaded loot table: " + name);
            } catch (Exception e) {
                getLogger().warning("Failed to load loot table: " + name);
            }
        }

        private void saveLootTable(LootTable table) {
            File file = new File(getDataFolder(), "loot/" + table.getName() + ".yml");
            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            for (Map.Entry<String, LootEntry> entry : table.getEntries().entrySet()) {
                String path = "items." + entry.getKey();
                LootEntry lootEntry = entry.getValue();

                config.set(path + ".weight", lootEntry.getWeight());
                config.set(path + ".min-amount", lootEntry.getMinAmount());
                config.set(path + ".max-amount", lootEntry.getMaxAmount());
            }

            try {
                config.save(file);
            } catch (Exception e) {
                getLogger().warning("Failed to save loot table: " + table.getName());
            }
        }

        public void giveRewards(org.bukkit.entity.Player player, String dungeonName) {
            DungeonTemplate template = dungeonManager.getTemplate(dungeonName);
            if (template == null) return;

            // Give configured rewards
            for (String reward : template.getRewards()) {
                giveReward(player, reward);
            }

            // Give loot table rewards
            String lootTableName = dungeonName.toLowerCase();
            LootTable lootTable = lootTables.get(lootTableName);
            if (lootTable == null) {
                lootTable = lootTables.get("default");
            }

            if (lootTable != null) {
                List<LootDrop> drops = lootTable.generateLoot(new Random());
                for (LootDrop drop : drops) {
                    giveLootDrop(player, drop);
                }
            }

            // Give experience
            int expReward = calculateExpReward(dungeonName);
            player.giveExp(expReward);

            player.sendMessage(colorize("&a&lRewards received! &7Check your inventory."));
        }

        private void giveReward(org.bukkit.entity.Player player, String reward) {
            if (reward.startsWith("money:")) {
                // Economy integration (placeholder)
                String amount = reward.substring(6);
                player.sendMessage(colorize("&a+$" + amount + " &7(Economy not integrated)"));
            } else if (reward.startsWith("exp:")) {
                int exp = Integer.parseInt(reward.substring(4));
                player.giveExp(exp);
                player.sendMessage(colorize("&a+" + exp + " experience"));
            } else if (reward.startsWith("item:")) {
                // Parse item format: item:MATERIAL:amount
                String[] parts = reward.split(":");
                if (parts.length >= 3) {
                    try {
                        org.bukkit.Material material = org.bukkit.Material.valueOf(parts[1]);
                        int amount = Integer.parseInt(parts[2]);

                        org.bukkit.inventory.ItemStack item = new org.bukkit.inventory.ItemStack(material, amount);
                        player.getInventory().addItem(item);
                        player.sendMessage(colorize("&a+" + amount + " " + material.name()));
                    } catch (Exception e) {
                        getLogger().warning("Invalid reward format: " + reward);
                    }
                }
            }
        }

        private void giveLootDrop(org.bukkit.entity.Player player, LootDrop drop) {
            try {
                org.bukkit.Material material = org.bukkit.Material.valueOf(drop.getItemId());
                org.bukkit.inventory.ItemStack item = new org.bukkit.inventory.ItemStack(material, drop.getAmount());
                player.getInventory().addItem(item);
                player.sendMessage(colorize("&e+" + drop.getAmount() + " " + material.name()));
            } catch (Exception e) {
                getLogger().warning("Invalid loot item: " + drop.getItemId());
            }
        }

        private int calculateExpReward(String dungeonName) {
            DungeonTemplate template = dungeonManager.getTemplate(dungeonName);
            if (template == null) return 100;

            // Base experience based on difficulty
            int baseExp = 100;
            switch (template.getDifficulty().toLowerCase()) {
                case "easy": baseExp = 50; break;
                case "normal": baseExp = 100; break;
                case "hard": baseExp = 200; break;
                case "expert": baseExp = 400; break;
                case "master": baseExp = 800; break;
            }

            return baseExp;
        }

        public LootTable getLootTable(String name) {
            return lootTables.get(name);
        }

        public Collection<LootTable> getAllLootTables() {
            return lootTables.values();
        }
    }

    public class StatisticsManager {

        public void loadPlayerData() {
            File dataFolder = new File(getDataFolder(), "playerdata");
            if (!dataFolder.exists()) {
                dataFolder.mkdirs();
                return;
            }

            File[] dataFiles = dataFolder.listFiles((dir, name) -> name.endsWith(".yml"));
            if (dataFiles == null) return;

            for (File file : dataFiles) {
                String uuidString = file.getName().replace(".yml", "");
                try {
                    UUID playerId = UUID.fromString(uuidString);
                    loadPlayerData(playerId, file);
                } catch (Exception e) {
                    getLogger().warning("Invalid player data file: " + file.getName());
                }
            }
        }

        private void loadPlayerData(UUID playerId, File file) {
            try {
                org.bukkit.configuration.file.YamlConfiguration config =
                    org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(file);

                String playerName = config.getString("name", "Unknown");
                PlayerData data = new PlayerData(playerId, playerName);

                data.setDungeonsCompleted(config.getInt("dungeons-completed", 0));
                data.setDungeonsFailed(config.getInt("dungeons-failed", 0));
                data.setTotalPlayTime(config.getLong("total-playtime", 0));
                data.setLastPlayed(config.getLong("last-played", System.currentTimeMillis()));

                // Load dungeon-specific stats
                if (config.contains("dungeon-stats")) {
                    for (String dungeon : config.getConfigurationSection("dungeon-stats").getKeys(false)) {
                        int completions = config.getInt("dungeon-stats." + dungeon, 0);
                        data.getDungeonStats().put(dungeon, completions);
                    }
                }

                // Load achievements
                data.getAchievements().addAll(config.getStringList("achievements"));

                playerData.put(playerId, data);
            } catch (Exception e) {
                getLogger().warning("Failed to load player data for: " + playerId);
            }
        }

        public void savePlayerData(UUID playerId) {
            PlayerData data = playerData.get(playerId);
            if (data == null) return;

            File file = new File(getDataFolder(), "playerdata/" + playerId.toString() + ".yml");
            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            config.set("name", data.getPlayerName());
            config.set("dungeons-completed", data.getDungeonsCompleted());
            config.set("dungeons-failed", data.getDungeonsFailed());
            config.set("total-playtime", data.getTotalPlayTime());
            config.set("last-played", data.getLastPlayed());
            config.set("achievements", data.getAchievements());

            // Save dungeon-specific stats
            for (Map.Entry<String, Integer> entry : data.getDungeonStats().entrySet()) {
                config.set("dungeon-stats." + entry.getKey(), entry.getValue());
            }

            try {
                config.save(file);
            } catch (Exception e) {
                getLogger().warning("Failed to save player data for: " + playerId);
            }
        }

        public void saveAllPlayerData() {
            for (UUID playerId : playerData.keySet()) {
                savePlayerData(playerId);
            }
        }

        public PlayerData getPlayerData(UUID playerId) {
            return playerData.computeIfAbsent(playerId, id -> {
                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(id);
                String name = player != null ? player.getName() : "Unknown";
                return new PlayerData(id, name);
            });
        }

        public void recordCompletion(UUID playerId, String dungeonName) {
            PlayerData data = getPlayerData(playerId);
            data.incrementCompleted();
            data.getDungeonStats().merge(dungeonName, 1, Integer::sum);
            data.setLastPlayed(System.currentTimeMillis());

            // Check for achievements
            checkAchievements(playerId, data);

            savePlayerData(playerId);
        }

        public void recordFailure(UUID playerId, String dungeonName) {
            PlayerData data = getPlayerData(playerId);
            data.incrementFailed();
            data.setLastPlayed(System.currentTimeMillis());

            savePlayerData(playerId);
        }

        public void recordPlayTime(UUID playerId, long playTime) {
            PlayerData data = getPlayerData(playerId);
            data.addPlayTime(playTime);

            savePlayerData(playerId);
        }

        private void checkAchievements(UUID playerId, PlayerData data) {
            List<String> newAchievements = new ArrayList<>();

            // First completion
            if (data.getDungeonsCompleted() == 1 && !data.getAchievements().contains("first_completion")) {
                newAchievements.add("first_completion");
            }

            // Milestone achievements
            int completed = data.getDungeonsCompleted();
            if (completed >= 10 && !data.getAchievements().contains("veteran")) {
                newAchievements.add("veteran");
            }
            if (completed >= 50 && !data.getAchievements().contains("expert")) {
                newAchievements.add("expert");
            }
            if (completed >= 100 && !data.getAchievements().contains("master")) {
                newAchievements.add("master");
            }

            // Dungeon-specific achievements
            for (Map.Entry<String, Integer> entry : data.getDungeonStats().entrySet()) {
                String dungeonName = entry.getKey();
                int completions = entry.getValue();
                String achievementName = dungeonName + "_master";

                if (completions >= 10 && !data.getAchievements().contains(achievementName)) {
                    newAchievements.add(achievementName);
                }
            }

            // Award new achievements
            for (String achievement : newAchievements) {
                data.getAchievements().add(achievement);

                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    player.sendMessage(colorize("&6&lAchievement Unlocked! &e" + formatAchievementName(achievement)));
                    // Play sound effect
                    player.playSound(player.getLocation(), org.bukkit.Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
                }
            }
        }

        private String formatAchievementName(String achievement) {
            return Arrays.stream(achievement.split("_"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .reduce((a, b) -> a + " " + b)
                .orElse(achievement);
        }

        public List<PlayerData> getTopPlayers(int limit) {
            return playerData.values().stream()
                .sorted((a, b) -> Integer.compare(b.getDungeonsCompleted(), a.getDungeonsCompleted()))
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
        }

        public Map<String, Integer> getDungeonPopularity() {
            Map<String, Integer> popularity = new HashMap<>();

            for (PlayerData data : playerData.values()) {
                for (Map.Entry<String, Integer> entry : data.getDungeonStats().entrySet()) {
                    popularity.merge(entry.getKey(), entry.getValue(), Integer::sum);
                }
            }

            return popularity;
        }
    }

    // ===== BUILDER DATA CLASSES =====

    public static class BuildSession {
        private UUID playerId;
        private String dungeonName;
        private org.bukkit.Location pos1;
        private org.bukkit.Location pos2;
        private org.bukkit.Location startPoint;
        private org.bukkit.Location endPoint;
        private List<DungeonBlock> dungeonBlocks;
        private BuildMode mode;
        private long createdTime;
        private boolean hasChanges;

        public BuildSession(UUID playerId, String dungeonName) {
            this.playerId = playerId;
            this.dungeonName = dungeonName;
            this.dungeonBlocks = new ArrayList<>();
            this.mode = BuildMode.BUILDING;
            this.createdTime = System.currentTimeMillis();
            this.hasChanges = false;
        }

        // Getters and setters
        public UUID getPlayerId() { return playerId; }
        public String getDungeonName() { return dungeonName; }
        public org.bukkit.Location getPos1() { return pos1; }
        public org.bukkit.Location getPos2() { return pos2; }
        public org.bukkit.Location getStartPoint() { return startPoint; }
        public org.bukkit.Location getEndPoint() { return endPoint; }
        public List<DungeonBlock> getDungeonBlocks() { return dungeonBlocks; }
        public BuildMode getMode() { return mode; }
        public long getCreatedTime() { return createdTime; }
        public boolean hasChanges() { return hasChanges; }

        public void setPos1(org.bukkit.Location pos1) { this.pos1 = pos1; this.hasChanges = true; }
        public void setPos2(org.bukkit.Location pos2) { this.pos2 = pos2; this.hasChanges = true; }
        public void setStartPoint(org.bukkit.Location startPoint) { this.startPoint = startPoint; this.hasChanges = true; }
        public void setEndPoint(org.bukkit.Location endPoint) { this.endPoint = endPoint; this.hasChanges = true; }
        public void setMode(BuildMode mode) { this.mode = mode; }
        public void setHasChanges(boolean hasChanges) { this.hasChanges = hasChanges; }

        public boolean hasSelection() {
            return pos1 != null && pos2 != null;
        }

        public int getVolume() {
            if (!hasSelection()) return 0;
            int dx = Math.abs(pos1.getBlockX() - pos2.getBlockX()) + 1;
            int dy = Math.abs(pos1.getBlockY() - pos2.getBlockY()) + 1;
            int dz = Math.abs(pos1.getBlockZ() - pos2.getBlockZ()) + 1;
            return dx * dy * dz;
        }
    }

    public enum BuildMode {
        BUILDING, TESTING, PREVIEWING
    }

    public static class DungeonBlock {
        private org.bukkit.Location location;
        private DungeonBlockType type;
        private Map<String, Object> data;
        private String lootTable;
        private String mobType;
        private boolean isBoss;

        public DungeonBlock(org.bukkit.Location location, DungeonBlockType type) {
            this.location = location;
            this.type = type;
            this.data = new HashMap<>();
        }

        // Getters and setters
        public org.bukkit.Location getLocation() { return location; }
        public DungeonBlockType getType() { return type; }
        public Map<String, Object> getData() { return data; }
        public String getLootTable() { return lootTable; }
        public String getMobType() { return mobType; }
        public boolean isBoss() { return isBoss; }

        public void setType(DungeonBlockType type) { this.type = type; }
        public void setLootTable(String lootTable) { this.lootTable = lootTable; }
        public void setMobType(String mobType) { this.mobType = mobType; }
        public void setBoss(boolean boss) { this.isBoss = boss; }
    }

    public enum DungeonBlockType {
        START_POINT, END_POINT, LOOT_CHEST, MOB_SPAWNER, BOSS_SPAWNER, CHECKPOINT, TRIGGER, BARRIER
    }

    public static class SchematicPreview {
        private UUID playerId;
        private String schematicName;
        private org.bukkit.Location previewLocation;
        private List<org.bukkit.Location> hologramLocations;
        private boolean isActive;
        private long createdTime;

        public SchematicPreview(UUID playerId, String schematicName, org.bukkit.Location previewLocation) {
            this.playerId = playerId;
            this.schematicName = schematicName;
            this.previewLocation = previewLocation;
            this.hologramLocations = new ArrayList<>();
            this.isActive = true;
            this.createdTime = System.currentTimeMillis();
        }

        // Getters and setters
        public UUID getPlayerId() { return playerId; }
        public String getSchematicName() { return schematicName; }
        public org.bukkit.Location getPreviewLocation() { return previewLocation; }
        public List<org.bukkit.Location> getHologramLocations() { return hologramLocations; }
        public boolean isActive() { return isActive; }
        public long getCreatedTime() { return createdTime; }

        public void setActive(boolean active) { this.isActive = active; }
        public void addHologramLocation(org.bukkit.Location location) { this.hologramLocations.add(location); }
    }

    // ===== BUILDER MANAGER =====

    public class BuilderManager {

        public void giveBuilderWand(org.bukkit.entity.Player player) {
            org.bukkit.inventory.ItemStack wand = new org.bukkit.inventory.ItemStack(org.bukkit.Material.GOLDEN_AXE);
            org.bukkit.inventory.meta.ItemMeta meta = wand.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(colorize("&6&lDungeon Builder Wand"));
                meta.setLore(Arrays.asList(
                    colorize("&7Left click: Set position 1"),
                    colorize("&7Right click: Set position 2"),
                    colorize("&7Shift + Right click: Open builder menu"),
                    "",
                    colorize("&eUltimateDungeons Builder Tool")
                ));
                meta.addEnchant(org.bukkit.enchantments.Enchantment.DURABILITY, 1, true);
                meta.addItemFlags(org.bukkit.inventory.ItemFlag.HIDE_ENCHANTS);
                wand.setItemMeta(meta);
            }

            player.getInventory().addItem(wand);
            player.sendMessage(colorize("&aYou received the Dungeon Builder Wand!"));
            player.sendMessage(colorize("&7Use it to select areas and access builder tools."));
        }

        public BuildSession createBuildSession(UUID playerId, String dungeonName) {
            BuildSession session = new BuildSession(playerId, dungeonName);
            activeBuildSessions.put(playerId, session);

            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(colorize("&aStarted building dungeon: &f" + dungeonName));
                player.sendMessage(colorize("&7Use your builder wand to select an area."));
            }

            return session;
        }

        public void saveBuildSession(UUID playerId, String fileName) {
            BuildSession session = activeBuildSessions.get(playerId);
            if (session == null) return;

            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player == null) return;

            if (!session.hasSelection()) {
                player.sendMessage(colorize("&cYou must select an area first!"));
                return;
            }

            try {
                // Save schematic (simplified - would use WorldEdit API)
                File schematicFile = new File(getDataFolder(), "dungeons/" + fileName + ".schematic");
                saveSchematic(session.getPos1(), session.getPos2(), schematicFile);

                // Save dungeon configuration
                saveDungeonConfig(session, fileName);

                // Save dungeon blocks
                saveDungeonBlocks(session, fileName);

                player.sendMessage(colorize("&aDungeon saved as: &f" + fileName));
                session.setHasChanges(false);

            } catch (Exception e) {
                player.sendMessage(colorize("&cFailed to save dungeon: " + e.getMessage()));
                getLogger().warning("Failed to save dungeon: " + e.getMessage());
            }
        }

        private void saveSchematic(org.bukkit.Location pos1, org.bukkit.Location pos2, File file) {
            // Placeholder for schematic saving
            // In a real implementation, this would use WorldEdit API
            getLogger().info("Saving schematic from " + pos1 + " to " + pos2 + " as " + file.getName());
        }

        private void saveDungeonConfig(BuildSession session, String fileName) {
            File configFile = new File(getDataFolder(), "dungeons/" + fileName + ".yml");
            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            config.set("display-name", formatDungeonName(fileName));
            config.set("description", "A custom dungeon created with UltimateDungeons");
            config.set("min-players", 1);
            config.set("max-players", 5);
            config.set("time-limit", 1800);
            config.set("difficulty", "Normal");
            config.set("rewards", Arrays.asList("exp:100", "money:50"));

            if (session.getStartPoint() != null) {
                config.set("start-point.x", session.getStartPoint().getX());
                config.set("start-point.y", session.getStartPoint().getY());
                config.set("start-point.z", session.getStartPoint().getZ());
            }

            if (session.getEndPoint() != null) {
                config.set("end-point.x", session.getEndPoint().getX());
                config.set("end-point.y", session.getEndPoint().getY());
                config.set("end-point.z", session.getEndPoint().getZ());
            }

            try {
                config.save(configFile);
            } catch (Exception e) {
                getLogger().warning("Failed to save dungeon config: " + e.getMessage());
            }
        }

        private void saveDungeonBlocks(BuildSession session, String fileName) {
            File blocksFile = new File(getDataFolder(), "dungeons/" + fileName + "_blocks.yml");
            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            int index = 0;
            for (DungeonBlock block : session.getDungeonBlocks()) {
                String path = "blocks." + index;
                config.set(path + ".type", block.getType().name());
                config.set(path + ".x", block.getLocation().getX());
                config.set(path + ".y", block.getLocation().getY());
                config.set(path + ".z", block.getLocation().getZ());

                if (block.getLootTable() != null) {
                    config.set(path + ".loot-table", block.getLootTable());
                }

                if (block.getMobType() != null) {
                    config.set(path + ".mob-type", block.getMobType());
                    config.set(path + ".is-boss", block.isBoss());
                }

                for (Map.Entry<String, Object> entry : block.getData().entrySet()) {
                    config.set(path + ".data." + entry.getKey(), entry.getValue());
                }

                index++;
            }

            try {
                config.save(blocksFile);
            } catch (Exception e) {
                getLogger().warning("Failed to save dungeon blocks: " + e.getMessage());
            }
        }

        private String formatDungeonName(String fileName) {
            return Arrays.stream(fileName.split("_"))
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .reduce((a, b) -> a + " " + b)
                .orElse(fileName);
        }

        public SchematicPreview createPreview(UUID playerId, String schematicName, org.bukkit.Location location) {
            // Remove existing preview
            SchematicPreview existingPreview = activepreviews.get(playerId);
            if (existingPreview != null) {
                removePreview(playerId);
            }

            SchematicPreview preview = new SchematicPreview(playerId, schematicName, location);
            activepreviews.put(playerId, preview);

            // Create 3D holographic preview
            createHolographicPreview(preview);

            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
            if (player != null) {
                player.sendMessage(colorize("&aCreated 3D preview of: &f" + schematicName));
                player.sendMessage(colorize("&7Use &e/udtools place &7to place it or &c/udtools cancel &7to remove"));
            }

            return preview;
        }

        private void createHolographicPreview(SchematicPreview preview) {
            // Create holographic outline of the schematic
            // This is a simplified version - would need proper schematic reading
            org.bukkit.Location center = preview.getPreviewLocation();

            // Create a simple box outline for demonstration
            for (int x = -5; x <= 5; x++) {
                for (int y = 0; y <= 10; y++) {
                    for (int z = -5; z <= 5; z++) {
                        // Only create outline, not filled
                        if (Math.abs(x) == 5 || Math.abs(z) == 5 || y == 0 || y == 10) {
                            org.bukkit.Location hologramLoc = center.clone().add(x, y, z);

                            // Spawn holographic blocks (using barrier blocks with glow effect)
                            org.bukkit.World world = hologramLoc.getWorld();
                            if (world != null) {
                                // In a real implementation, you'd use a hologram API
                                // For now, we'll just track the locations
                                preview.addHologramLocation(hologramLoc);
                            }
                        }
                    }
                }
            }
        }

        public void removePreview(UUID playerId) {
            SchematicPreview preview = activepreviews.remove(playerId);
            if (preview != null) {
                // Remove holographic blocks
                for (org.bukkit.Location location : preview.getHologramLocations()) {
                    // Remove hologram at location
                    // In a real implementation, you'd clean up the hologram API objects
                }

                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    player.sendMessage(colorize("&cRemoved schematic preview."));
                }
            }
        }

        public void placeSchematic(UUID playerId) {
            SchematicPreview preview = activepreviews.get(playerId);
            if (preview == null) {
                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    player.sendMessage(colorize("&cNo active preview to place!"));
                }
                return;
            }

            // Place the actual schematic
            File schematicFile = new File(getDataFolder(), "dungeons/" + preview.getSchematicName() + ".schematic");
            if (!schematicFile.exists()) {
                schematicFile = new File(getDataFolder(), "dungeons/" + preview.getSchematicName() + ".schem");
            }

            if (schematicFile.exists()) {
                loadAndPlaceSchematic(schematicFile, preview.getPreviewLocation());

                org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(playerId);
                if (player != null) {
                    player.sendMessage(colorize("&aPlaced schematic: &f" + preview.getSchematicName()));
                }
            }

            // Remove preview
            removePreview(playerId);
        }

        private void loadAndPlaceSchematic(File schematicFile, org.bukkit.Location location) {
            // Placeholder for schematic placement
            // In a real implementation, this would use WorldEdit API
            getLogger().info("Placing schematic " + schematicFile.getName() + " at " + location);
        }
    }
}
