package com.ultimatedungeons;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class PartyManager {
    
    private final UltimateDungeons plugin;
    private final Map<UUID, Party> parties;
    private final Map<UUID, UUID> playerToParty; // Player UUID -> Party Leader UUID
    
    public PartyManager(UltimateDungeons plugin) {
        this.plugin = plugin;
        this.parties = new ConcurrentHashMap<>();
        this.playerToParty = new ConcurrentHashMap<>();
    }
    
    public void createParty(Player leader) {
        UUID leaderId = leader.getUniqueId();
        
        if (isInParty(leader)) {
            leader.sendMessage(colorize("&cYou are already in a party!"));
            return;
        }
        
        Party party = new Party(leaderId);
        parties.put(leaderId, party);
        playerToParty.put(leaderId, leaderId);
        
        leader.sendMessage(colorize("&aParty created! You are the party leader."));
        plugin.getLogger().info("Player " + leader.getName() + " created a party");
    }
    
    public void joinParty(Player player, Player leader) {
        UUID playerId = player.getUniqueId();
        UUID leaderId = leader.getUniqueId();
        
        if (isInParty(player)) {
            player.sendMessage(colorize("&cYou are already in a party!"));
            return;
        }
        
        Party party = parties.get(leaderId);
        if (party == null) {
            player.sendMessage(colorize("&cThat player doesn't have a party!"));
            return;
        }
        
        if (party.getMembers().size() >= getMaxPartySize()) {
            player.sendMessage(colorize("&cThat party is full!"));
            return;
        }
        
        // Add player to party
        party.addMember(playerId);
        playerToParty.put(playerId, leaderId);
        
        // Notify all party members
        String joinMessage = colorize("&a" + player.getName() + " joined the party!");
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(joinMessage);
            }
        }
        
        plugin.getLogger().info("Player " + player.getName() + " joined " + leader.getName() + "'s party");
    }
    
    public void leaveParty(Player player) {
        UUID playerId = player.getUniqueId();
        
        if (!isInParty(player)) {
            player.sendMessage(colorize("&cYou are not in a party!"));
            return;
        }
        
        UUID leaderId = playerToParty.get(playerId);
        Party party = parties.get(leaderId);
        
        if (party == null) {
            playerToParty.remove(playerId);
            return;
        }
        
        // If player is the leader, disband the party
        if (playerId.equals(leaderId)) {
            disbandParty(party);
            return;
        }
        
        // Remove player from party
        party.removeMember(playerId);
        playerToParty.remove(playerId);
        
        // Notify all party members
        String leaveMessage = colorize("&c" + player.getName() + " left the party.");
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(leaveMessage);
            }
        }
        
        player.sendMessage(colorize("&cYou left the party."));
        plugin.getLogger().info("Player " + player.getName() + " left the party");
    }
    
    public void inviteToParty(Player leader, Player target) {
        UUID leaderId = leader.getUniqueId();
        
        Party party = parties.get(leaderId);
        if (party == null) {
            leader.sendMessage(colorize("&cYou don't have a party!"));
            return;
        }
        
        if (isInParty(target)) {
            leader.sendMessage(colorize("&cThat player is already in a party!"));
            return;
        }
        
        if (party.getMembers().size() >= getMaxPartySize()) {
            leader.sendMessage(colorize("&cYour party is full!"));
            return;
        }
        
        // Send invite (simplified - in a full implementation you'd have a proper invite system)
        target.sendMessage(colorize("&a" + leader.getName() + " invited you to their party!"));
        target.sendMessage(colorize("&7Use /party join " + leader.getName() + " to accept."));
        
        leader.sendMessage(colorize("&aInvite sent to " + target.getName() + "!"));
    }
    
    public void kickFromParty(Player leader, Player target) {
        UUID leaderId = leader.getUniqueId();
        UUID targetId = target.getUniqueId();
        
        Party party = parties.get(leaderId);
        if (party == null) {
            leader.sendMessage(colorize("&cYou don't have a party!"));
            return;
        }
        
        if (!party.getMembers().contains(targetId)) {
            leader.sendMessage(colorize("&cThat player is not in your party!"));
            return;
        }
        
        if (targetId.equals(leaderId)) {
            leader.sendMessage(colorize("&cYou cannot kick yourself! Use /party disband instead."));
            return;
        }
        
        // Remove player from party
        party.removeMember(targetId);
        playerToParty.remove(targetId);
        
        // Notify all party members
        String kickMessage = colorize("&c" + target.getName() + " was kicked from the party.");
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(kickMessage);
            }
        }
        
        target.sendMessage(colorize("&cYou were kicked from the party."));
        plugin.getLogger().info("Player " + target.getName() + " was kicked from " + leader.getName() + "'s party");
    }
    
    public void disbandParty(Party party) {
        UUID leaderId = party.getLeader();
        
        // Notify all members
        String disbandMessage = colorize("&cThe party has been disbanded.");
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                member.sendMessage(disbandMessage);
            }
            playerToParty.remove(memberId);
        }
        
        parties.remove(leaderId);
        
        Player leader = Bukkit.getPlayer(leaderId);
        if (leader != null) {
            plugin.getLogger().info("Party disbanded by " + leader.getName());
        }
    }
    
    public void listPartyMembers(Player player) {
        if (!isInParty(player)) {
            player.sendMessage(colorize("&cYou are not in a party!"));
            return;
        }
        
        UUID leaderId = playerToParty.get(player.getUniqueId());
        Party party = parties.get(leaderId);
        
        if (party == null) {
            player.sendMessage(colorize("&cError: Party not found!"));
            return;
        }
        
        player.sendMessage(colorize("&6=== Party Members ==="));
        
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                String role = memberId.equals(leaderId) ? " &7(Leader)" : "";
                String status = member.isOnline() ? "&a" : "&c";
                player.sendMessage(colorize(status + member.getName() + role));
            }
        }
    }
    
    public boolean isInParty(Player player) {
        return playerToParty.containsKey(player.getUniqueId());
    }
    
    public Party getParty(Player player) {
        UUID leaderId = playerToParty.get(player.getUniqueId());
        return leaderId != null ? parties.get(leaderId) : null;
    }
    
    public boolean isPartyLeader(Player player) {
        UUID playerId = player.getUniqueId();
        return parties.containsKey(playerId);
    }
    
    public List<Player> getPartyMembers(Player player) {
        Party party = getParty(player);
        if (party == null) {
            return new ArrayList<>();
        }
        
        List<Player> members = new ArrayList<>();
        for (UUID memberId : party.getMembers()) {
            Player member = Bukkit.getPlayer(memberId);
            if (member != null) {
                members.add(member);
            }
        }
        return members;
    }
    
    private int getMaxPartySize() {
        return plugin.getConfig().getInt("dungeons.max-party-size", 5);
    }
    
    private String colorize(String message) {
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    public void shutdown() {
        // Clean up all parties
        parties.clear();
        playerToParty.clear();
    }
    
    // Inner class
    public static class Party {
        private final UUID leader;
        private final Set<UUID> members;
        private final long createdTime;
        
        public Party(UUID leader) {
            this.leader = leader;
            this.members = ConcurrentHashMap.newKeySet();
            this.members.add(leader); // Leader is also a member
            this.createdTime = System.currentTimeMillis();
        }
        
        public void addMember(UUID playerId) {
            members.add(playerId);
        }
        
        public void removeMember(UUID playerId) {
            members.remove(playerId);
        }
        
        public UUID getLeader() {
            return leader;
        }
        
        public Set<UUID> getMembers() {
            return new HashSet<>(members);
        }
        
        public int getSize() {
            return members.size();
        }
        
        public long getCreatedTime() {
            return createdTime;
        }
        
        public boolean isMember(UUID playerId) {
            return members.contains(playerId);
        }
    }
}
