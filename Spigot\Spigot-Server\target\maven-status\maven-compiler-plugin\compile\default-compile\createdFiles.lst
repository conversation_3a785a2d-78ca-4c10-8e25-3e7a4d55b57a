org\bukkit\craftbukkit\configuration\ConfigSerializationUtil.class
net\minecraft\world\level\entity\EntityAccess.class
org\bukkit\craftbukkit\entity\CraftFrog$CraftVariant.class
net\minecraft\world\entity\ai\behavior\BehaviorProfession.class
net\minecraft\world\entity\IEntitySelector.class
net\minecraft\world\entity\animal\EntityPanda$j.class
net\minecraft\world\inventory\SlotFurnaceResult.class
org\bukkit\craftbukkit\block\impl\CraftBell.class
net\minecraft\world\level\storage\loot\predicates\LootItemConditionSurvivesExplosion.class
org\bukkit\craftbukkit\entity\CraftGoat.class
org\bukkit\craftbukkit\inventory\CraftInventorySmithing.class
org\bukkit\craftbukkit\inventory\CraftInventoryEnchanting.class
net\minecraft\commands\CommandDispatcher$1.class
net\minecraft\world\entity\monster\EntitySlime$PathfinderGoalSlimeRandomDirection.class
org\bukkit\craftbukkit\damage\CraftDamageSource.class
org\bukkit\craftbukkit\entity\CraftBoat.class
net\minecraft\world\level\block\entity\TileEntityShulkerBox.class
net\minecraft\world\entity\animal\frog\ShootTongue.class
net\minecraft\server\network\PlayerConnection$1.class
net\minecraft\world\level\gameevent\vibrations\VibrationSystem$c.class
net\minecraft\server\commands\CommandLoot$b.class
net\minecraft\world\level\block\entity\CrafterBlockEntity.class
org\bukkit\craftbukkit\block\data\type\CraftSwitch.class
net\minecraft\world\entity\animal\allay\Allay$a.class
net\minecraft\world\level\portal\ShapeDetectorShape.class
net\minecraft\util\datafix\DataConverterRegistry$1.class
net\minecraft\world\level\levelgen\structure\structures\IglooPieces.class
net\minecraft\commands\arguments\blocks\ArgumentBlock$a.class
net\minecraft\world\level\block\BlockPressurePlateAbstract.class
net\minecraft\server\network\LoginListener$2.class
net\minecraft\world\entity\animal\EntityPanda$e.class
net\minecraft\world\level\material\FluidTypeFlowing$3.class
net\minecraft\world\entity\animal\horse\EntityLlamaTrader.class
org\bukkit\craftbukkit\block\impl\CraftDirtSnow.class
net\minecraft\commands\CommandDispatcher$1$1.class
net\minecraft\core\dispenser\DispenseBehaviorShulkerBox.class
org\bukkit\craftbukkit\scheduler\CraftScheduler$2.class
net\minecraft\world\level\block\BlockChorusFlower.class
net\minecraft\server\level\EntityPlayer.class
net\minecraft\world\entity\monster\EntityZombie.class
net\minecraft\world\entity\monster\EntityEnderman$PathfinderGoalEndermanPlaceBlock.class
net\minecraft\world\entity\ai\behavior\BehaviorMakeLove.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperGrate.class
org\bukkit\craftbukkit\block\impl\CraftSnow.class
net\minecraft\world\entity\animal\Bucketable.class
net\minecraft\world\entity\monster\EntitySpider$PathfinderGoalSpiderNearestAttackableTarget.class
org\bukkit\craftbukkit\block\data\type\CraftRespawnAnchor.class
org\bukkit\craftbukkit\entity\CraftFish.class
net\minecraft\world\item\crafting\RecipeSmoking.class
org\bukkit\craftbukkit\block\impl\CraftCake.class
org\bukkit\craftbukkit\packs\CraftDataPack.class
org\bukkit\craftbukkit\entity\CraftDolphin.class
net\minecraft\server\network\ServerConnection$2.class
org\bukkit\craftbukkit\inventory\CraftInventoryJukebox.class
net\minecraft\world\entity\ai\behavior\BehaviorInteractDoor.class
net\minecraft\world\level\block\BlockFenceGate.class
org\bukkit\craftbukkit\help\SimpleHelpMap.class
org\bukkit\craftbukkit\CraftSound.class
net\minecraft\world\entity\monster\EntityPhantom$d.class
net\minecraft\world\effect\PoisonMobEffect.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneTorchWall.class
net\minecraft\nbt\NBTTagIntArray.class
net\minecraft\world\entity\boss\wither\EntityWither.class
org\bukkit\craftbukkit\inventory\CraftMetaCharge.class
org\bukkit\craftbukkit\inventory\CraftInventoryStonecutter.class
org\bukkit\craftbukkit\entity\CraftStrider.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalHurtByTarget.class
net\minecraft\world\item\crafting\ShapelessRecipes.class
net\minecraft\server\network\ServerConnection$LatencySimulator.class
org\bukkit\craftbukkit\profile\CraftPlayerTextures.class
org\bukkit\craftbukkit\block\impl\CraftStepAbstract.class
org\bukkit\craftbukkit\entity\CraftPlayer$1.class
net\minecraft\world\inventory\Container.class
net\minecraft\network\protocol\game\ClientboundSetBorderCenterPacket.class
net\minecraft\world\entity\animal\EntityTurtle$d.class
net\minecraft\world\entity\monster\EntityGuardian$EntitySelectorGuardianTargetHumanSquid.class
net\minecraft\world\level\block\entity\TileEntitySkull.class
org\bukkit\craftbukkit\boss\CraftDragonBattle.class
net\minecraft\server\level\ChunkProviderServer.class
net\minecraft\world\level\block\BlockCauldron.class
net\minecraft\core\dispenser\DispenseBehaviorItem.class
org\bukkit\craftbukkit\generator\CraftWorldInfo.class
net\minecraft\core\dispenser\IDispenseBehavior$23.class
org\bukkit\craftbukkit\inventory\CraftMetaBundle.class
net\minecraft\core\cauldron\CauldronInteraction$a.class
org\bukkit\craftbukkit\inventory\trim\CraftTrimMaterial.class
net\minecraft\world\level\block\state\BlockBase$f.class
org\bukkit\craftbukkit\entity\CraftTippedArrow.class
org\bukkit\craftbukkit\entity\CraftHanging.class
org\bukkit\craftbukkit\block\impl\CraftMinecartDetector.class
net\minecraft\world\level\storage\Convertable$ConversionSession.class
org\bukkit\craftbukkit\block\impl\CraftMinecartTrack.class
net\minecraft\world\level\block\entity\TileEntityBeehive$ReleaseStatus.class
net\minecraft\world\level\block\SculkSpreader.class
org\bukkit\craftbukkit\block\CraftCalibratedSculkSensor.class
org\bukkit\craftbukkit\block\impl\CraftFire.class
org\bukkit\craftbukkit\scoreboard\CraftScoreboardComponent.class
org\bukkit\craftbukkit\entity\CraftEntityTypes$SpawnData.class
net\minecraft\commands\ICommandListener$1.class
net\minecraft\world\level\block\SculkSensorBlock.class
org\bukkit\craftbukkit\block\impl\CraftPinkPetals.class
org\bukkit\craftbukkit\entity\CraftIronGolem.class
org\bukkit\craftbukkit\entity\CraftEntityTypes$EntityTypeData.class
net\minecraft\server\level\PlayerChunkMap.class
org\bukkit\craftbukkit\scheduler\CraftAsyncTask$1.class
net\minecraft\world\entity\boss\wither\EntityWither$a.class
net\minecraft\server\commands\CommandSummon.class
net\minecraft\world\level\block\entity\TileEntityJukeBox.class
org\bukkit\craftbukkit\inventory\CraftInventoryLlama.class
org\bukkit\craftbukkit\entity\CraftMonster.class
org\bukkit\craftbukkit\block\CraftDropper.class
net\minecraft\world\item\ItemEnderEye.class
org\bukkit\craftbukkit\block\impl\CraftHugeMushroom.class
net\minecraft\core\dispenser\IDispenseBehavior$9.class
net\minecraft\world\entity\IEntitySelector$EntitySelectorEquipable.class
net\minecraft\world\level\levelgen\structure\structures\SwampHutPiece.class
net\minecraft\world\level\GameRules$GameRuleCategory.class
org\bukkit\craftbukkit\block\impl\CraftNetherWart.class
net\minecraft\world\entity\raid\EntityRaider$a.class
net\minecraft\core\dispenser\IDispenseBehavior$4.class
net\minecraft\commands\arguments\selector\ArgumentParserSelector.class
org\bukkit\craftbukkit\help\HelpTopicAmendment.class
org\bukkit\craftbukkit\entity\CraftOcelot.class
org\bukkit\craftbukkit\block\impl\CraftDispenser.class
net\minecraft\world\entity\projectile\EntityEgg.class
org\bukkit\craftbukkit\entity\CraftLivingEntity.class
net\minecraft\world\level\block\state\BlockBase$BlockData.class
org\bukkit\craftbukkit\entity\CraftTextDisplay.class
net\minecraft\world\level\block\BlockConcretePowder.class
org\bukkit\craftbukkit\block\impl\CraftSculkCatalyst.class
net\minecraft\world\level\block\BlockSapling.class
net\minecraft\world\level\MobSpawnerAbstract.class
net\minecraft\world\entity\monster\EntityGhast$PathfinderGoalGhastAttackTarget.class
org\bukkit\craftbukkit\block\data\type\CraftDispenser.class
org\bukkit\craftbukkit\persistence\DirtyCraftPersistentDataContainer.class
net\minecraft\world\inventory\ContainerHorse.class
org\bukkit\craftbukkit\block\data\type\CraftBed.class
org\bukkit\craftbukkit\block\impl\CraftIceFrost.class
org\bukkit\craftbukkit\util\CraftStructureTransformer.class
org\bukkit\craftbukkit\generator\CustomChunkGenerator.class
org\bukkit\craftbukkit\block\impl\CraftWitherSkull.class
net\minecraft\world\entity\monster\EntityPhantom$i.class
org\bukkit\craftbukkit\block\impl\CraftCherryLeaves.class
org\bukkit\craftbukkit\entity\CraftMinecartTNT.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$h.class
org\bukkit\craftbukkit\entity\CraftGiant.class
net\minecraft\world\inventory\ContainerLoom$4.class
org\bukkit\craftbukkit\block\data\type\CraftTechnicalPiston.class
org\bukkit\craftbukkit\entity\CraftVillager.class
net\minecraft\world\level\block\entity\TileEntityConduit.class
net\minecraft\world\entity\monster\EntitySpider.class
net\minecraft\world\entity\vehicle\EntityMinecartAbstract.class
org\bukkit\craftbukkit\entity\CraftChestedHorse.class
org\bukkit\craftbukkit\block\impl\CraftEnderChest.class
net\minecraft\world\item\ItemBoat.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces$c.class
org\bukkit\craftbukkit\block\impl\CraftNote.class
net\minecraft\world\entity\projectile\WindCharge.class
org\bukkit\craftbukkit\inventory\CraftMerchant.class
org\bukkit\craftbukkit\advancement\CraftAdvancementProgress.class
net\minecraft\world\item\ItemBow.class
org\bukkit\craftbukkit\block\CraftBlockStates.class
org\bukkit\craftbukkit\block\CraftContainer.class
org\bukkit\craftbukkit\block\impl\CraftBubbleColumn.class
net\minecraft\world\entity\animal\EntityFox$g.class
org\bukkit\craftbukkit\entity\CraftCreeper.class
org\bukkit\craftbukkit\generator\OldCraftChunkData.class
net\minecraft\world\item\ItemArmor$1.class
net\minecraft\world\level\gameevent\GameEventDispatcher.class
org\bukkit\craftbukkit\block\data\CraftWaterlogged.class
net\minecraft\world\level\chunk\IChunkAccess.class
net\minecraft\server\AdvancementDataWorld.class
net\minecraft\world\level\block\BlockNylium.class
net\minecraft\world\level\block\entity\TileEntityBeacon.class
org\bukkit\craftbukkit\command\ServerCommandSender.class
net\minecraft\world\level\border\WorldBorder$d.class
org\bukkit\craftbukkit\util\TransformerGeneratorAccess.class
net\minecraft\world\level\block\BlockButtonAbstract.class
org\bukkit\craftbukkit\entity\CraftFireball.class
net\minecraft\world\inventory\ContainerFurnace.class
org\bukkit\craftbukkit\entity\CraftTameableAnimal.class
net\minecraft\world\level\block\entity\TileEntityBanner.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$c.class
net\minecraft\world\level\block\entity\TileEntityLectern$LecternInventory.class
net\minecraft\world\level\GeneratorAccess.class
net\minecraft\network\protocol\handshake\PacketHandshakingInSetProtocol.class
net\minecraft\util\datafix\DataConverterRegistry.class
org\bukkit\craftbukkit\block\impl\CraftTripwireHook.class
net\minecraft\world\level\levelgen\MobSpawnerPatrol.class
net\minecraft\world\entity\animal\EntityFox$b.class
org\bukkit\craftbukkit\block\data\type\CraftDaylightDetector.class
net\minecraft\world\entity\animal\horse\EntityLlama$b.class
net\minecraft\world\entity\animal\EntitySheep$1.class
net\minecraft\world\level\block\entity\BrushableBlockEntity.class
org\bukkit\craftbukkit\block\CraftSuspiciousSand.class
net\minecraft\world\level\chunk\ChunkSection.class
org\bukkit\craftbukkit\generator\structure\CraftGeneratedStructure.class
org\bukkit\craftbukkit\entity\CraftSniffer.class
org\bukkit\craftbukkit\inventory\CraftEntityEquipment.class
net\minecraft\world\entity\projectile\EntityFishingHook$WaterPosition.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperTrapDoor.class
org\bukkit\craftbukkit\entity\CraftEndermite.class
net\minecraft\world\item\crafting\ShapelessRecipes$a.class
org\bukkit\craftbukkit\block\impl\CraftLayeredCauldron.class
net\minecraft\world\entity\EntityAgeable$a.class
net\minecraft\world\level\block\BlockObserver.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces.class
net\minecraft\world\entity\monster\EntityEnderman$PathfinderGoalPlayerWhoLookedAtTarget.class
net\minecraft\world\entity\animal\EntityCat$a.class
net\minecraft\world\entity\monster\EntityEnderman$PathfinderGoalEndermanPickupBlock.class
org\bukkit\craftbukkit\entity\CraftSilverfish.class
org\bukkit\craftbukkit\block\data\CraftHangable.class
net\minecraft\world\level\block\BlockMobSpawner.class
org\bukkit\craftbukkit\block\impl\CraftRotatable.class
net\minecraft\world\inventory\ContainerStonecutter.class
org\bukkit\craftbukkit\bootstrap\Main.class
net\minecraft\commands\arguments\ArgumentEntity$Info$Template.class
net\minecraft\world\entity\animal\EntityTurtle$i.class
net\minecraft\stats\ServerStatisticManager.class
net\minecraft\world\level\block\BlockDaylightDetector.class
net\minecraft\world\item\ItemArmor$a.class
net\minecraft\world\level\chunk\ChunkStatus$Type.class
net\minecraft\stats\RecipeBookServer.class
net\minecraft\world\level\storage\loot\functions\LootEnchantFunction.class
net\minecraft\world\inventory\TransientCraftingContainer.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$e.class
org\spigotmc\TickLimiter.class
org\bukkit\craftbukkit\inventory\CraftRecipe.class
net\minecraft\world\entity\monster\EntitySilverfish$PathfinderGoalSilverfishWakeOthers.class
org\bukkit\craftbukkit\structure\CraftPalette.class
net\minecraft\world\inventory\ContainerGrindstone.class
net\minecraft\server\players\NameReferencingFileConverter$2.class
org\bukkit\craftbukkit\CraftServerTickManager.class
org\bukkit\craftbukkit\block\impl\CraftBarrier.class
org\bukkit\craftbukkit\block\impl\CraftSkull.class
net\minecraft\world\level\chunk\ChunkStatus$b.class
net\minecraft\core\dispenser\IDispenseBehavior$14.class
net\minecraft\world\entity\animal\EntityBee$c.class
net\minecraft\world\level\block\entity\CrafterBlockEntity$1.class
net\minecraft\world\level\chunk\storage\RegionFile$b.class
org\bukkit\craftbukkit\SpigotTimings.class
net\minecraft\world\IInventory.class
org\bukkit\craftbukkit\block\impl\CraftCommand.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Hopper.class
org\bukkit\craftbukkit\boss\CraftKeyedBossbar.class
net\minecraft\world\entity\projectile\EntityLlamaSpit.class
net\minecraft\world\level\storage\Convertable$b.class
net\minecraft\network\syncher\DataWatcher$Item.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$e.class
org\bukkit\craftbukkit\block\CraftSign.class
org\bukkit\craftbukkit\generator\structure\CraftStructure.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$c.class
org\bukkit\craftbukkit\entity\CraftEntityTypes$HangingData.class
net\minecraft\server\level\ChunkProviderServer$a.class
net\minecraft\world\entity\animal\camel\Camel$b.class
org\bukkit\craftbukkit\util\CraftBiomeSearchResult.class
net\minecraft\server\level\EntityPlayer$2.class
org\bukkit\craftbukkit\block\data\type\CraftCrafter.class
net\minecraft\world\level\IWorldWriter.class
org\bukkit\craftbukkit\CraftRaid$1.class
net\minecraft\server\players\ExpirableListEntry.class
net\minecraft\nbt\NBTTagList$1.class
net\minecraft\world\level\entity\PersistentEntitySectionManager$a.class
org\bukkit\craftbukkit\entity\CraftVillager$CraftType.class
net\minecraft\core\dispenser\IDispenseBehavior$19.class
org\bukkit\craftbukkit\CraftEffect.class
net\minecraft\server\CustomFunctionData.class
net\minecraft\server\players\PlayerList.class
net\minecraft\world\entity\monster\EntityPhantom$AttackPhase.class
org\bukkit\craftbukkit\block\CraftDispenser.class
org\bukkit\craftbukkit\block\impl\CraftLever.class
net\minecraft\world\item\ItemEndCrystal.class
org\bukkit\craftbukkit\entity\CraftAnimals.class
net\minecraft\network\PacketDataSerializer.class
org\bukkit\craftbukkit\inventory\CraftInventoryMerchant.class
org\bukkit\craftbukkit\block\data\type\CraftDoor.class
org\bukkit\craftbukkit\inventory\CraftInventoryCustom$MinecraftInventory.class
org\bukkit\craftbukkit\block\data\type\CraftPointedDripstone.class
org\bukkit\craftbukkit\CraftMusicInstrument.class
org\bukkit\craftbukkit\block\data\type\CraftCommandBlock.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn$3.class
net\minecraft\core\dispenser\IDispenseBehavior$6.class
org\bukkit\craftbukkit\block\CraftTrialSpawner.class
org\bukkit\craftbukkit\block\data\CraftRotatable.class
org\bukkit\craftbukkit\map\CraftMapCanvas.class
net\minecraft\world\entity\animal\EntityOcelot$a.class
org\bukkit\craftbukkit\tag\CraftItemTag.class
org\bukkit\craftbukkit\event\CraftEventFactory$1.class
org\bukkit\craftbukkit\block\CraftBiome.class
org\bukkit\craftbukkit\util\DatFileFilter.class
org\bukkit\craftbukkit\packs\CraftDataPackManager.class
net\minecraft\world\entity\monster\EntityGhast$PathfinderGoalGhastIdleMove.class
net\minecraft\world\entity\monster\EntityPhantom$g.class
net\minecraft\world\entity\monster\EntityGuardian$PathfinderGoalGuardianAttack.class
org\bukkit\craftbukkit\entity\CraftBee.class
org\bukkit\craftbukkit\entity\CraftPiglinAbstract.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$a.class
net\minecraft\core\cauldron\CauldronInteraction.class
org\bukkit\craftbukkit\block\impl\CraftBanner.class
net\minecraft\world\item\enchantment\EnchantmentWeaponDamage.class
net\minecraft\world\item\crafting\ShapedRecipes$Serializer.class
net\minecraft\world\level\storage\loot\parameters\LootContextParameters.class
net\minecraft\world\entity\projectile\EntityFishingHook$HookState.class
org\bukkit\craftbukkit\block\CraftCreatureSpawner.class
net\minecraft\world\level\block\state\BlockBase$EnumRandomOffset.class
org\bukkit\craftbukkit\entity\CraftVillagerZombie.class
org\bukkit\craftbukkit\persistence\CraftPersistentDataAdapterContext.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn$1.class
org\bukkit\craftbukkit\block\CraftBarrel.class
net\minecraft\world\entity\animal\EntityTurtle$a.class
net\minecraft\world\level\block\grower\WorldGenTreeProvider.class
net\minecraft\world\level\block\MultifaceSpreader$b.class
net\minecraft\world\entity\animal\frog\Tadpole.class
org\bukkit\craftbukkit\block\impl\CraftFurnaceFurace.class
net\minecraft\world\entity\ai\goal\PathfinderGoalEatTile.class
net\minecraft\world\level\border\WorldBorder$a.class
org\bukkit\craftbukkit\block\CraftCampfire.class
net\minecraft\world\level\World$a.class
net\minecraft\world\entity\decoration\EntityLeash.class
org\bukkit\craftbukkit\entity\CraftTropicalFish.class
net\minecraft\world\entity\animal\EntityPanda$b.class
org\bukkit\craftbukkit\entity\CraftAreaEffectCloud.class
net\minecraft\world\inventory\ContainerEnchantTable$2.class
net\minecraft\world\level\block\entity\TileEntityLectern.class
net\minecraft\server\commands\CommandTeleport.class
net\minecraft\world\level\block\BlockReed.class
net\minecraft\world\entity\IEntityAngerable.class
net\minecraft\world\inventory\ContainerAnvilAbstract$2.class
org\bukkit\craftbukkit\entity\CraftLeash.class
org\bukkit\craftbukkit\entity\CraftEntityType.class
net\minecraft\world\entity\projectile\EntityEnderSignal.class
net\minecraft\world\entity\animal\EntityRabbit$GroupDataRabbit.class
org\bukkit\craftbukkit\block\CraftBed.class
org\bukkit\craftbukkit\block\data\type\CraftScaffolding.class
net\minecraft\core\dispenser\IDispenseBehavior$16.class
org\bukkit\craftbukkit\CraftWorld$1.class
org\bukkit\craftbukkit\tag\CraftBlockTag.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$j.class
org\spigotmc\Metrics$Graph.class
net\minecraft\world\entity\animal\EntityChicken.class
net\minecraft\world\inventory\ContainerLectern.class
net\minecraft\world\entity\animal\EntityPufferFish$a.class
net\minecraft\world\inventory\ContainerBeacon$1.class
org\bukkit\craftbukkit\entity\CraftPlayer.class
org\bukkit\craftbukkit\inventory\CraftResultInventory.class
org\bukkit\craftbukkit\entity\CraftFox.class
net\minecraft\network\protocol\common\ServerboundCustomPayloadPacket$UnknownPayload.class
net\minecraft\world\entity\npc\EntityVillager.class
net\minecraft\world\inventory\InventoryMerchant.class
org\bukkit\craftbukkit\block\CraftDecoratedPot.class
org\bukkit\craftbukkit\inventory\CraftMetaItem.class
net\minecraft\world\level\levelgen\structure\structures\IglooPieces$a.class
org\bukkit\craftbukkit\block\impl\CraftSkullPlayer.class
org\bukkit\craftbukkit\structure\CraftStructure.class
net\minecraft\core\dispenser\IDispenseBehavior$11.class
org\bukkit\craftbukkit\block\CraftConduit.class
org\bukkit\craftbukkit\block\impl\CraftKelp.class
org\bukkit\craftbukkit\block\impl\CraftCoralFanAbstract.class
net\minecraft\server\level\PlayerChunk$Failure$1.class
net\minecraft\world\entity\monster\EntityRavager.class
org\bukkit\craftbukkit\entity\CraftSpectralArrow.class
net\minecraft\world\level\SpawnerCreature$b.class
net\minecraft\world\level\block\BlockComposter$ContainerOutput.class
net\minecraft\world\entity\ai\behavior\BehaviorFindAdmirableItem.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$2.class
net\minecraft\world\entity\player\PlayerInventory.class
net\minecraft\world\inventory\ContainerAccess.class
org\bukkit\craftbukkit\inventory\CraftMetaArmor.class
net\minecraft\world\entity\monster\EntityShulker.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructure$a.class
org\bukkit\craftbukkit\scoreboard\CraftScoreboard.class
net\minecraft\world\level\block\SculkVeinBlock.class
net\minecraft\world\entity\monster\EntityPhantom$b.class
net\minecraft\server\level\PlayerChunkMap$3.class
org\bukkit\craftbukkit\util\Commodore$1.class
net\minecraft\server\players\NameReferencingFileConverter$4.class
net\minecraft\world\level\World$1.class
net\minecraft\world\entity\monster\EntityGuardian$ControllerMoveGuardian.class
net\minecraft\world\level\block\BlockLeaves.class
net\minecraft\server\commands\CommandSpreadPlayers.class
org\bukkit\craftbukkit\inventory\CraftInventoryBeacon.class
com\mojang\brigadier\CommandDispatcher.class
org\bukkit\craftbukkit\inventory\CraftInventoryCustom.class
org\bukkit\craftbukkit\block\impl\CraftPumpkinCarved.class
net\minecraft\world\entity\decoration\EntityHanging.class
org\bukkit\craftbukkit\ban\CraftIpBanList.class
net\minecraft\world\level\block\BlockChest.class
net\minecraft\server\players\UserCache$1.class
org\bukkit\craftbukkit\block\data\type\CraftComparator.class
org\bukkit\craftbukkit\inventory\CraftMetaBlockState.class
org\bukkit\craftbukkit\inventory\CraftMetaCrossbow.class
org\bukkit\craftbukkit\block\data\CraftMultipleFacing.class
net\minecraft\world\item\ItemDebugStick.class
org\bukkit\craftbukkit\block\impl\CraftCaveVinesPlant.class
org\bukkit\craftbukkit\entity\CraftEvokerFangs.class
net\minecraft\server\level\PlayerChunk$b.class
net\minecraft\world\level\block\entity\TileEntityFurnace.class
org\bukkit\craftbukkit\CraftServer.class
net\minecraft\commands\arguments\selector\EntitySelector$1.class
net\minecraft\world\entity\animal\EntityBee$e.class
net\minecraft\server\level\TicketType.class
net\minecraft\world\entity\Entity$MovementEmission.class
org\bukkit\craftbukkit\inventory\CraftMetaBookSigned.class
net\minecraft\world\level\levelgen\structure\PersistentStructureLegacy.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$1.class
net\minecraft\world\item\ItemBlock.class
net\minecraft\server\network\LegacyPingHandler.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$m.class
org\bukkit\craftbukkit\entity\CraftEnderPearl.class
net\minecraft\world\item\ItemRecord.class
net\minecraft\world\entity\vehicle\EntityMinecartTNT.class
net\minecraft\world\entity\EntityAgeable.class
net\minecraft\world\level\GameRules$h.class
org\bukkit\craftbukkit\block\impl\CraftPoweredRail.class
org\bukkit\craftbukkit\CraftCrashReport.class
org\bukkit\craftbukkit\legacy\CraftLegacy.class
net\minecraft\world\item\ItemFishingRod.class
net\minecraft\world\item\ItemFlintAndSteel.class
org\bukkit\craftbukkit\inventory\CraftMerchant$1.class
net\minecraft\world\level\storage\loot\LootTable$a.class
org\bukkit\craftbukkit\block\impl\CraftBeehive.class
net\minecraft\world\level\block\SculkVeinBlock$a.class
net\minecraft\world\level\block\BlockFungi.class
net\minecraft\network\protocol\game\PacketPlayOutMultiBlockChange.class
net\minecraft\world\item\ItemMinecart$1.class
net\minecraft\server\commands\CommandGamerule$1.class
net\minecraft\world\entity\monster\EntityPigZombie.class
net\minecraft\world\level\World$2.class
net\minecraft\world\entity\ai\goal\PathfinderGoalBreakDoor.class
org\bukkit\craftbukkit\entity\CraftCat$CraftType.class
org\bukkit\craftbukkit\entity\CraftLlamaSpit.class
org\bukkit\craftbukkit\block\impl\CraftBed.class
org\bukkit\craftbukkit\block\impl\CraftPortal.class
net\minecraft\world\level\block\MultifaceSpreader$e.class
org\bukkit\craftbukkit\block\impl\CraftChain.class
net\minecraft\server\level\PlayerChunk$c.class
net\minecraft\world\entity\animal\EntityBee$j.class
net\minecraft\world\entity\monster\EntityPhantom.class
net\minecraft\world\inventory\ContainerCartography$3.class
org\bukkit\craftbukkit\block\data\type\CraftCampfire.class
org\bukkit\craftbukkit\entity\CraftThrownPotion.class
net\minecraft\world\level\block\BlockCoralPlant.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneLamp.class
net\minecraft\world\entity\monster\EntitySkeletonWither.class
net\minecraft\world\inventory\ContainerCartography$5.class
org\bukkit\craftbukkit\block\impl\CraftCalibratedSculkSensor.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$o.class
net\minecraft\util\SpawnUtil.class
org\bukkit\craftbukkit\block\data\CraftLevelled.class
org\bukkit\craftbukkit\block\impl\CraftComposter.class
org\bukkit\craftbukkit\util\CraftRayTraceResult.class
net\minecraft\world\entity\animal\EntityBee$h.class
org\bukkit\craftbukkit\block\CraftCrafter.class
net\minecraft\world\level\storage\WorldNBTStorage.class
net\minecraft\world\InventorySubcontainer.class
org\bukkit\craftbukkit\command\BukkitCommandWrapper.class
net\minecraft\commands\arguments\ArgumentEntity$Info.class
org\bukkit\craftbukkit\block\impl\CraftHangingRoots.class
net\minecraft\world\level\block\BlockRedstoneOre.class
org\bukkit\craftbukkit\block\data\type\CraftChiseledBookshelf.class
net\minecraft\server\level\PlayerChunkMap$CallbackExecutor.class
net\minecraft\world\level\chunk\storage\RegionFileCache.class
net\minecraft\server\network\PlayerConnection$a.class
net\minecraft\world\level\block\SculkCatalystBlock.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructureInfo.class
net\minecraft\world\entity\animal\axolotl\Axolotl$c.class
org\bukkit\craftbukkit\util\CraftVector.class
net\minecraft\world\entity\projectile\EntityThrownTrident.class
org\bukkit\craftbukkit\entity\CraftHusk.class
org\bukkit\craftbukkit\entity\CraftMinecartCommand.class
org\bukkit\craftbukkit\block\impl\CraftGrindstone.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$4.class
net\minecraft\server\commands\CommandEffect.class
org\bukkit\craftbukkit\block\impl\CraftRepeater.class
net\minecraft\server\level\ChunkMapDistance$c.class
org\bukkit\craftbukkit\block\data\type\CraftJigsaw.class
net\minecraft\world\entity\monster\piglin\EntityPiglinAbstract.class
net\minecraft\world\entity\monster\EntityStrider.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$b.class
net\minecraft\world\entity\monster\EntityDrowned$a.class
net\minecraft\world\entity\animal\EntityFox$d.class
org\bukkit\craftbukkit\block\impl\CraftEquipableCarvedPumpkin.class
org\bukkit\craftbukkit\inventory\CraftItemType.class
net\minecraft\server\network\PacketStatusListener$1ServerListPingEvent$1.class
net\minecraft\world\entity\monster\EntityStrider$b.class
org\bukkit\craftbukkit\potion\CraftPotionType.class
net\minecraft\world\level\block\BlockNote.class
org\bukkit\craftbukkit\block\data\type\CraftCake.class
net\minecraft\world\level\block\CeilingHangingSignBlock.class
net\minecraft\world\entity\monster\EntityShulker$e.class
org\bukkit\craftbukkit\generator\structure\CraftStructurePiece.class
org\bukkit\craftbukkit\inventory\CraftSmithingTrimRecipe.class
net\minecraft\world\entity\projectile\EntityTippedArrow.class
org\bukkit\craftbukkit\block\impl\CraftSculkShrieker.class
org\bukkit\craftbukkit\inventory\CraftMetaItem$ItemMetaKey$Specific$To.class
net\minecraft\world\level\border\WorldBorder$b.class
net\minecraft\world\level\block\state\BlockBase$BlockData$Cache.class
org\bukkit\craftbukkit\block\impl\CraftLight.class
net\minecraft\world\entity\monster\EntityVex$b.class
org\bukkit\craftbukkit\entity\CraftMushroomCow.class
org\bukkit\craftbukkit\inventory\util\CraftCustomInventoryConverter.class
net\minecraft\world\level\chunk\NibbleArray.class
net\minecraft\world\item\ItemEgg.class
net\minecraft\world\entity\animal\EntityPanda.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter.class
org\bukkit\craftbukkit\block\impl\CraftScaffolding.class
net\minecraft\world\entity\Entity$MoveFunction.class
net\minecraft\network\protocol\common\ServerboundCustomPayloadPacket.class
org\bukkit\craftbukkit\util\CraftStructureTransformer$CraftTransformationState.class
org\bukkit\craftbukkit\entity\CraftTNTPrimed.class
net\minecraft\world\item\ItemArmorStand.class
net\minecraft\world\entity\animal\EntityPufferFish.class
org\bukkit\craftbukkit\block\CraftBlockEntityState.class
org\bukkit\craftbukkit\block\impl\CraftTrialSpawner.class
net\minecraft\world\entity\animal\EntityRabbit$ControllerMoveRabbit.class
net\minecraft\world\entity\monster\EntitySpider$PathfinderGoalSpiderMeleeAttack.class
org\bukkit\craftbukkit\block\data\CraftPowerable.class
net\minecraft\world\level\block\BlockTNT.class
org\bukkit\craftbukkit\block\impl\CraftSapling.class
net\minecraft\world\entity\ai\goal\PathfinderGoalTempt.class
net\minecraft\world\level\block\state\BlockBase.class
net\minecraft\world\level\block\entity\TileEntityContainer.class
net\minecraft\world\level\SpawnerCreature$d.class
org\bukkit\craftbukkit\inventory\CraftMetaSpawnEgg.class
net\minecraft\nbt\NBTTagIntArray$1.class
org\bukkit\craftbukkit\entity\CraftLightningStrike.class
net\minecraft\world\item\crafting\RecipeStonecutting.class
net\minecraft\world\entity\npc\InventoryCarrier.class
net\minecraft\server\dedicated\DedicatedServerSettings.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$b.class
org\bukkit\craftbukkit\block\impl\CraftWallSign.class
org\bukkit\craftbukkit\advancement\CraftAdvancement.class
org\spigotmc\ActivationRange.class
org\bukkit\craftbukkit\entity\CraftAbstractSkeleton.class
net\minecraft\world\entity\animal\EntityBee$1.class
net\minecraft\world\level\block\Block$a.class
org\bukkit\craftbukkit\block\impl\CraftStairs.class
net\minecraft\world\entity\projectile\EntityPotion.class
net\minecraft\world\level\block\BlockDirtSnowSpreadable.class
net\minecraft\world\inventory\ContainerStonecutter$2.class
org\bukkit\craftbukkit\block\data\type\CraftTripwire.class
net\minecraft\world\entity\vehicle\EntityMinecartCommandBlock.class
org\bukkit\craftbukkit\help\CustomHelpTopic.class
net\minecraft\world\level\GameRules.class
org\bukkit\craftbukkit\block\impl\CraftBannerWall.class
net\minecraft\world\level\chunk\ChunkSection$1a.class
net\minecraft\world\entity\animal\axolotl\Axolotl$Variant.class
net\minecraft\world\level\block\PointedDripstoneBlock.class
net\minecraft\world\item\ItemMonsterEgg.class
net\minecraft\world\level\block\entity\TileEntityEndGateway.class
net\minecraft\world\level\block\entity\TileEntityDispenser.class
org\bukkit\craftbukkit\util\CraftLegacy.class
net\minecraft\server\DispenserRegistry$1.class
net\minecraft\world\item\ItemWorldMap.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$l.class
net\minecraft\world\level\levelgen\structure\StructurePiece.class
net\minecraft\world\entity\animal\allay\Allay.class
net\minecraft\world\effect\RegenerationMobEffect.class
org\bukkit\craftbukkit\CraftWorldBorder.class
org\bukkit\craftbukkit\block\data\CraftAttachable.class
org\bukkit\craftbukkit\block\data\type\CraftGate.class
net\minecraft\world\entity\monster\EntitySilverfish$PathfinderGoalSilverfishHideInBlock.class
org\spigotmc\SpigotWorldConfig.class
net\minecraft\world\entity\vehicle\ChestBoat.class
net\minecraft\world\level\entity\PersistentEntitySectionManager$b.class
net\minecraft\world\level\portal\BlockPortalShape.class
org\bukkit\craftbukkit\block\data\type\CraftSnow.class
org\bukkit\craftbukkit\block\CraftBeehive.class
org\bukkit\craftbukkit\block\impl\CraftLantern.class
org\bukkit\craftbukkit\entity\CraftIllusioner.class
org\bukkit\craftbukkit\entity\CraftVillager$CraftProfession.class
net\minecraft\world\level\material\FluidTypeLava.class
com\mojang\brigadier\CommandDispatcher$1.class
net\minecraft\world\level\block\BlockCrops.class
org\bukkit\craftbukkit\block\impl\CraftChest.class
org\bukkit\craftbukkit\block\impl\CraftWaterloggedTransparent.class
net\minecraft\server\dedicated\PropertyManager$EditableProperty.class
org\bukkit\craftbukkit\command\ConsoleCommandCompleter.class
org\bukkit\craftbukkit\block\CraftBlock.class
net\minecraft\world\entity\raid\Raid$Status.class
net\minecraft\server\commands\CommandList.class
net\minecraft\world\entity\animal\EntityDolphin$b.class
net\minecraft\world\level\block\BlockRedstoneLamp.class
net\minecraft\world\entity\EntityLiving$3.class
org\bukkit\craftbukkit\block\impl\CraftRespawnAnchor.class
net\minecraft\world\level\block\entity\TileEntityLectern$1.class
org\bukkit\craftbukkit\inventory\CraftItemStack.class
net\minecraft\world\entity\projectile\EntityFishingHook.class
org\bukkit\craftbukkit\entity\CraftSquid.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$k.class
org\bukkit\craftbukkit\entity\CraftMinecart.class
org\bukkit\craftbukkit\block\data\type\CraftCaveVinesPlant.class
org\bukkit\craftbukkit\CraftWorld$2.class
org\bukkit\craftbukkit\entity\CraftSnowman.class
org\bukkit\craftbukkit\inventory\CraftMetaBook$SpigotMeta.class
net\minecraft\world\entity\animal\EntityFox$i.class
org\bukkit\craftbukkit\entity\CraftTurtle.class
net\minecraft\world\entity\monster\EntityDrowned$f.class
org\bukkit\craftbukkit\block\data\type\CraftSculkSensor.class
org\bukkit\craftbukkit\block\data\CraftAnaloguePowerable.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces$e.class
org\bukkit\craftbukkit\CraftChunkSnapshot.class
net\minecraft\world\inventory\ContainerHorse$1.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Lectern.class
net\minecraft\world\level\block\entity\SculkCatalystBlockEntity$CatalystListener.class
org\bukkit\craftbukkit\entity\CraftGhast.class
net\minecraft\world\item\ItemStack.class
net\minecraft\world\entity\EntityLiving$4.class
org\bukkit\craftbukkit\help\CommandAliasHelpTopic.class
org\bukkit\craftbukkit\util\DummyGeneratorAccess.class
org\bukkit\craftbukkit\entity\CraftMinecartFurnace.class
org\bukkit\craftbukkit\block\impl\CraftConduit.class
net\minecraft\world\InventoryLargeChest.class
org\bukkit\craftbukkit\CraftWorld.class
net\minecraft\world\level\block\BlockCocoa.class
org\bukkit\craftbukkit\scheduler\CraftScheduler$4.class
net\minecraft\world\inventory\ContainerEnchantTable$1.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$k.class
org\bukkit\craftbukkit\block\impl\CraftSmoker.class
org\bukkit\craftbukkit\util\WeakCollection.class
org\bukkit\craftbukkit\advancement\CraftAdvancementDisplay.class
org\bukkit\craftbukkit\block\impl\CraftLectern.class
net\minecraft\server\dedicated\PropertyManager.class
net\minecraft\world\item\crafting\FurnaceRecipe.class
net\minecraft\world\level\block\BlockMagma.class
org\bukkit\craftbukkit\block\impl\CraftSeaPickle.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$b.class
net\minecraft\world\level\block\BlockSweetBerryBush.class
org\bukkit\craftbukkit\entity\CraftFirework.class
org\bukkit\craftbukkit\util\ForwardLogHandler.class
net\minecraft\server\network\LoginListener$EnumProtocolState.class
org\bukkit\craftbukkit\entity\CraftPiglinBrute.class
net\minecraft\world\level\block\BlockRespawnAnchor.class
net\minecraft\world\level\block\BlockDiodeAbstract.class
net\minecraft\world\entity\monster\EntityGuardianElder.class
net\minecraft\world\entity\projectile\EntityEnderPearl.class
org\bukkit\craftbukkit\command\ColouredConsoleSender.class
org\bukkit\craftbukkit\potion\CraftPotionEffectType.class
org\spigotmc\AsyncCatcher.class
org\bukkit\craftbukkit\block\impl\CraftCoralDead.class
net\minecraft\network\protocol\game\ClientboundInitializeBorderPacket.class
org\bukkit\craftbukkit\block\impl\CraftLightningRod.class
org\bukkit\craftbukkit\event\CraftEventFactory.class
net\minecraft\server\players\PlayerList$1.class
net\minecraft\world\level\block\entity\DecoratedPotBlockEntity$Decoration.class
org\bukkit\craftbukkit\block\CraftBeacon.class
org\bukkit\craftbukkit\block\data\type\CraftStructureBlock.class
net\minecraft\world\entity\monster\EntityDrowned.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$a.class
org\spigotmc\ActivationRange$ActivationType.class
org\bukkit\craftbukkit\entity\CraftLlama.class
net\minecraft\world\entity\decoration\EntityItemFrame.class
net\minecraft\world\level\gameevent\vibrations\VibrationSystem$a.class
org\bukkit\craftbukkit\block\CraftBlockStates$BlockStateFactory.class
org\spigotmc\SpigotCommand.class
net\minecraft\server\players\PlayerList$2.class
org\bukkit\craftbukkit\block\CraftDaylightDetector.class
org\bukkit\craftbukkit\entity\CraftTadpole.class
net\minecraft\world\level\material\FluidTypeFlowing.class
net\minecraft\world\entity\monster\EntityIllagerIllusioner.class
org\bukkit\craftbukkit\inventory\CraftFurnaceRecipe.class
net\minecraft\world\level\block\BlockMonsterEggs.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$g.class
net\minecraft\world\level\block\BlockTrapdoor.class
net\minecraft\nbt\NBTTagList.class
org\bukkit\craftbukkit\entity\CraftSnowball.class
org\bukkit\craftbukkit\generator\structure\CraftStructureType.class
net\minecraft\core\dispenser\IDispenseBehavior$7$1.class
org\bukkit\craftbukkit\block\data\type\CraftCandle.class
net\minecraft\server\players\NameReferencingFileConverter$FileConversionException.class
org\bukkit\craftbukkit\entity\CraftExperienceOrb.class
org\bukkit\craftbukkit\block\impl\CraftChorusFruit.class
org\bukkit\craftbukkit\generator\CraftBiomeParameterPoint.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces$a.class
org\bukkit\craftbukkit\block\impl\CraftShulkerBox.class
org\bukkit\craftbukkit\entity\CraftEnderDragonPart.class
org\bukkit\craftbukkit\CraftOfflinePlayer.class
net\minecraft\world\level\block\BlockBed.class
net\minecraft\world\entity\projectile\WindCharge$a.class
org\bukkit\craftbukkit\util\Commodore$MethodPrinter.class
net\minecraft\world\entity\monster\EntityVex$c.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$f.class
org\bukkit\craftbukkit\CraftFluidCollisionMode.class
org\bukkit\craftbukkit\block\impl\CraftMangrovePropagule.class
org\bukkit\craftbukkit\entity\CraftProjectile.class
org\bukkit\craftbukkit\block\impl\CraftStonecutter.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Dispenser.class
net\minecraft\world\level\material\FluidTypeLava$a.class
net\minecraft\world\entity\EntityLightning.class
org\bukkit\craftbukkit\legacy\CraftEvil.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$g.class
org\bukkit\craftbukkit\entity\CraftSlime.class
org\bukkit\craftbukkit\util\CraftStructureSearchResult.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn$4.class
net\minecraft\world\level\block\entity\TileEntityFurnace$1.class
net\minecraft\world\level\block\SculkShriekerBlock.class
org\bukkit\craftbukkit\entity\CraftPiglin.class
org\bukkit\craftbukkit\entity\CraftVindicator.class
net\minecraft\util\datafix\fixes\DataConverterMap.class
net\minecraft\world\entity\animal\EntityFox$e.class
net\minecraft\world\entity\player\EntityHuman$EnumBedResult.class
net\minecraft\world\level\block\BlockBambooSapling.class
net\minecraft\world\entity\raid\Raid.class
org\bukkit\craftbukkit\block\impl\CraftTorchflowerCrop.class
net\minecraft\world\level\block\BlockBeehive.class
net\minecraft\world\level\block\ChangeOverTimeBlock.class
net\minecraft\server\commands\CommandDifficulty.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperDoor.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn$5.class
net\minecraft\world\entity\projectile\EntityFireballFireball.class
net\minecraft\world\entity\monster\EntityStrider$a.class
net\minecraft\world\entity\animal\EntityFox$c.class
net\minecraft\world\entity\animal\horse\EntityLlama$Variant.class
net\minecraft\world\entity\ai\goal\PathfinderGoalTame.class
net\minecraft\network\protocol\PlayerConnectionUtils.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$f.class
org\bukkit\craftbukkit\block\CraftHangingSign.class
net\minecraft\world\item\ItemBlockWallable.class
net\minecraft\world\level\block\BlockSnow.class
org\bukkit\craftbukkit\entity\CraftEnderman.class
org\bukkit\craftbukkit\block\impl\CraftStructure.class
net\minecraft\world\entity\projectile\EntityEvokerFangs.class
net\minecraft\world\entity\monster\EntityVex$a.class
net\minecraft\server\bossevents\BossBattleCustom.class
org\bukkit\craftbukkit\scheduler\CraftAsyncDebugger.class
org\bukkit\craftbukkit\generator\CustomChunkGenerator$CustomBiomeGrid.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalOwnerHurtByTarget.class
net\minecraft\world\entity\animal\EntityDolphin.class
net\minecraft\advancements\AdvancementTree$a.class
net\minecraft\server\players\JsonList.class
net\minecraft\world\level\levelgen\structure\structures\OceanRuinPieces$a.class
org\bukkit\craftbukkit\inventory\CraftInventoryPlayer.class
org\spigotmc\SpigotConfig.class
org\bukkit\craftbukkit\help\HelpYamlReader.class
org\bukkit\craftbukkit\util\JsonHelper.class
org\bukkit\craftbukkit\inventory\CraftMerchantRecipe.class
net\minecraft\world\item\crafting\RecipeItemStack$b.class
net\minecraft\world\entity\animal\EntityFox$o.class
org\bukkit\craftbukkit\inventory\CraftMetaKnowledgeBook.class
net\minecraft\world\entity\monster\warden\Warden$1.class
net\minecraft\world\level\block\BlockComposter$ContainerInput.class
net\minecraft\world\entity\animal\EntityDolphin$a.class
org\bukkit\craftbukkit\inventory\CraftMerchantCustom$MinecraftMerchant.class
org\bukkit\craftbukkit\map\RenderData.class
net\minecraft\world\entity\monster\EntityGhast$PathfinderGoalGhastMoveTowardsTarget.class
net\minecraft\world\entity\monster\EntitySlime$PathfinderGoalSlimeNearestPlayer.class
org\bukkit\craftbukkit\conversations\ConversationTracker.class
org\bukkit\craftbukkit\entity\CraftPainting.class
net\minecraft\world\inventory\Containers.class
org\bukkit\craftbukkit\entity\CraftArrow.class
net\minecraft\world\entity\ai\goal\PathfinderGoalSit.class
net\minecraft\world\entity\Interaction$PlayerAction.class
net\minecraft\world\entity\animal\EntityMushroomCow.class
net\minecraft\world\level\block\BlockPumpkinCarved.class
net\minecraft\core\dispenser\IDispenseBehavior$8$1.class
net\minecraft\world\level\storage\loot\LootDataManager$1.class
net\minecraft\commands\CommandDispatcher$ServerType.class
net\minecraft\world\level\chunk\ChunkGeneratorStructureState.class
org\bukkit\craftbukkit\entity\CraftPanda.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$m.class
net\minecraft\world\item\ItemDye.class
net\minecraft\world\level\material\FluidTypeLava$b.class
org\bukkit\craftbukkit\CraftRaid.class
org\bukkit\craftbukkit\block\data\type\CraftTrialSpawner.class
org\bukkit\craftbukkit\entity\CraftWanderingTrader.class
net\minecraft\world\entity\animal\EntityFox$t.class
org\bukkit\craftbukkit\entity\CraftDrowned.class
net\minecraft\world\item\ItemCrossbow.class
org\bukkit\craftbukkit\block\CraftFurnaceFurnace.class
org\bukkit\craftbukkit\block\CraftSculkShrieker.class
net\minecraft\world\entity\animal\horse\EntityLlama$a.class
net\minecraft\world\entity\monster\EntityZombie$GroupDataZombie.class
org\bukkit\craftbukkit\tag\CraftEntityTag.class
net\minecraft\world\level\block\entity\ContainerOpenersCounter.class
net\minecraft\world\level\block\BlockFireAbstract.class
org\bukkit\craftbukkit\block\data\type\CraftWall.class
net\minecraft\world\entity\monster\EntityShulker$d.class
org\bukkit\craftbukkit\attribute\CraftAttributeMap.class
org\bukkit\craftbukkit\block\impl\CraftMycel.class
org\bukkit\craftbukkit\block\impl\CraftSnifferEgg.class
net\minecraft\world\inventory\ContainerBrewingStand$a.class
net\minecraft\server\level\ChunkMapDistance$b.class
net\minecraft\world\level\block\entity\ChiseledBookShelfBlockEntity.class
net\minecraft\world\entity\boss\enderdragon\phases\DragonControllerManager.class
net\minecraft\world\entity\item\EntityTNTPrimed.class
net\minecraft\world\entity\projectile\EntityArrow$PickupStatus.class
net\minecraft\world\entity\animal\EntityRabbit$PathfinderGoalEatCarrots.class
org\bukkit\craftbukkit\util\LazyHashSet.class
net\minecraft\world\level\chunk\Chunk$d.class
org\bukkit\craftbukkit\block\impl\CraftPiglinWallSkull.class
org\bukkit\craftbukkit\map\CraftMapView.class
org\bukkit\craftbukkit\block\impl\CraftCeilingHangingSign.class
net\minecraft\world\inventory\ContainerPlayer.class
net\minecraft\world\entity\vehicle\EntityMinecartAbstract$EnumMinecartType.class
net\minecraft\world\level\SpawnerCreature.class
net\minecraft\world\entity\ai\behavior\BehaviorFarm.class
net\minecraft\stats\StatisticManager.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$h.class
net\minecraft\world\inventory\ContainerChest.class
org\bukkit\craftbukkit\inventory\CraftMetaAxolotlBucket.class
org\bukkit\craftbukkit\entity\CraftFlying.class
org\bukkit\craftbukkit\entity\CraftWither.class
net\minecraft\world\entity\animal\EntityFox$h.class
net\minecraft\network\chat\IChatBaseComponent$b.class
org\bukkit\craftbukkit\entity\CraftDonkey.class
net\minecraft\world\entity\monster\EntityShulker$f.class
net\minecraft\world\entity\animal\allay\Allay$b.class
net\minecraft\world\damagesource\DamageSources.class
net\minecraft\server\level\EntityTrackerEntry.class
net\minecraft\world\entity\ai\behavior\PrepareRamNearestTarget.class
net\minecraft\world\entity\EntityLiving$5.class
net\minecraft\world\entity\item\EntityFallingBlock.class
net\minecraft\world\level\dimension\end\EnderDragonBattle.class
org\bukkit\craftbukkit\inventory\CraftMetaItem$ItemMetaKey$Specific.class
org\bukkit\craftbukkit\block\impl\CraftBarrel.class
net\minecraft\world\entity\animal\EntityDolphin$c.class
org\bukkit\craftbukkit\util\Waitable.class
net\minecraft\server\Main.class
org\bukkit\craftbukkit\entity\CraftZoglin.class
net\minecraft\server\network\ServerCommonPacketListenerImpl.class
net\minecraft\world\entity\EntityLiving$2.class
net\minecraft\world\item\ItemFireball.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$j.class
org\bukkit\craftbukkit\block\CraftCommandBlock.class
org\bukkit\craftbukkit\inventory\CraftInventoryView.class
net\minecraft\world\level\chunk\storage\RegionFile$ChunkBuffer.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces$d.class
org\bukkit\craftbukkit\util\UnsafeList$Itr.class
org\bukkit\craftbukkit\block\impl\CraftBamboo.class
net\minecraft\world\entity\monster\EntityDrowned$e.class
org\bukkit\craftbukkit\block\CraftComparator.class
net\minecraft\world\entity\animal\horse\EntityHorseAbstract.class
net\minecraft\server\level\PlayerChunk$Failure.class
org\bukkit\craftbukkit\block\CraftSmoker.class
org\bukkit\craftbukkit\entity\CraftPlayer$ChunkSectionChanges.class
org\bukkit\craftbukkit\projectiles\CraftBlockProjectileSource.class
net\minecraft\world\inventory\InventoryEnderChest.class
net\minecraft\world\level\chunk\storage\ChunkRegionLoader.class
org\bukkit\craftbukkit\block\sign\CraftSignSide.class
net\minecraft\world\level\block\BlockComposter.class
org\bukkit\craftbukkit\entity\CraftElderGuardian.class
net\minecraft\world\item\ItemTrident.class
org\bukkit\craftbukkit\block\impl\CraftStem.class
net\minecraft\world\entity\animal\EntityIronGolem.class
net\minecraft\commands\arguments\ArgumentEntity.class
net\minecraft\world\entity\animal\camel\Camel.class
org\bukkit\craftbukkit\block\impl\CraftBrushable.class
org\bukkit\craftbukkit\entity\CraftWitch.class
net\minecraft\world\entity\animal\EntityFox$j.class
org\bukkit\craftbukkit\inventory\util\CraftInventoryCreator$InventoryConverter.class
net\minecraft\world\item\crafting\ShapedRecipes.class
net\minecraft\world\entity\monster\piglin\PiglinAI.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructure$EntityInfo.class
org\bukkit\craftbukkit\entity\CraftBlaze.class
net\minecraft\world\level\Explosion.class
org\bukkit\craftbukkit\command\ConsoleCommandCompleter$1.class
net\minecraft\server\commands\CommandGamerule.class
net\minecraft\world\inventory\ContainerEnchantTable.class
net\minecraft\world\effect\HealOrHarmMobEffect.class
net\minecraft\world\ChestLock.class
org\bukkit\craftbukkit\damage\CraftDamageEffect.class
net\minecraft\world\entity\animal\sniffer\Sniffer.class
net\minecraft\world\entity\animal\EntityFox$l.class
net\minecraft\world\inventory\ContainerPlayer$1.class
org\bukkit\craftbukkit\entity\memory\CraftMemoryMapper.class
org\bukkit\craftbukkit\block\impl\CraftSkullWall.class
net\minecraft\world\level\block\BlockEnderPortal.class
org\bukkit\craftbukkit\inventory\trim\CraftTrimPattern.class
net\minecraft\world\entity\monster\EntityShulker$b.class
net\minecraft\world\level\block\piston\BlockPiston$1.class
net\minecraft\world\level\block\BlockIce.class
net\minecraft\world\level\block\piston\BlockPiston.class
net\minecraft\world\item\crafting\RecipeBlasting.class
org\spigotmc\Metrics$1.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$p.class
net\minecraft\world\entity\animal\EntityFox$r.class
org\bukkit\craftbukkit\entity\CraftAxolotl.class
net\minecraft\world\entity\monster\EntitySkeletonAbstract$1.class
org\bukkit\craftbukkit\bootstrap\Main$Thrower.class
net\minecraft\server\level\ChunkMapDistance$a.class
org\bukkit\craftbukkit\block\impl\CraftBigDripleaf.class
net\minecraft\world\item\enchantment\EnchantmentFrostWalker.class
org\bukkit\craftbukkit\block\data\type\CraftHopper.class
net\minecraft\world\item\crafting\RecipeCampfire.class
net\minecraft\world\entity\monster\EntityDrowned$c.class
net\minecraft\world\inventory\ContainerPlayer$2.class
org\bukkit\craftbukkit\generator\CraftLimitedRegion.class
net\minecraft\world\level\chunk\Chunk$c.class
net\minecraft\world\entity\monster\EntityZombieHusk.class
com\mojang\brigadier\tree\CommandNode.class
org\bukkit\craftbukkit\inventory\CraftMerchantCustom.class
net\minecraft\world\level\block\BlockBamboo.class
net\minecraft\world\entity\monster\EntityDrowned$d.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Furnace.class
net\minecraft\world\entity\animal\EntityFox$s.class
net\minecraft\network\NetworkManager$2.class
net\minecraft\world\level\block\BlockRedstoneTorch$RedstoneUpdateInfo.class
org\bukkit\craftbukkit\inventory\CraftInventoryFurnace.class
net\minecraft\world\entity\monster\EntityShulker$c.class
net\minecraft\world\level\block\BlockCactus.class
net\minecraft\server\commands\CommandTime.class
net\minecraft\world\entity\monster\EntityIllagerIllusioner$b.class
org\bukkit\craftbukkit\inventory\CraftMetaItem$SerializableMeta.class
org\spigotmc\Metrics.class
net\minecraft\nbt\NBTTagByteArray.class
net\minecraft\world\level\block\BlockRedstoneComparator.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneWire.class
net\minecraft\world\entity\animal\EntityFox$m.class
org\bukkit\craftbukkit\block\data\CraftOrientable.class
org\bukkit\craftbukkit\block\impl\CraftHay.class
org\bukkit\craftbukkit\block\impl\CraftPitcherCrop.class
net\minecraft\network\NetworkManager$1.class
org\bukkit\craftbukkit\ban\CraftIpBanEntry.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$o.class
org\bukkit\craftbukkit\block\data\type\CraftPinkPetals.class
net\minecraft\world\entity\monster\EntityIllagerIllusioner$a.class
net\minecraft\world\inventory\ContainerStonecutter$1.class
org\bukkit\craftbukkit\entity\CraftRabbit.class
net\minecraft\world\level\chunk\Chunk$a.class
org\bukkit\craftbukkit\block\impl\CraftDropper.class
org\bukkit\craftbukkit\block\impl\CraftCoralFan.class
net\minecraft\world\entity\animal\EntityParrot.class
org\bukkit\craftbukkit\inventory\tags\DeprecatedItemAdapterContext.class
net\minecraft\server\network\ServerCommonPacketListenerImpl$1.class
net\minecraft\world\entity\vehicle\EntityBoat$EnumStatus.class
net\minecraft\world\entity\animal\EntityFox$n.class
org\bukkit\craftbukkit\block\data\type\CraftEndPortalFrame.class
org\bukkit\craftbukkit\entity\CraftSizedFireball.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Dropper.class
net\minecraft\world\level\block\MultifaceSpreader.class
net\minecraft\server\commands\CommandSpawnpoint.class
net\minecraft\world\entity\ai\behavior\BehaviorFollowAdult.class
org\bukkit\craftbukkit\block\impl\CraftEnderPortalFrame.class
net\minecraft\world\entity\Entity$RemovalReason.class
net\minecraft\world\entity\monster\EntityDrowned$b.class
net\minecraft\world\level\block\BlockChest$3.class
net\minecraft\server\players\SleepStatus.class
org\bukkit\craftbukkit\entity\CraftMarker.class
org\bukkit\craftbukkit\block\impl\CraftCandleCake.class
net\minecraft\world\level\block\BlockChest$2.class
net\minecraft\world\item\ItemArmor.class
net\minecraft\world\item\trading\IMerchant.class
net\minecraft\world\entity\animal\EntityFox$q.class
net\minecraft\world\entity\monster\EntityShulker$a.class
net\minecraft\world\level\GameRules$GameRuleKey.class
net\minecraft\world\effect\HungerMobEffect.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$q.class
org\bukkit\craftbukkit\block\data\type\CraftSculkCatalyst.class
org\bukkit\craftbukkit\util\CraftVoxelShape.class
net\minecraft\world\level\block\entity\TileEntityBarrel.class
org\bukkit\craftbukkit\block\data\CraftBlockData.class
org\bukkit\craftbukkit\entity\CraftThrownExpBottle.class
org\bukkit\craftbukkit\inventory\CraftStonecuttingRecipe.class
net\minecraft\world\entity\SaddleStorage.class
org\bukkit\craftbukkit\generator\InternalChunkGenerator.class
net\minecraft\world\level\block\BlockChest$1.class
org\bukkit\craftbukkit\inventory\CraftInventoryLectern.class
net\minecraft\world\entity\animal\EntityFox$p.class
org\bukkit\craftbukkit\entity\CraftMinecartContainer.class
net\minecraft\world\item\ItemMinecart.class
org\bukkit\craftbukkit\entity\CraftCamel.class
org\bukkit\craftbukkit\scheduler\CraftFuture.class
org\bukkit\craftbukkit\entity\CraftFishHook.class
net\minecraft\world\entity\EntityLiving$1.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$3.class
org\bukkit\craftbukkit\inventory\CraftInventory.class
net\minecraft\world\entity\projectile\IProjectile.class
org\bukkit\craftbukkit\block\impl\CraftTorchWall.class
org\bukkit\craftbukkit\block\data\type\CraftTNT.class
org\bukkit\craftbukkit\block\impl\CraftGlazedTerracotta.class
org\bukkit\craftbukkit\util\ServerShutdownThread.class
org\bukkit\craftbukkit\entity\memory\CraftMemoryKey.class
net\minecraft\world\entity\raid\EntityRaider.class
net\minecraft\world\entity\animal\EntityFox$u.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Crafter.class
org\bukkit\craftbukkit\entity\AbstractProjectile.class
net\minecraft\world\entity\monster\EntitySkeletonAbstract.class
net\minecraft\world\entity\animal\EntityPanda$Gene.class
org\bukkit\craftbukkit\entity\CraftMinecartHopper.class
net\minecraft\world\entity\animal\horse\PathfinderGoalHorseTrap.class
net\minecraft\server\commands\CommandLoot.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneTorch.class
org\bukkit\craftbukkit\util\permissions\CraftDefaultPermissions.class
net\minecraft\world\entity\raid\Raid$Wave.class
net\minecraft\world\item\crafting\IRecipeComplex.class
org\bukkit\craftbukkit\entity\CraftMinecartMobSpawner.class
org\spigotmc\WatchdogThread.class
org\bukkit\craftbukkit\entity\CraftThrowableProjectile.class
org\bukkit\craftbukkit\util\DelegatedGeneratorAccess.class
net\minecraft\core\dispenser\DispenseBehaviorProjectile.class
net\minecraft\world\entity\projectile\EntityFireball.class
net\minecraft\world\entity\animal\EntityBee$d.class
net\minecraft\world\entity\EntityLiving$6.class
org\bukkit\craftbukkit\bootstrap\Main$FileEntry.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$8.class
org\bukkit\craftbukkit\ban\CraftProfileBanList.class
org\bukkit\craftbukkit\block\impl\CraftFloorSign.class
org\bukkit\craftbukkit\enchantments\CraftEnchantment.class
net\minecraft\world\level\block\BuddingAmethystBlock.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$i.class
org\bukkit\craftbukkit\entity\CraftParrot.class
org\bukkit\craftbukkit\block\data\type\CraftJukebox.class
net\minecraft\world\entity\ai\behavior\PrepareRamNearestTarget$a.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn$2.class
org\bukkit\craftbukkit\entity\CraftGuardian.class
org\bukkit\craftbukkit\entity\CraftStray.class
net\minecraft\server\level\ChunkProviderServer$b.class
net\minecraft\world\entity\boss\enderdragon\EntityEnderCrystal.class
net\minecraft\server\players\NameReferencingFileConverter$3.class
org\bukkit\craftbukkit\entity\CraftLightningStrike$1.class
net\minecraft\core\dispenser\DispenseBehaviorShears.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$n.class
net\minecraft\world\item\crafting\SmithingTrimRecipe$a.class
net\minecraft\world\entity\projectile\EntityArrow.class
net\minecraft\world\entity\ai\goal\PathfinderGoalFollowOwner.class
org\bukkit\craftbukkit\entity\CraftDisplay.class
net\minecraft\world\inventory\ContainerLoom$5.class
org\bukkit\craftbukkit\CraftServer$1.class
net\minecraft\world\level\block\entity\TileEntityCommand$1.class
org\bukkit\craftbukkit\block\impl\CraftCampfire.class
org\bukkit\craftbukkit\util\RandomSourceWrapper.class
net\minecraft\world\level\block\BlockCoralFanWall.class
org\bukkit\craftbukkit\entity\CraftPufferFish.class
net\minecraft\world\entity\animal\EntityFox$k.class
org\bukkit\craftbukkit\util\CraftDimensionUtil.class
org\bukkit\craftbukkit\tag\CraftFluidTag.class
net\minecraft\world\entity\animal\camel\Camel$c.class
net\minecraft\world\level\SpawnerCreature$a.class
net\minecraft\network\chat\IChatBaseComponent.class
net\minecraft\world\entity\monster\EntityGhast.class
org\bukkit\craftbukkit\block\impl\CraftReed.class
net\minecraft\world\level\block\BlockTripwire.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$i.class
org\bukkit\craftbukkit\entity\CraftAmbient.class
org\bukkit\craftbukkit\entity\CraftWindCharge.class
org\bukkit\craftbukkit\entity\CraftSalmon.class
net\minecraft\server\level\PlayerChunkMap$a.class
org\bukkit\craftbukkit\block\CraftBlockStates$1.class
net\minecraft\world\entity\monster\EntityZombie$a.class
org\bukkit\craftbukkit\CraftLootTable.class
org\bukkit\craftbukkit\entity\CraftGlowSquid.class
org\bukkit\craftbukkit\block\data\CraftLightable.class
net\minecraft\world\level\block\PointedDripstoneBlock$a.class
net\minecraft\world\level\block\Block.class
org\bukkit\craftbukkit\entity\CraftSpider.class
org\bukkit\craftbukkit\util\CraftNBTTagConfigSerializer.class
net\minecraft\world\level\IBlockAccess.class
net\minecraft\world\level\chunk\storage\IChunkLoader.class
org\bukkit\craftbukkit\util\TerminalConsoleWriterThread.class
net\minecraft\world\level\chunk\IChunkAccess$a.class
org\bukkit\craftbukkit\entity\CraftEntity.class
org\bukkit\craftbukkit\metadata\PlayerMetadataStore.class
net\minecraft\world\level\GameRules$GameRuleValue.class
org\bukkit\craftbukkit\block\impl\CraftTwistingVines.class
org\bukkit\craftbukkit\entity\CraftInteraction$CraftPreviousInteraction.class
org\bukkit\craftbukkit\command\CraftConsoleCommandSender.class
net\minecraft\core\dispenser\IDispenseBehavior$15.class
net\minecraft\world\level\RayTrace$c.class
org\bukkit\craftbukkit\block\impl\CraftFence.class
net\minecraft\world\level\gameevent\vibrations\VibrationSystem.class
net\minecraft\world\level\saveddata\maps\WorldMap.class
org\bukkit\craftbukkit\block\impl\CraftCarrots.class
org\bukkit\craftbukkit\inventory\CraftMetaBook.class
net\minecraft\world\entity\animal\EntityRabbit.class
org\bukkit\craftbukkit\entity\CraftBlockDisplay.class
net\minecraft\world\level\block\DropExperienceBlock.class
net\minecraft\world\entity\animal\frog\ShootTongue$a.class
net\minecraft\world\entity\vehicle\EntityBoat.class
net\minecraft\world\level\block\MultifaceSpreader$a.class
net\minecraft\world\level\block\BlockMushroom.class
org\bukkit\craftbukkit\block\CraftSculkCatalyst.class
net\minecraft\world\level\block\BlockNetherWart.class
net\minecraft\world\entity\animal\sniffer\Sniffer$State.class
net\minecraft\world\entity\npc\EntityVillagerTrader.class
net\minecraft\commands\CommandDispatcher.class
org\bukkit\craftbukkit\block\impl\CraftBlastFurnace.class
org\bukkit\craftbukkit\block\impl\CraftPotatoes.class
net\minecraft\world\inventory\ContainerEnchantTable$3.class
org\bukkit\craftbukkit\entity\CraftItemDisplay.class
net\minecraft\world\entity\animal\horse\EntityLlama$c.class
net\minecraft\world\entity\animal\EntityFox$a.class
net\minecraft\world\inventory\ContainerAnvilAbstract$1.class
net\minecraft\world\level\saveddata\maps\WorldMap$WorldMapHumanTracker.class
org\bukkit\craftbukkit\block\CraftEndGateway.class
net\minecraft\world\level\block\BlockCommand.class
net\minecraft\commands\arguments\selector\EntitySelector.class
net\minecraft\world\entity\projectile\EntityThrownExpBottle.class
net\minecraft\core\dispenser\IDispenseBehavior$24.class
net\minecraft\world\entity\animal\EntityRabbit$Variant.class
net\minecraft\nbt\NBTTagByteArray$1.class
org\bukkit\craftbukkit\inventory\CraftMetaEnchantedBook.class
net\minecraft\world\inventory\ContainerBrewingStand$SlotPotionBottle.class
org\bukkit\craftbukkit\scheduler\CraftTask.class
net\minecraft\network\chat\IChatBaseComponent$ChatSerializer.class
net\minecraft\world\entity\vehicle\EntityMinecartContainer.class
org\bukkit\craftbukkit\block\impl\CraftLeaves.class
net\minecraft\world\entity\monster\EntitySilverfish.class
net\minecraft\world\level\block\BlockRespawnAnchor$1.class
net\minecraft\server\MinecraftServer$TimeProfiler.class
net\minecraft\world\level\saveddata\maps\WorldMap$b.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperBulb.class
net\minecraft\world\entity\animal\goat\Goat.class
net\minecraft\world\level\block\BlockPressurePlateBinary.class
net\minecraft\world\entity\monster\EntityPhantom$h.class
net\minecraft\world\level\block\SculkBlock.class
org\bukkit\craftbukkit\block\data\type\CraftTurtleEgg.class
net\minecraft\world\entity\monster\EntityEnderman.class
org\bukkit\craftbukkit\util\Commodore$1$1.class
net\minecraft\world\entity\monster\EntitySlime$ControllerMoveSlime.class
net\minecraft\world\entity\monster\EntityVex$d.class
net\minecraft\network\chat\ChatHexColor.class
net\minecraft\world\entity\animal\EntityFox$f.class
net\minecraft\world\level\block\PowderSnowBlock.class
net\minecraft\world\level\chunk\Chunk$EnumTileEntityState.class
org\bukkit\craftbukkit\persistence\CraftPersistentDataTypeRegistry.class
net\minecraft\world\entity\animal\EntityTurtle$e.class
net\minecraft\server\commands\CommandReload.class
org\bukkit\craftbukkit\generator\CraftChunkData.class
org\bukkit\craftbukkit\scheduler\CraftScheduler$3.class
org\bukkit\craftbukkit\block\data\CraftFaceAttachable.class
net\minecraft\world\level\block\BlockPortal.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces$b.class
net\minecraft\world\level\block\BlockDropper.class
net\minecraft\world\entity\EntityTypes$b.class
org\bukkit\craftbukkit\block\impl\CraftSoil.class
net\minecraft\world\inventory\ContainerAnvil.class
net\minecraft\world\level\block\BlockMinecartDetector.class
net\minecraft\world\level\block\BlockScaffolding.class
org\bukkit\craftbukkit\block\impl\CraftPiston.class
net\minecraft\world\entity\animal\horse\EntityLlamaTrader$a.class
org\bukkit\craftbukkit\entity\CraftBreeze.class
net\minecraft\world\level\redstone\NeighborUpdater.class
org\bukkit\craftbukkit\inventory\CraftItemCraftResult.class
net\minecraft\world\level\storage\Convertable$a.class
org\bukkit\craftbukkit\metadata\BlockMetadataStore.class
net\minecraft\world\item\ItemSnowball.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$d.class
net\minecraft\server\DispenserRegistry.class
org\bukkit\craftbukkit\entity\CraftTropicalFish$CraftPattern.class
org\bukkit\craftbukkit\help\MultipleCommandAliasHelpTopic.class
org\bukkit\craftbukkit\block\impl\CraftDaylightDetector.class
net\minecraft\server\network\ServerConnection.class
net\minecraft\world\level\block\entity\TileEntity.class
org\bukkit\craftbukkit\Main$1.class
net\minecraft\core\dispenser\IDispenseBehavior$5.class
net\minecraft\world\entity\animal\EntityPanda$f.class
net\minecraft\world\level\GameRules$GameRuleVisitor.class
net\minecraft\world\level\block\BlockLever.class
org\bukkit\craftbukkit\block\CraftEnderChest.class
org\bukkit\craftbukkit\block\impl\CraftCactus.class
net\minecraft\server\MinecraftServer.class
net\minecraft\world\level\block\RootedDirtBlock.class
net\minecraft\world\entity\animal\EntityTurtle$h.class
org\bukkit\craftbukkit\block\impl\CraftCoralPlant.class
net\minecraft\world\entity\monster\EntityPillager.class
net\minecraft\world\level\block\BlockRedstoneTorch.class
org\bukkit\craftbukkit\block\data\CraftDirectional.class
net\minecraft\world\entity\ai\behavior\BehaviorUtil.class
net\minecraft\commands\arguments\blocks\ArgumentBlock.class
org\bukkit\craftbukkit\block\impl\CraftFluids.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$d.class
org\bukkit\craftbukkit\inventory\CraftMetaMusicInstrument.class
net\minecraft\world\entity\animal\EntityPanda$i.class
net\minecraft\world\level\block\LayeredCauldronBlock.class
net\minecraft\commands\CommandDispatcher$b.class
net\minecraft\world\level\block\state\BlockBase$b.class
org\bukkit\craftbukkit\ban\CraftProfileBanEntry.class
net\minecraft\server\level\EntityPlayer$1.class
net\minecraft\world\level\block\entity\DecoratedPotBlockEntity.class
org\bukkit\craftbukkit\entity\CraftCreature.class
net\minecraft\server\network\LoginListener$1.class
net\minecraft\world\entity\raid\EntityRaider$b.class
net\minecraft\world\entity\boss\enderdragon\phases\DragonControllerLandedFlame.class
net\minecraft\world\entity\vehicle\EntityBoat$EnumBoatType.class
net\minecraft\world\entity\animal\EntityCat$b.class
net\minecraft\world\inventory\ContainerGrindstone$4.class
org\bukkit\craftbukkit\block\data\type\CraftBrewingStand.class
org\bukkit\craftbukkit\entity\CraftEntityTypes.class
net\minecraft\world\entity\player\EntityHuman.class
net\minecraft\world\level\block\BlockCoral.class
net\minecraft\server\players\GameProfileBanEntry.class
net\minecraft\world\entity\monster\EntitySkeleton.class
net\minecraft\world\entity\animal\EntityTurtle$c.class
net\minecraft\world\entity\animal\EntityIronGolem$CrackLevel.class
net\minecraft\world\entity\projectile\EntityProjectileThrowable.class
org\bukkit\craftbukkit\CraftRegionAccessor.class
net\minecraft\server\level\PlayerChunk$1.class
net\minecraft\world\inventory\ContainerHopper.class
org\bukkit\craftbukkit\SpigotTimings$WorldTimingsHandler.class
net\minecraft\world\entity\EntityTypes.class
net\minecraft\server\network\ServerConnection$1.class
org\bukkit\craftbukkit\block\impl\CraftWitherSkullWall.class
net\minecraft\world\level\block\entity\DecoratedPotBlockEntity$b.class
net\minecraft\world\entity\monster\EntityIllagerWizard$b.class
net\minecraft\server\level\PlayerChunk$d.class
net\minecraft\world\entity\animal\EntityPanda$d.class
org\bukkit\craftbukkit\block\data\CraftSnowable.class
org\bukkit\craftbukkit\inventory\CraftInventoryDecoratedPot.class
org\bukkit\craftbukkit\block\impl\CraftChorusFlower.class
net\minecraft\world\entity\monster\EntityPhantom$e.class
net\minecraft\world\entity\EntityCreature.class
net\minecraft\world\level\block\BlockStem.class
net\minecraft\world\entity\monster\EntityIllagerWizard$PathfinderGoalCastSpell.class
org\bukkit\craftbukkit\scheduler\CraftScheduler$1.class
org\bukkit\craftbukkit\block\impl\CraftDoor.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneComparator.class
org\bukkit\craftbukkit\entity\CraftHorse.class
net\minecraft\world\entity\animal\EntityParrot$a.class
net\minecraft\world\entity\decoration\EntityArmorStand.class
net\minecraft\world\level\block\MultifaceSpreader$d.class
org\bukkit\craftbukkit\command\ProxiedNativeCommandSender.class
org\bukkit\craftbukkit\inventory\CraftBlockInventoryHolder.class
org\bukkit\craftbukkit\entity\CraftArmorStand.class
org\bukkit\craftbukkit\block\CraftBlockState.class
org\bukkit\craftbukkit\attribute\CraftAttributeInstance.class
net\minecraft\world\level\block\AbstractCandleBlock.class
net\minecraft\server\network\LoginListener$3.class
org\bukkit\craftbukkit\block\impl\CraftButtonAbstract.class
org\bukkit\craftbukkit\block\impl\CraftMangroveLeaves.class
net\minecraft\world\level\block\BlockPressurePlateWeighted.class
net\minecraft\server\level\PlayerChunkMap$4.class
net\minecraft\server\AdvancementDataPlayer.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalDefendVillage.class
org\bukkit\craftbukkit\util\Commodore.class
net\minecraft\world\inventory\ContainerCartography$1.class
org\bukkit\craftbukkit\metadata\EntityMetadataStore.class
net\minecraft\util\worldupdate\WorldUpgrader.class
net\minecraft\world\item\ItemPotion.class
net\minecraft\world\level\block\state\BlockBase$e.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$2.class
org\bukkit\craftbukkit\inventory\CraftInventoryCrafter.class
net\minecraft\network\NetworkManager.class
org\bukkit\craftbukkit\CraftEquipmentSlot.class
net\minecraft\core\dispenser\DispenseBehaviorBoat.class
org\bukkit\craftbukkit\entity\CraftRavager.class
org\bukkit\craftbukkit\block\CraftBanner.class
net\minecraft\world\entity\monster\piglin\EntityPiglin.class
org\bukkit\craftbukkit\metadata\WorldMetadataStore.class
net\minecraft\world\item\ItemEnderPearl.class
org\bukkit\craftbukkit\util\TerminalCompletionHandler.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperSlab.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces$a.class
net\minecraft\world\level\gameevent\vibrations\VibrationSystem$b.class
net\minecraft\world\entity\projectile\EntitySnowball.class
org\bukkit\craftbukkit\help\CustomIndexHelpTopic.class
org\bukkit\craftbukkit\block\impl\CraftCopperBulb.class
net\minecraft\world\level\border\WorldBorder$c.class
net\minecraft\world\item\ItemBucket.class
net\minecraft\util\SpawnUtil$a.class
net\minecraft\world\entity\animal\EntityAnimal.class
org\bukkit\craftbukkit\entity\CraftZombieHorse.class
net\minecraft\core\dispenser\IDispenseBehavior$8.class
org\bukkit\craftbukkit\entity\CraftWarden.class
net\minecraft\server\network\LoginListener.class
net\minecraft\world\item\crafting\CraftingManager$1.class
org\bukkit\craftbukkit\block\impl\CraftJigsaw.class
net\minecraft\server\commands\CommandLoot$c.class
org\bukkit\craftbukkit\block\impl\CraftBigDripleafStem.class
org\bukkit\craftbukkit\block\impl\CraftFenceGate.class
net\minecraft\server\MinecraftServer$ServerResourcePackInfo.class
net\minecraft\world\level\block\LightningRodBlock.class
net\minecraft\world\level\levelgen\ChunkGeneratorAbstract.class
org\bukkit\craftbukkit\scoreboard\CraftObjective.class
org\bukkit\craftbukkit\block\impl\CraftTripwire.class
net\minecraft\world\level\GameRules$GameRuleBoolean.class
org\bukkit\craftbukkit\block\impl\CraftEndRod.class
org\bukkit\craftbukkit\inventory\CraftShapelessRecipe.class
net\minecraft\server\dedicated\DedicatedServer$1.class
org\bukkit\craftbukkit\block\impl\CraftTrapdoor.class
org\bukkit\craftbukkit\CraftParticle.class
org\bukkit\craftbukkit\inventory\CraftInventoryCrafting.class
org\bukkit\craftbukkit\entity\CraftVehicle.class
net\minecraft\world\level\levelgen\structure\structures\MineshaftPieces.class
net\minecraft\world\level\block\BlockRedstoneWire.class
org\bukkit\craftbukkit\inventory\CraftMetaPotion.class
net\minecraft\world\entity\monster\EntitySlime$PathfinderGoalSlimeRandomJump.class
net\minecraft\world\entity\animal\EntityWolf.class
org\bukkit\craftbukkit\block\CraftJukebox.class
org\bukkit\craftbukkit\block\impl\CraftChiseledBookShelf.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$BlastFurnace.class
net\minecraft\world\inventory\ContainerBeacon.class
org\bukkit\craftbukkit\entity\CraftZombie.class
net\minecraft\world\entity\animal\EntityBee$a.class
org\bukkit\craftbukkit\inventory\CraftInventoryCartography.class
net\minecraft\world\inventory\ContainerMerchant.class
org\bukkit\craftbukkit\block\impl\CraftPointedDripstone.class
org\bukkit\craftbukkit\block\data\type\CraftBigDripleaf.class
net\minecraft\server\players\NameReferencingFileConverter.class
org\bukkit\craftbukkit\block\impl\CraftGlowLichen.class
org\bukkit\craftbukkit\entity\CraftShulkerBullet.class
org\bukkit\craftbukkit\block\data\type\CraftRepeater.class
net\minecraft\server\network\PlayerConnection$2.class
net\minecraft\world\entity\EntityExperienceOrb.class
net\minecraft\world\entity\animal\EntityParrot$1.class
net\minecraft\world\level\block\BlockCampfire.class
org\bukkit\craftbukkit\block\data\type\CraftLeaves.class
org\bukkit\craftbukkit\util\UnsafeList.class
net\minecraft\world\level\block\entity\TileEntityBeehive$HiveBee.class
net\minecraft\world\level\GameRules$GameRuleDefinition.class
net\minecraft\server\commands\CommandLoot$a.class
net\minecraft\world\entity\animal\EntityBee.class
org\bukkit\craftbukkit\entity\CraftPhantom.class
org\bukkit\craftbukkit\util\Waitable$Status.class
org\bukkit\craftbukkit\inventory\tags\DeprecatedCustomTagContainer.class
net\minecraft\world\entity\vehicle\VehicleEntity.class
net\minecraft\world\entity\animal\EntityRabbit$PathfinderGoalRabbitAvoidTarget.class
org\bukkit\craftbukkit\block\data\type\CraftBeehive.class
net\minecraft\world\level\gameevent\vibrations\VibrationSystem$d.class
org\bukkit\craftbukkit\block\impl\CraftChestTrapped.class
net\minecraft\world\entity\monster\EntitySpider$GroupDataSpider.class
net\minecraft\world\entity\animal\EntityBee$f.class
org\bukkit\craftbukkit\block\impl\CraftPistonMoving.class
org\bukkit\craftbukkit\block\impl\CraftSkullPlayerWall.class
net\minecraft\world\level\storage\loot\predicates\LootItemConditionRandomChanceWithLooting.class
net\minecraft\network\PacketDataSerializer$a.class
net\minecraft\world\level\block\BigDripleafBlock.class
org\bukkit\craftbukkit\inventory\CraftMetaSuspiciousStew.class
net\minecraft\server\MinecraftServer$ReloadableResources.class
org\bukkit\craftbukkit\CraftWorld$3.class
org\bukkit\craftbukkit\entity\CraftWitherSkeleton.class
org\bukkit\craftbukkit\block\CraftChiseledBookshelf.class
org\bukkit\craftbukkit\entity\CraftSkeletonHorse.class
org\bukkit\craftbukkit\inventory\CraftMetaBook$SpigotMeta$1.class
net\minecraft\server\players\NameReferencingFileConverter$5.class
net\minecraft\world\item\ItemBoneMeal.class
org\bukkit\craftbukkit\entity\CraftMule.class
org\bukkit\craftbukkit\entity\CraftPolarBear.class
net\minecraft\world\entity\ai\behavior\BehaviorAttackTargetForget.class
net\minecraft\world\inventory\ContainerHorse$2.class
net\minecraft\world\entity\animal\EntitySheep.class
org\bukkit\craftbukkit\generator\CustomWorldChunkManager.class
org\bukkit\craftbukkit\block\impl\CraftVine.class
org\bukkit\craftbukkit\entity\CraftHoglin.class
net\minecraft\server\level\PlayerChunk.class
org\bukkit\craftbukkit\entity\CraftEnderDragon.class
org\bukkit\craftbukkit\inventory\CraftComplexRecipe.class
net\minecraft\world\entity\ai\attributes\GenericAttributes.class
org\bukkit\craftbukkit\block\CraftEnchantingTable.class
org\bukkit\craftbukkit\block\data\type\CraftSeaPickle.class
org\bukkit\craftbukkit\inventory\CraftMetaColorableArmor.class
net\minecraft\world\level\dimension\end\EnumDragonRespawn.class
net\minecraft\world\level\block\BlockDoor.class
net\minecraft\world\entity\animal\axolotl\Axolotl$a.class
net\minecraft\world\inventory\InventoryCraftResult.class
org\bukkit\craftbukkit\map\CraftMapColorCache.class
net\minecraft\world\entity\animal\EntityFox.class
org\bukkit\craftbukkit\block\data\type\CraftRedstoneWire.class
org\spigotmc\TicksPerSecondCommand.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$l.class
net\minecraft\server\dedicated\DedicatedServerProperties$WorldDimensionData.class
net\minecraft\world\level\block\CaveVines.class
org\spigotmc\Metrics$Plotter.class
org\bukkit\craftbukkit\block\data\type\CraftChest.class
org\bukkit\craftbukkit\util\permissions\CommandPermissions.class
net\minecraft\core\dispenser\IDispenseBehavior$12.class
net\minecraft\network\protocol\game\PacketPlayInUseItem.class
net\minecraft\world\entity\animal\EntityBee$k.class
net\minecraft\world\entity\animal\EntityBee$l.class
org\bukkit\craftbukkit\inventory\CraftSmokingRecipe.class
net\minecraft\world\level\block\entity\TileEntityChest.class
net\minecraft\world\level\levelgen\ChunkProviderFlat.class
net\minecraft\world\inventory\ContainerLoom.class
net\minecraft\world\level\block\DecoratedPotBlock.class
org\bukkit\craftbukkit\block\data\type\CraftStairs.class
org\bukkit\craftbukkit\entity\CraftEnemy.class
net\minecraft\server\gui\ServerGUI.class
org\bukkit\craftbukkit\entity\CraftMinecartRideable.class
net\minecraft\world\inventory\ContainerSmithing.class
net\minecraft\world\level\chunk\ChunkStatus.class
net\minecraft\world\level\GameRules$GameRuleInt.class
net\minecraft\world\entity\Interaction.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalNearestAttackableTarget.class
net\minecraft\world\item\crafting\CraftingManager$a.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces.class
org\bukkit\craftbukkit\inventory\CraftSmithingTransformRecipe.class
net\minecraft\world\level\SpawnerCreature$c.class
net\minecraft\world\entity\monster\EntityPhantom$c.class
org\bukkit\craftbukkit\block\impl\CraftBeetroot.class
org\bukkit\craftbukkit\boss\CraftBossBar.class
net\minecraft\world\entity\animal\EntityTurtle.class
net\minecraft\world\level\block\BlockDispenser.class
net\minecraft\world\level\levelgen\structure\StructureStart.class
net\minecraft\core\dispenser\IDispenseBehavior$10.class
org\bukkit\craftbukkit\block\impl\CraftPistonExtension.class
org\bukkit\craftbukkit\block\impl\CraftDecoratedPot.class
net\minecraft\world\entity\npc\EntityVillagerTrader$a.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructure$b.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$q.class
net\minecraft\world\entity\ai\sensing\TemptingSensor.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$n.class
org\bukkit\craftbukkit\util\CraftMagicNumbers.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$BrewingStand.class
net\minecraft\world\level\entity\PersistentEntitySectionManager.class
org\bukkit\craftbukkit\inventory\CraftMetaBanner.class
org\bukkit\craftbukkit\Overridden.class
org\bukkit\craftbukkit\inventory\CraftInventoryAbstractHorse.class
net\minecraft\world\level\block\BlockSponge.class
net\minecraft\world\entity\animal\axolotl\Axolotl$b.class
net\minecraft\world\entity\animal\EntityMushroomCow$Type.class
net\minecraft\server\players\NameReferencingFileConverter$1.class
org\bukkit\craftbukkit\entity\CraftFrog.class
net\minecraft\network\PacketDataSerializer$b.class
org\bukkit\craftbukkit\block\data\type\CraftFarmland.class
org\bukkit\craftbukkit\inventory\CraftMetaCompass.class
net\minecraft\world\entity\animal\EntityBee$g.class
org\bukkit\craftbukkit\block\impl\CraftStainedGlassPane.class
net\minecraft\world\item\ItemStack$HideFlags.class
org\bukkit\craftbukkit\entity\CraftWitherSkull.class
org\bukkit\craftbukkit\block\CraftBell.class
org\bukkit\craftbukkit\util\WeakCollection$1.class
net\minecraft\world\level\levelgen\structure\structures\EndCityPieces$3.class
org\bukkit\craftbukkit\entity\CraftBat.class
net\minecraft\world\entity\raid\PersistentRaid.class
net\minecraft\world\level\block\entity\TileEntityBell$a.class
org\spigotmc\ValidateUtils.class
net\minecraft\server\AdvancementDataPlayer$a.class
net\minecraft\world\inventory\ContainerWorkbench.class
org\bukkit\craftbukkit\scoreboard\CraftScore.class
org\bukkit\craftbukkit\entity\CraftLargeFireball.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$p.class
net\minecraft\world\inventory\ContainerCartography$4.class
org\bukkit\craftbukkit\entity\CraftShulker.class
net\minecraft\world\entity\npc\EntityVillagerAbstract.class
org\bukkit\craftbukkit\entity\CraftItem.class
net\minecraft\commands\arguments\blocks\ArgumentBlock$b.class
net\minecraft\world\entity\animal\EntityBee$i.class
org\bukkit\craftbukkit\inventory\CraftMetaTropicalFishBucket.class
org\bukkit\craftbukkit\scoreboard\CraftScoreboardTranslations.class
net\minecraft\world\entity\monster\EntitySlime$PathfinderGoalSlimeIdle.class
org\bukkit\craftbukkit\block\impl\CraftPressurePlateWeighted.class
net\minecraft\server\commands\CommandGive.class
org\bukkit\craftbukkit\inventory\CraftInventoryDoubleChest.class
net\minecraft\world\entity\ai\behavior\BehaviorCareer.class
org\bukkit\craftbukkit\command\VanillaCommandWrapper.class
net\minecraft\world\entity\EntityLiving.class
net\minecraft\world\entity\monster\EntityEvoker.class
net\minecraft\world\entity\vehicle\EntityMinecartCommandBlock$a.class
net\minecraft\network\syncher\DataWatcher.class
org\bukkit\craftbukkit\entity\CraftTrident.class
org\bukkit\craftbukkit\block\CraftBlockSupport.class
net\minecraft\server\commands\CommandSchedule.class
net\minecraft\world\level\block\BlockSign.class
org\bukkit\craftbukkit\inventory\CraftInventoryAnvil.class
org\bukkit\craftbukkit\entity\CraftHumanEntity.class
net\minecraft\world\entity\monster\EntityVex.class
org\bukkit\craftbukkit\block\data\type\CraftPistonHead.class
org\bukkit\craftbukkit\boss\CraftBossBar$FlagContainer.class
org\bukkit\craftbukkit\inventory\CraftInventoryLoom.class
org\bukkit\craftbukkit\inventory\tags\DeprecatedItemTagType.class
net\minecraft\core\dispenser\IDispenseBehavior$7.class
net\minecraft\world\entity\raid\EntityRaider$d.class
org\bukkit\craftbukkit\block\data\CraftAgeable.class
org\bukkit\craftbukkit\CraftFluid.class
org\bukkit\craftbukkit\util\Handleable.class
net\minecraft\world\entity\ambient\EntityBat.class
org\bukkit\craftbukkit\block\data\CraftRail.class
net\minecraft\world\entity\monster\EntityEnderman$a.class
net\minecraft\server\ScoreboardServer.class
net\minecraft\world\entity\monster\EntityEndermite.class
net\minecraft\world\entity\ai\behavior\TryLaySpawnOnWaterNearLand.class
org\bukkit\craftbukkit\inventory\CraftBlastingRecipe.class
org\bukkit\craftbukkit\util\BlockStateListPopulator.class
net\minecraft\advancements\AdvancementTree.class
org\bukkit\craftbukkit\entity\CraftAbstractVillager.class
net\minecraft\server\gui\ServerGUI$2.class
org\bukkit\craftbukkit\event\CraftPortalEvent.class
org\bukkit\craftbukkit\block\impl\CraftCobbleWall.class
org\bukkit\craftbukkit\util\CraftChatMessage.class
org\bukkit\craftbukkit\block\CraftLectern.class
org\bukkit\craftbukkit\inventory\CraftShapedRecipe.class
net\minecraft\world\level\levelgen\structure\structures\NetherFortressPieces.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$1.class
net\minecraft\world\level\block\BlockWitherSkull.class
org\bukkit\craftbukkit\CraftGameEvent.class
net\minecraft\world\entity\ai\behavior\BehaviorAttackTargetSet.class
net\minecraft\world\level\CommandBlockListenerAbstract.class
org\bukkit\craftbukkit\entity\CraftCod.class
org\bukkit\craftbukkit\entity\CraftMinecartChest.class
net\minecraft\server\ScoreboardServer$Action.class
org\bukkit\craftbukkit\block\impl\CraftTallPlant.class
net\minecraft\nbt\NBTCompressedStreamTools.class
net\minecraft\world\item\crafting\RecipeItemStack$Provider.class
net\minecraft\server\level\PlayerChunkMap$EntityTracker.class
net\minecraft\world\inventory\ContainerCartography$2.class
net\minecraft\world\entity\animal\EntityOcelot$b.class
net\minecraft\world\food\FoodMetaData.class
net\minecraft\world\level\chunk\ChunkStatus$c.class
org\bukkit\craftbukkit\entity\CraftChestBoat.class
net\minecraft\world\entity\animal\camel\Camel$a.class
net\minecraft\world\level\block\MultifaceSpreader$c.class
org\bukkit\craftbukkit\entity\CraftEgg.class
org\bukkit\craftbukkit\inventory\RecipeIterator.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructure.class
org\bukkit\craftbukkit\inventory\CraftInventoryHorse.class
net\minecraft\world\level\block\BlockTripwireHook.class
net\minecraft\world\entity\projectile\EntityFireworks.class
net\minecraft\core\dispenser\IDispenseBehavior$13.class
org\bukkit\craftbukkit\block\CraftChest.class
org\bukkit\craftbukkit\block\data\type\CraftBubbleColumn.class
net\minecraft\world\entity\animal\EntityCow.class
net\minecraft\world\level\block\BlockVine.class
org\bukkit\craftbukkit\block\impl\CraftCandle.class
org\bukkit\craftbukkit\Main.class
net\minecraft\world\inventory\ContainerLoom$2.class
org\bukkit\craftbukkit\inventory\util\CraftInventoryCreator.class
org\bukkit\craftbukkit\structure\CraftStructureManager.class
org\bukkit\craftbukkit\entity\CraftCat.class
org\bukkit\craftbukkit\block\impl\CraftWeatheringCopperStair.class
net\minecraft\util\datafix\DataConverterRegistry$3.class
org\bukkit\craftbukkit\entity\CraftSpellcaster.class
net\minecraft\world\entity\EntityInsentient.class
net\minecraft\world\level\block\BlockCake.class
org\bukkit\craftbukkit\block\impl\CraftWeepingVines.class
org\bukkit\craftbukkit\block\impl\CraftTarget.class
org\bukkit\craftbukkit\entity\CraftChicken.class
net\minecraft\network\protocol\game\PacketPlayInBlockPlace.class
net\minecraft\world\entity\Entity.class
net\minecraft\world\level\block\entity\TileEntitySign.class
org\bukkit\craftbukkit\entity\CraftSmallFireball.class
net\minecraft\core\dispenser\IDispenseBehavior$18.class
org\bukkit\craftbukkit\CraftChunk.class
net\minecraft\world\level\RayTrace.class
net\minecraft\world\entity\monster\EntityEvoker$b.class
org\bukkit\craftbukkit\attribute\CraftAttribute.class
org\bukkit\craftbukkit\profile\CraftPlayerProfile.class
net\minecraft\world\level\block\BlockTurtleEgg.class
org\bukkit\craftbukkit\util\CraftBlockVector.class
net\minecraft\world\entity\animal\EntityPanda$c.class
org\bukkit\craftbukkit\entity\CraftInteraction.class
net\minecraft\world\level\Explosion$Effect.class
net\minecraft\world\inventory\ContainerAnvilAbstract$3.class
org\bukkit\craftbukkit\CraftServer$4.class
net\minecraft\server\level\WorldServer.class
org\bukkit\craftbukkit\entity\CraftEntity$2.class
net\minecraft\world\entity\monster\EntityPhantom$f.class
org\bukkit\craftbukkit\util\CraftIconCache.class
org\bukkit\craftbukkit\inventory\CraftMetaSkull.class
net\minecraft\server\MinecraftServer$1.class
net\minecraft\server\rcon\thread\RemoteControlSession.class
org\bukkit\craftbukkit\block\impl\CraftInfestedRotatedPillar.class
net\minecraft\commands\CommandListenerWrapper.class
net\minecraft\server\level\WorldServer$a.class
net\minecraft\world\entity\animal\EntityRabbit$PathfinderGoalRabbitPanic.class
net\minecraft\world\level\levelgen\MobSpawnerPhantom.class
net\minecraft\core\dispenser\IDispenseBehavior$21.class
net\minecraft\server\network\PlayerConnection$3.class
net\minecraft\world\item\crafting\SmithingTransformRecipe.class
net\minecraft\world\inventory\ContainerAccess$1.class
net\minecraft\world\entity\animal\EntityTurtle$b.class
org\bukkit\craftbukkit\inventory\tags\DeprecatedContainerTagType.class
org\bukkit\craftbukkit\block\CraftLootable.class
net\minecraft\world\entity\animal\EntityBee$b.class
org\bukkit\craftbukkit\packs\CraftResourcePack.class
net\minecraft\world\inventory\ContainerGrindstone$3.class
net\minecraft\world\level\chunk\storage\RegionFile.class
net\minecraft\world\entity\animal\EntityPanda$g.class
net\minecraft\server\players\UserCache.class
org\bukkit\craftbukkit\block\data\type\CraftNoteBlock.class
org\bukkit\craftbukkit\util\CraftChatMessage$StringMessage.class
org\bukkit\craftbukkit\util\CraftLocation.class
net\minecraft\world\level\chunk\Chunk.class
net\minecraft\world\level\storage\loot\LootDataManager.class
net\minecraft\core\dispenser\IDispenseBehavior$17.class
net\minecraft\world\level\block\entity\TileEntityShulkerBox$AnimationPhase.class
org\bukkit\craftbukkit\command\ServerCommandSender$1.class
net\minecraft\world\entity\monster\EntityWitch.class
org\bukkit\craftbukkit\entity\CraftVex.class
net\minecraft\world\inventory\ContainerLoom$3.class
net\minecraft\world\item\ItemHanging.class
org\bukkit\craftbukkit\CraftServer$3.class
net\minecraft\world\entity\monster\warden\Warden$a.class
org\bukkit\craftbukkit\block\CraftFurnace.class
org\bukkit\craftbukkit\command\CraftRemoteConsoleCommandSender.class
org\bukkit\craftbukkit\block\impl\CraftJukeBox.class
org\bukkit\craftbukkit\inventory\CraftCampfireRecipe.class
net\minecraft\world\item\ItemMilkBucket.class
org\bukkit\craftbukkit\scoreboard\CraftTeam.class
net\minecraft\world\entity\animal\EntityPanda$l.class
net\minecraft\world\item\ItemSign.class
net\minecraft\core\dispenser\IDispenseBehavior$25.class
net\minecraft\world\level\storage\Convertable$ConversionSession$2.class
net\minecraft\world\level\RayTrace$BlockCollisionOption.class
net\minecraft\world\entity\monster\EntityGhast$ControllerGhast.class
org\bukkit\craftbukkit\entity\CraftSheep.class
net\minecraft\world\level\block\entity\TileEntityBarrel$1.class
net\minecraft\world\entity\decoration\EntityItemFrame$1.class
net\minecraft\CrashReport.class
net\minecraft\world\level\chunk\ChunkStatus$d.class
org\bukkit\craftbukkit\block\impl\CraftCocoa.class
org\bukkit\craftbukkit\inventory\CraftInventoryChiseledBookshelf.class
net\minecraft\world\entity\EntityTypes$Builder.class
org\bukkit\craftbukkit\block\impl\CraftTallPlantFlower.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalOwnerHurtTarget.class
org\bukkit\craftbukkit\block\data\CraftOpenable.class
org\bukkit\craftbukkit\scheduler\CraftAsyncTask.class
org\bukkit\craftbukkit\persistence\CraftPersistentDataTypeRegistry$TagAdapter.class
net\minecraft\world\level\block\entity\SculkCatalystBlockEntity.class
org\bukkit\craftbukkit\block\impl\CraftSweetBerryBush.class
org\bukkit\craftbukkit\inventory\CraftMetaFirework.class
org\bukkit\craftbukkit\block\CraftMovingPiston.class
net\minecraft\world\entity\boss\enderdragon\EntityEnderDragon.class
net\minecraft\world\level\block\SculkSpreader$a.class
net\minecraft\world\inventory\ContainerBrewingStand.class
org\bukkit\craftbukkit\scheduler\CraftScheduler.class
net\minecraft\world\entity\animal\EntityTurtle$f.class
net\minecraft\world\entity\ai\behavior\warden\Digging.class
net\minecraft\world\level\block\WallHangingSignBlock.class
net\minecraft\world\level\block\entity\TileEntityBeacon$1.class
org\bukkit\craftbukkit\block\impl\CraftSculkVein.class
org\bukkit\craftbukkit\entity\CraftRaider.class
org\bukkit\craftbukkit\util\LazyPlayerSet.class
net\minecraft\server\level\RegionLimitedWorldAccess.class
org\bukkit\craftbukkit\entity\CraftPillager.class
net\minecraft\world\level\chunk\ChunkGenerator.class
org\bukkit\craftbukkit\block\CraftHopper.class
net\minecraft\world\level\block\BlockComposter$ContainerEmpty.class
net\minecraft\world\level\block\BlockWitherRose.class
net\minecraft\world\entity\projectile\EntityDragonFireball.class
net\minecraft\world\level\WorldAccess.class
net\minecraft\world\inventory\ContainerBrewingStand$SlotBrewing.class
net\minecraft\world\entity\animal\EntityTurtle$g.class
net\minecraft\world\entity\animal\horse\EntityHorseAbstract$1.class
org\bukkit\craftbukkit\entity\CraftCow.class
net\minecraft\world\level\block\state\BlockBase$Info.class
org\bukkit\craftbukkit\inventory\CraftMetaLeatherArmor.class
net\minecraft\world\inventory\ContainerGrindstone$2.class
net\minecraft\world\entity\animal\EntityCat$PathfinderGoalTemptChance.class
net\minecraft\world\entity\projectile\EntitySmallFireball.class
org\bukkit\craftbukkit\block\CraftBrushableBlock.class
net\minecraft\world\inventory\ContainerAnvilAbstract.class
net\minecraft\world\entity\monster\EntityZombieVillager.class
org\bukkit\craftbukkit\block\CraftStructureBlock.class
org\bukkit\craftbukkit\block\impl\CraftObserver.class
org\bukkit\craftbukkit\entity\CraftEntitySnapshot.class
org\bukkit\craftbukkit\CraftSoundGroup.class
net\minecraft\server\dedicated\DedicatedServer.class
net\minecraft\world\entity\monster\EntityIllagerWizard$Spell.class
net\minecraft\network\protocol\game\ClientboundSystemChatPacket.class
net\minecraft\world\entity\animal\EntityPerchable.class
net\minecraft\world\inventory\ContainerDispenser.class
net\minecraft\server\commands\CommandTeleport$a.class
net\minecraft\core\dispenser\IDispenseBehavior$26.class
net\minecraft\world\entity\animal\EntityFox$Type.class
net\minecraft\world\entity\animal\EntityRabbit$ControllerJumpRabbit.class
org\bukkit\craftbukkit\block\impl\CraftCoralFanWallAbstract.class
org\bukkit\craftbukkit\inventory\CraftMetaArmorStand.class
net\minecraft\server\rcon\RemoteControlCommandListener.class
org\bukkit\craftbukkit\inventory\CraftInventoryGrindstone.class
net\minecraft\world\entity\raid\EntityRaider$c.class
org\bukkit\craftbukkit\entity\CraftMagmaCube.class
net\minecraft\world\level\block\entity\TileEntityBrewingStand.class
org\bukkit\craftbukkit\block\CraftBrewingStand.class
org\bukkit\craftbukkit\block\impl\CraftSmallDripleaf.class
net\minecraft\server\network\PlayerConnection.class
net\minecraft\core\dispenser\IDispenseBehavior$2.class
org\bukkit\craftbukkit\inventory\InventoryIterator.class
org\bukkit\craftbukkit\block\data\type\CraftBrushable.class
org\bukkit\craftbukkit\block\data\type\CraftBell.class
net\minecraft\util\datafix\fixes\DataConverterFlatten.class
net\minecraft\world\entity\animal\EntityPig.class
net\minecraft\world\inventory\ContainerCartography.class
org\bukkit\craftbukkit\damage\CraftDamageSourceBuilder.class
net\minecraft\world\entity\ai\goal\target\PathfinderGoalTarget.class
org\bukkit\craftbukkit\inventory\CraftMetaMap.class
net\minecraft\core\dispenser\IDispenseBehavior$3.class
org\bukkit\craftbukkit\entity\CraftEnderCrystal.class
org\bukkit\craftbukkit\util\Versioning.class
org\bukkit\craftbukkit\entity\CraftPig.class
net\minecraft\world\entity\ai\village\VillageSiege$State.class
net\minecraft\world\level\levelgen\structure\structures\StrongholdPieces$p$a.class
org\bukkit\craftbukkit\command\CraftBlockCommandSender$1.class
net\minecraft\world\entity\animal\EntityPanda$h.class
net\minecraft\world\entity\EntityTypes$1.class
org\bukkit\craftbukkit\block\impl\CraftBrewingStand.class
net\minecraft\core\dispenser\IDispenseBehavior$27.class
org\bukkit\craftbukkit\block\data\CraftHatchable.class
org\spigotmc\TrackingRange.class
org\bukkit\craftbukkit\block\CraftEndPortal.class
net\minecraft\world\inventory\ContainerBeacon$SlotBeacon.class
net\minecraft\world\level\portal\PortalTravelAgent.class
net\minecraft\nbt\NBTCompressedStreamTools$a.class
org\bukkit\craftbukkit\entity\CraftTraderLlama.class
org\bukkit\craftbukkit\bootstrap\Main$ResourceParser.class
net\minecraft\world\entity\EntityLiving$ProcessableEffect.class
org\bukkit\craftbukkit\profile\CraftProfileProperty.class
org\bukkit\craftbukkit\tag\CraftTag.class
net\minecraft\server\MinecraftServer$TimeProfiler$1.class
org\bukkit\craftbukkit\help\MultipleCommandAliasHelpTopicFactory.class
net\minecraft\world\inventory\ContainerLoom$6.class
org\bukkit\craftbukkit\block\impl\CraftCrops.class
net\minecraft\world\entity\monster\EntityCreeper.class
net\minecraft\world\entity\projectile\EntityWitherSkull.class
net\minecraft\world\level\block\entity\TileEntityChest$1.class
org\bukkit\craftbukkit\entity\CraftDragonFireball.class
org\bukkit\craftbukkit\block\impl\CraftGrass.class
net\minecraft\world\level\block\BlockLectern.class
net\minecraft\world\item\crafting\RecipeItemStack$StackProvider.class
net\minecraft\world\item\crafting\SmithingTransformRecipe$a.class
org\bukkit\craftbukkit\block\impl\CraftCoralFanWall.class
org\bukkit\craftbukkit\CraftRegistry.class
org\bukkit\craftbukkit\entity\CraftPigZombie.class
net\minecraft\advancements\AdvancementHolder.class
net\minecraft\core\dispenser\IDispenseBehavior$1.class
net\minecraft\server\commands\CommandWorldBorder.class
net\minecraft\world\item\trading\MerchantRecipe.class
org\bukkit\craftbukkit\block\CraftJigsaw.class
org\bukkit\craftbukkit\entity\CraftGlowItemFrame.class
org\bukkit\craftbukkit\block\data\CraftBrushable.class
org\bukkit\craftbukkit\CraftFeatureFlag.class
net\minecraft\world\level\block\Block$4.class
net\minecraft\world\damagesource\DamageSource.class
net\minecraft\server\network\PacketStatusListener$1ServerListPingEvent.class
org\bukkit\craftbukkit\scoreboard\CraftScoreboardManager.class
org\bukkit\craftbukkit\entity\CraftSkeleton.class
net\minecraft\server\network\HandshakeListener.class
net\minecraft\world\inventory\ContainerGrindstone$1.class
net\minecraft\world\level\block\BlockPoweredRail.class
org\bukkit\craftbukkit\inventory\util\CraftTileInventoryConverter$Smoker.class
net\minecraft\world\item\crafting\CraftingManager.class
org\bukkit\craftbukkit\CraftStatistic.class
net\minecraft\server\network\PacketStatusListener.class
net\minecraft\world\entity\animal\EntityPanda$k.class
org\bukkit\craftbukkit\block\impl\CraftWallHangingSign.class
org\bukkit\craftbukkit\CraftHeightMap.class
net\minecraft\world\level\storage\Convertable$ConversionSession$1.class
net\minecraft\world\entity\monster\EntityEvoker$a.class
net\minecraft\world\level\block\entity\TileEntityCommand$Type.class
net\minecraft\world\level\storage\loot\LootTable.class
net\minecraft\world\inventory\ContainerAccess$2.class
net\minecraft\world\effect\MobEffectUtil.class
net\minecraft\core\dispenser\IDispenseBehavior$20.class
org\bukkit\craftbukkit\entity\CraftComplexPart.class
org\bukkit\craftbukkit\block\CraftBlastFurnace.class
net\minecraft\commands\ICommandListener.class
org\spigotmc\RestartCommand$2.class
org\bukkit\craftbukkit\CraftServer$2.class
org\bukkit\craftbukkit\CraftServer$5.class
org\bukkit\craftbukkit\block\CraftShulkerBox.class
net\minecraft\server\network\ServerConfigurationPacketListenerImpl.class
net\minecraft\world\level\block\BlockFluids.class
org\bukkit\craftbukkit\entity\CraftIllager.class
net\minecraft\world\item\crafting\IRecipe.class
org\bukkit\craftbukkit\potion\CraftPotionBrewer.class
net\minecraft\world\level\storage\loot\functions\LootEnchantFunction$a.class
org\bukkit\craftbukkit\help\SimpleHelpMap$IsCommandTopicPredicate.class
org\bukkit\craftbukkit\util\WorldUUID.class
org\bukkit\craftbukkit\entity\CraftFallingBlock.class
org\bukkit\craftbukkit\util\RandomSourceWrapper$RandomWrapper.class
org\bukkit\craftbukkit\inventory\CraftItemFactory.class
org\spigotmc\LimitStream.class
net\minecraft\world\entity\monster\warden\Warden.class
org\bukkit\craftbukkit\profile\CraftProfileProperty$JsonFormatter.class
org\bukkit\craftbukkit\block\impl\CraftAmethystCluster.class
org\bukkit\craftbukkit\block\impl\CraftIronBars.class
net\minecraft\world\level\block\BlockSoil.class
net\minecraft\world\level\storage\WorldDataServer$a.class
org\bukkit\craftbukkit\block\impl\CraftAnvil.class
net\minecraft\world\entity\animal\EntityParrot$Variant.class
org\bukkit\craftbukkit\block\impl\CraftLadder.class
net\minecraft\world\level\chunk\Chunk$1.class
net\minecraft\world\entity\monster\EntityEvoker$c.class
net\minecraft\world\entity\animal\EntitySnowman.class
net\minecraft\server\gui\ServerGUI$1.class
net\minecraft\world\entity\item\EntityItem.class
net\minecraft\world\level\block\entity\TileEntityBeacon$BeaconColorTracker.class
net\minecraft\world\level\block\BlockTallPlant.class
net\minecraft\world\level\storage\Convertable.class
net\minecraft\server\ServerTickRateManager.class
net\minecraft\world\level\levelgen\structure\templatesystem\DefinedStructure$BlockInfo.class
net\minecraft\world\level\block\entity\TileEntityHopper.class
net\minecraft\world\inventory\ContainerLoom$1.class
net\minecraft\world\item\crafting\RecipeItemStack.class
net\minecraft\core\dispenser\IDispenseBehavior$22.class
net\minecraft\world\entity\animal\horse\EntityLlama.class
net\minecraft\world\level\block\BlockDragonEgg.class
net\minecraft\world\entity\projectile\EntityLargeFireball.class
net\minecraft\world\level\block\BlockFire.class
net\minecraft\world\entity\monster\EntityEvoker$d.class
org\bukkit\craftbukkit\block\impl\CraftTNT.class
org\bukkit\craftbukkit\block\data\type\CraftSlab.class
net\minecraft\world\level\levelgen\structure\structures\OceanRuinPieces.class
net\minecraft\world\level\block\entity\TileEntityBrewingStand$1.class
net\minecraft\util\datafix\DataConverterRegistry$2.class
net\minecraft\world\level\block\BlockGrowingTop.class
org\bukkit\craftbukkit\block\impl\CraftMangroveRoots.class
org\bukkit\craftbukkit\damage\CraftDamageType.class
net\minecraft\server\commands\PlaceCommand.class
net\minecraft\world\inventory\CrafterMenu.class
net\minecraft\world\entity\animal\horse\EntityHorseSkeleton.class
org\bukkit\craftbukkit\block\data\CraftBisected.class
org\bukkit\craftbukkit\block\impl\CraftCrafter.class
net\minecraft\world\level\block\BlockPlant.class
org\bukkit\craftbukkit\block\impl\CraftPressurePlateBinary.class
org\bukkit\craftbukkit\inventory\CraftMetaEntityTag.class
net\minecraft\world\entity\projectile\EntityShulkerBullet.class
net\minecraft\world\level\levelgen\structure\structures\DesertPyramidStructure.class
org\bukkit\craftbukkit\command\CraftBlockCommandSender.class
net\minecraft\world\entity\EntityLiving$a.class
net\minecraft\world\level\block\entity\TileEntityCommand.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$6.class
org\bukkit\craftbukkit\inventory\CraftMetaItem$ItemMetaKey.class
net\minecraft\world\level\block\BlockBell.class
org\bukkit\craftbukkit\CraftArt.class
org\bukkit\craftbukkit\entity\CraftEntity$1.class
net\minecraft\server\players\UserCache$UserCacheEntry.class
net\minecraft\server\level\ChunkMapDistance.class
net\minecraft\world\entity\monster\EntityIllagerWizard.class
org\bukkit\craftbukkit\scoreboard\CraftCriteria.class
org\bukkit\craftbukkit\util\CraftNamespacedKey.class
net\minecraft\world\level\dimension\end\EnderDragonBattle$a.class
org\bukkit\craftbukkit\entity\CraftGolem.class
net\minecraft\world\entity\animal\EntityOcelot.class
net\minecraft\world\inventory\Container$1.class
net\minecraft\world\level\block\BlockChest$DoubleInventory.class
org\spigotmc\RestartCommand$1.class
org\bukkit\craftbukkit\entity\CraftPlayer$2.class
org\bukkit\craftbukkit\block\impl\CraftLoom.class
net\minecraft\world\entity\ai\village\VillageSiege.class
org\bukkit\craftbukkit\block\data\type\CraftBamboo.class
org\bukkit\craftbukkit\entity\CraftCaveSpider.class
org\bukkit\craftbukkit\block\CapturedBlockState.class
org\bukkit\craftbukkit\profile\CraftProfileProperty$JsonFormatter$1.class
net\minecraft\world\level\RayTrace$FluidCollisionOption.class
org\bukkit\craftbukkit\entity\CraftWaterMob.class
net\minecraft\world\entity\ai\goal\PathfinderGoalRemoveBlock.class
org\bukkit\craftbukkit\block\impl\CraftHopper.class
net\minecraft\server\level\PlayerInteractManager.class
org\bukkit\craftbukkit\block\impl\CraftSculkSensor.class
net\minecraft\world\inventory\ContainerShulkerBox.class
org\bukkit\craftbukkit\block\impl\CraftCaveVines.class
net\minecraft\world\level\border\WorldBorder.class
net\minecraft\core\dispenser\IDispenseBehavior.class
net\minecraft\world\level\block\entity\TileEntityCampfire.class
net\minecraft\world\inventory\Containers$Supplier.class
org\bukkit\craftbukkit\block\impl\CraftTurtleEgg.class
net\minecraft\world\entity\monster\EntitySlime.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$1.class
org\bukkit\craftbukkit\entity\CraftPlayer$3.class
org\bukkit\craftbukkit\util\CraftSpawnCategory.class
org\bukkit\craftbukkit\block\CraftSculkSensor.class
org\bukkit\craftbukkit\entity\CraftEnderSignal.class
net\minecraft\world\level\block\Block$3.class
org\spigotmc\RestartCommand.class
org\bukkit\craftbukkit\entity\CraftWolf.class
net\minecraft\world\level\levelgen\structure\StructurePiece$StructurePieceBlockSelector.class
org\bukkit\craftbukkit\entity\CraftAgeable.class
net\minecraft\world\entity\monster\EntityCaveSpider.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$7.class
net\minecraft\world\level\block\BlockCoralFan.class
net\minecraft\world\item\crafting\SmithingTrimRecipe.class
net\minecraft\world\entity\animal\EntityCat.class
net\minecraft\server\dedicated\DedicatedServerProperties.class
org\bukkit\craftbukkit\block\data\type\CraftPiston.class
org\bukkit\craftbukkit\entity\CraftEvoker.class
org\bukkit\craftbukkit\block\CraftBlockStates$BlockEntityStateFactory.class
net\minecraft\world\entity\monster\warden\Warden$1$1.class
net\minecraft\world\item\ItemLeash.class
org\bukkit\craftbukkit\entity\CraftAbstractHorse.class
org\bukkit\craftbukkit\block\data\type\CraftSapling.class
org\bukkit\craftbukkit\entity\CraftAllay.class
net\minecraft\world\entity\animal\EntityWolf$b.class
org\bukkit\craftbukkit\block\data\type\CraftSculkShrieker.class
net\minecraft\world\level\World.class
org\bukkit\craftbukkit\entity\CraftMob.class
net\minecraft\world\level\block\MultifaceSpreader$e$3.class
net\minecraft\world\entity\EntityAreaEffectCloud.class
net\minecraft\world\entity\projectile\EntityProjectile.class
net\minecraft\world\level\block\BlockWaterLily.class
net\minecraft\world\inventory\ContainerLectern$1.class
net\minecraft\world\level\block\entity\TileEntityBell.class
net\minecraft\world\level\storage\WorldDataServer.class
org\bukkit\craftbukkit\block\impl\CraftStemAttached.class
org\bukkit\craftbukkit\util\CraftMagicNumbers$NBT.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$2.class
org\bukkit\craftbukkit\potion\CraftPotionUtil.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$5.class
org\bukkit\craftbukkit\entity\CraftItemFrame.class
org\bukkit\craftbukkit\map\CraftMapRenderer.class
org\bukkit\craftbukkit\block\CraftSkull.class
net\minecraft\world\entity\animal\EntityWolf$a.class
net\minecraft\world\entity\monster\EntityGuardian.class
net\minecraft\world\level\block\BlockGrassPath.class
net\minecraft\world\item\ItemChorusFruit.class
net\minecraft\world\effect\SaturationMobEffect.class
org\bukkit\craftbukkit\block\CraftBlockType.class
net\minecraft\world\entity\projectile\EntitySpectralArrow.class
org\bukkit\craftbukkit\block\data\type\CraftLectern.class
org\bukkit\craftbukkit\command\CraftCommandMap.class
org\bukkit\craftbukkit\block\impl\CraftTallSeagrass.class
net\minecraft\server\commands\CommandSpreadPlayers$a.class
net\minecraft\world\level\block\MultifaceSpreader$e$2.class
net\minecraft\server\network\ServerConnection$LatencySimulator$DelayedMessage.class
org\bukkit\craftbukkit\CraftParticle$CraftParticleRegistry$4.class
org\bukkit\craftbukkit\inventory\CraftContainer.class
net\minecraft\world\entity\npc\MobSpawnerTrader.class
net\minecraft\world\level\block\MultifaceSpreader$e$1.class
net\minecraft\world\level\block\entity\TileEntityBeehive.class
org\bukkit\craftbukkit\block\impl\CraftRedstoneOre.class
net\minecraft\world\item\crafting\RecipeHolder.class
org\bukkit\craftbukkit\persistence\CraftPersistentDataContainer.class
org\bukkit\craftbukkit\inventory\CraftContainer$1.class
net\minecraft\world\entity\animal\axolotl\Axolotl.class
net\minecraft\network\syncher\DataWatcher$b.class
net\minecraft\world\level\block\entity\TileEntitySkull$1.class
org\bukkit\craftbukkit\inventory\CraftInventoryBrewer.class
