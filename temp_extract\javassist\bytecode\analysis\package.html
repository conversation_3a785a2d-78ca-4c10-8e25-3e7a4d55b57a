<html>
<body>
Bytecode Analysis API.

<p>This package provides an API for performing data-flow analysis on a method's bytecode.
This allows the user to determine the type state of the stack and local variable table 
at the start of every instruction. In addition this API can be used to validate 
bytecode, find dead bytecode, and identify unnecessary checkcasts.
Look at <code>ControlFlow</code> class first for details.

<p>The users of this package must know the specifications of
class file and Java bytecode.  For more details, read this book:

<ul><li><PERSON> and <PERSON>,
"The Java Virtual Machine Specification 2nd Ed.",
Addison-Wesley, 1999.
</li></ul>

</body>
</html>
