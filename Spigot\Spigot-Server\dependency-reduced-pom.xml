<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>spigot-parent</artifactId>
    <groupId>org.spigotmc</groupId>
    <version>dev-SNAPSHOT</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.spigotmc</groupId>
  <artifactId>spigot</artifactId>
  <name>Spigot</name>
  <version>1.20.4-R0.1-SNAPSHOT</version>
  <url>https://www.spigotmc.org/</url>
  <build>
    <plugins>
      <plugin>
        <groupId>net.md-5</groupId>
        <artifactId>scriptus</artifactId>
        <version>0.5.0</version>
        <executions>
          <execution>
            <id>ex-spigot</id>
            <phase>initialize</phase>
            <goals>
              <goal>describe</goal>
            </goals>
            <configuration>
              <format>${bt.name}-Spigot-%s</format>
              <scmDirectory>../</scmDirectory>
              <descriptionProperty>spigot.desc</descriptionProperty>
            </configuration>
          </execution>
          <execution>
            <id>ex-craftbukkit</id>
            <phase>initialize</phase>
            <goals>
              <goal>describe</goal>
            </goals>
            <configuration>
              <format>-%s</format>
              <scmDirectory>../../CraftBukkit</scmDirectory>
              <descriptionProperty>craftbukkit.desc</descriptionProperty>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <phase>initialize</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <archive>
            <manifest>
              <addDefaultEntries>false</addDefaultEntries>
            </manifest>
            <manifestEntries>
              <Main-Class>org.bukkit.craftbukkit.Main</Main-Class>
              <Implementation-Title>CraftBukkit</Implementation-Title>
              <Implementation-Version>${spigot.desc}${craftbukkit.desc}</Implementation-Version>
              <Implementation-Vendor>${project.build.outputTimestamp}</Implementation-Vendor>
              <Specification-Title>Bukkit</Specification-Title>
              <Specification-Version>${api.version}</Specification-Version>
              <Specification-Vendor>Bukkit Team</Specification-Vendor>
              <Multi-Release>true</Multi-Release>
            </manifestEntries>
            <manifestSections>
              <manifestSection>
                <name>net/bukkit/</name>
                <manifestEntries>
                  <Sealed>true</Sealed>
                </manifestEntries>
              </manifestSection>
              <manifestSection>
                <name>com/bukkit/</name>
                <manifestEntries>
                  <Sealed>true</Sealed>
                </manifestEntries>
              </manifestSection>
              <manifestSection>
                <name>org/bukkit/</name>
                <manifestEntries>
                  <Sealed>true</Sealed>
                </manifestEntries>
              </manifestSection>
            </manifestSections>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <createSourcesJar>${shadeSourcesJar}</createSourcesJar>
              <artifactSet>
                <includes>
                  <include>org.spigotmc:minecraft-server</include>
                </includes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>org.bukkit.craftbukkit</pattern>
                  <shadedPattern>org.bukkit.craftbukkit.v${minecraft_version}</shadedPattern>
                  <excludes>
                    <exclude>org.bukkit.craftbukkit.bootstrap.*</exclude>
                    <exclude>org.bukkit.craftbukkit.Main*</exclude>
                  </excludes>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.md-5</groupId>
        <artifactId>specialsource-maven-plugin</artifactId>
        <version>2.0.2</version>
        <executions>
          <execution>
            <id>remap-members</id>
            <phase>package</phase>
            <goals>
              <goal>remap</goal>
            </goals>
            <configuration>
              <useProjectDependencies>false</useProjectDependencies>
              <logFile>${project.build.directory}/server.txt</logFile>
              <srgIn>org.spigotmc:minecraft-server:${project.version}:csrg:maps-spigot-members</srgIn>
              <reverse>true</reverse>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>net.nicoulaj.maven.plugins</groupId>
        <artifactId>checksum-maven-plugin</artifactId>
        <version>1.11</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>artifacts</goal>
              <goal>dependencies</goal>
            </goals>
            <configuration>
              <algorithms>
                <algorithm>SHA-256</algorithm>
              </algorithms>
              <quiet>true</quiet>
              <scopes>
                <scope>compile</scope>
                <scope>runtime</scope>
              </scopes>
              <shasumSummary>true</shasumSummary>
              <transitive>true</transitive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.6.0</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <archive>
                <manifest>
                  <addDefaultEntries>false</addDefaultEntries>
                </manifest>
                <manifestEntries>
                  <Main-Class>org.bukkit.craftbukkit.bootstrap.Main</Main-Class>
                </manifestEntries>
              </archive>
              <attach>false</attach>
              <descriptors>
                <descriptor>${project.basedir}/src/assembly/bootstrap.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <dependencies>
          <dependency>
            <groupId>org.codehaus.plexus</groupId>
            <artifactId>plexus-compiler-eclipse</artifactId>
            <version>2.13.0</version>
          </dependency>
        </dependencies>
        <configuration>
          <compilerId>eclipse</compilerId>
          <showWarnings>false</showWarnings>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <workingDirectory>${basedir}/target/test-server</workingDirectory>
          <excludes>
            <exclude>org/bukkit/craftbukkit/inventory/ItemStack*Test.java</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>shadeSourcesJar</id>
      <properties>
        <shadeSourcesContent>true</shadeSourcesContent>
        <shadeSourcesJar>true</shadeSourcesJar>
      </properties>
    </profile>
    <profile>
      <id>development</id>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>3.3.0</version>
            <executions>
              <execution>
                <phase>test-compile</phase>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>8.45.1</version>
              </dependency>
            </dependencies>
            <configuration>
              <configLocation>checkstyle.xml</configLocation>
              <includeTestSourceDirectory>true</includeTestSourceDirectory>
            </configuration>
          </plugin>
        </plugins>
      </build>
      <properties>
        <skipTests>false</skipTests>
      </properties>
    </profile>
    <profile>
      <id>remapped</id>
      <build>
        <plugins>
          <plugin>
            <groupId>net.md-5</groupId>
            <artifactId>specialsource-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>remap-obf</id>
                <phase>verify</phase>
                <goals>
                  <goal>remap</goal>
                </goals>
                <configuration>
                  <useProjectDependencies>false</useProjectDependencies>
                  <srgIn>org.spigotmc:minecraft-server:${project.version}:csrg:maps-spigot</srgIn>
                  <reverse>true</reverse>
                  <remappedArtifactAttached>true</remappedArtifactAttached>
                  <remappedClassifierName>remapped-obf</remappedClassifierName>
                </configuration>
              </execution>
              <execution>
                <id>remap-mojang</id>
                <phase>verify</phase>
                <goals>
                  <goal>remap</goal>
                </goals>
                <configuration>
                  <useProjectDependencies>false</useProjectDependencies>
                  <inputFile>${project.build.directory}/${project.artifactId}-${project.version}-remapped-obf.jar</inputFile>
                  <srgIn>org.spigotmc:minecraft-server:${project.version}:txt:maps-mojang</srgIn>
                  <remappedArtifactAttached>true</remappedArtifactAttached>
                  <remappedClassifierName>remapped-mojang</remappedClassifierName>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <repositories>
    <repository>
      <id>minecraft-libraries</id>
      <name>Minecraft Libraries</name>
      <url>https://libraries.minecraft.net/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.4-R0.1-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>jline</groupId>
      <artifactId>jline</artifactId>
      <version>2.12.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-iostreams</artifactId>
      <version>2.19.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-commons</artifactId>
      <version>9.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.github.oshi</groupId>
      <artifactId>oshi-core</artifactId>
      <version>6.4.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>authlib</artifactId>
      <version>5.0.51</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>brigadier</artifactId>
      <version>1.2.9</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>datafixerupper</artifactId>
      <version>6.0.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.mojang</groupId>
      <artifactId>logging</artifactId>
      <version>1.1.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.13.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-buffer</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-codec</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-common</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-handler</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-resolver</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-classes-epoll</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-epoll</artifactId>
      <version>4.1.97.Final</version>
      <classifier>linux-x86_64</classifier>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-epoll</artifactId>
      <version>4.1.97.Final</version>
      <classifier>linux-aarch_64</classifier>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-transport-native-unix-common</artifactId>
      <version>4.1.97.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>it.unimi.dsi</groupId>
      <artifactId>fastutil</artifactId>
      <version>8.5.12</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>5.13.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna-platform</artifactId>
      <version>5.13.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.jopt-simple</groupId>
      <artifactId>jopt-simple</artifactId>
      <version>5.0.4</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.13.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.19.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-slf4j2-impl</artifactId>
      <version>2.19.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.googlecode.json-simple</groupId>
      <artifactId>json-simple</artifactId>
      <version>1.1.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.xerial</groupId>
      <artifactId>sqlite-jdbc</artifactId>
      <version>3.42.0.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.2.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-resolver-provider</artifactId>
      <version>3.9.6</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-connector-basic</artifactId>
      <version>1.9.18</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.maven.resolver</groupId>
      <artifactId>maven-resolver-transport-http</artifactId>
      <version>1.9.18</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations-java5</artifactId>
      <version>24.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.10.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-params</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-engine</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest</artifactId>
      <version>2.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.11.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>objenesis</artifactId>
          <groupId>org.objenesis</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-tree</artifactId>
      <version>9.7</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <api.version>unknown</api.version>
    <maven.compiler.source>17</maven.compiler.source>
    <minecraft_version>1_20_R3</minecraft_version>
    <maven.compiler.target>17</maven.compiler.target>
    <bt.name>git</bt.name>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <skipTests>true</skipTests>
  </properties>
</project>
