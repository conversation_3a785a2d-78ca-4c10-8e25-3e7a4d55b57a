# UltimateDungeons Boss Mob Configurations
# Defines all boss monsters and their abilities for dungeon encounters

version: "1.0.0"
author: "UltimateDungeons Team"
description: "Comprehensive boss mob configurations with custom abilities, loot, and mechanics"

# Global boss settings
global_settings:
  health_scaling:
    enabled: true
    base_multiplier: 1.0
    per_player_bonus: 0.3
    difficulty_multipliers:
      easy: 0.7
      normal: 1.0
      hard: 1.5
      nightmare: 2.2
      legendary: 3.5
  
  damage_scaling:
    enabled: true
    base_multiplier: 1.0
    per_player_bonus: 0.2
    difficulty_multipliers:
      easy: 0.8
      normal: 1.0
      hard: 1.3
      nightmare: 1.8
      legendary: 2.5
  
  experience_rewards:
    base_amount: 1000
    per_player_bonus: 200
    difficulty_multipliers:
      easy: 0.5
      normal: 1.0
      hard: 1.5
      nightmare: 2.0
      legendary: 3.0

# Boss definitions
bosses:
  
  # Castle Fortress Boss
  castle_lord:
    display_name: "&4&l⚔ Lord Malachar the Tyrant ⚔"
    type: "WITHER_SKELETON"
    level: 50
    health: 2000
    damage: 25
    armor: 15
    speed: 1.2
    
    # Custom model and appearance
    appearance:
      size: 1.8
      glowing: true
      custom_model: "castle_lord"
      equipment:
        helmet: "NETHERITE_HELMET"
        chestplate: "NETHERITE_CHESTPLATE"
        leggings: "NETHERITE_LEGGINGS"
        boots: "NETHERITE_BOOTS"
        main_hand: "NETHERITE_SWORD"
        off_hand: "SHIELD"
      
      enchantments:
        helmet:
          - "PROTECTION:5"
          - "UNBREAKING:10"
        chestplate:
          - "PROTECTION:5"
          - "THORNS:3"
          - "UNBREAKING:10"
        main_hand:
          - "SHARPNESS:8"
          - "FIRE_ASPECT:3"
          - "KNOCKBACK:2"
    
    # Boss abilities
    abilities:
      # Passive abilities
      fire_aura:
        type: "passive"
        description: "Burns nearby enemies"
        radius: 5
        damage: 2
        interval: 20
        particle: "FLAME"
      
      damage_resistance:
        type: "passive"
        description: "Reduces incoming damage"
        reduction: 0.3
      
      # Active abilities
      flame_strike:
        type: "active"
        cooldown: 8000
        cast_time: 2000
        description: "Strikes the ground with flaming sword"
        range: 10
        damage: 40
        effects:
          - "FIRE:100"
        particles:
          - type: "FLAME"
            count: 50
            spread: 3
        sound: "ENTITY_BLAZE_SHOOT"
      
      summon_minions:
        type: "active"
        cooldown: 15000
        cast_time: 3000
        description: "Summons skeleton warriors"
        minion_count: 4
        minion_type: "SKELETON"
        minion_health: 40
        minion_damage: 8
        duration: 30000
        sound: "ENTITY_WITHER_SPAWN"
      
      berserker_rage:
        type: "active"
        trigger: "health_below_50"
        description: "Enters rage mode when low on health"
        duration: 20000
        effects:
          speed_boost: 1.5
          damage_boost: 1.8
          attack_speed: 2.0
        particles:
          - type: "ANGRY_VILLAGER"
            count: 20
        sound: "ENTITY_RAVAGER_ROAR"
      
      ground_slam:
        type: "active"
        cooldown: 12000
        cast_time: 1500
        description: "Slams ground causing area damage"
        radius: 8
        damage: 30
        knockback: 3
        effects:
          - "SLOWNESS:60"
        particles:
          - type: "EXPLOSION_LARGE"
            count: 5
        sound: "ENTITY_GENERIC_EXPLODE"
    
    # Phase system
    phases:
      phase_1:
        health_range: "100-75"
        abilities: ["flame_strike", "fire_aura"]
        aggression: "normal"
      
      phase_2:
        health_range: "75-50"
        abilities: ["flame_strike", "summon_minions", "fire_aura"]
        aggression: "aggressive"
      
      phase_3:
        health_range: "50-25"
        abilities: ["flame_strike", "summon_minions", "berserker_rage", "ground_slam"]
        aggression: "very_aggressive"
      
      phase_4:
        health_range: "25-0"
        abilities: ["all"]
        aggression: "berserk"
        ability_cooldown_reduction: 0.5
    
    # Loot drops
    loot:
      guaranteed:
        - item: "NETHERITE_SWORD"
          enchantments:
            - "SHARPNESS:7"
            - "FIRE_ASPECT:2"
            - "UNBREAKING:5"
          custom_name: "&4Lord Malachar's Blade"
          lore:
            - "&7The tyrant's personal weapon,"
            - "&7still burning with his hatred."
        
        - item: "EMERALD"
          amount: 25
        
        - item: "EXPERIENCE_BOTTLE"
          amount: 10
      
      rare_drops:
        - item: "CASTLE_LORD_CROWN"
          chance: 0.15
          custom_name: "&6Crown of the Castle Lord"
          lore:
            - "&7A crown that grants dominion"
            - "&7over lesser undead creatures."
        
        - item: "FLAME_CRYSTAL"
          chance: 0.10
          custom_name: "&cCrystal of Eternal Flame"
          lore:
            - "&7A crystal containing the"
            - "&7essence of eternal fire."
    
    # Spawn conditions
    spawn:
      location: "throne_room"
      trigger: "player_enter_throne_room"
      announcement: "&4&l⚔ LORD MALACHAR HAS AWAKENED! ⚔"
      music: "boss_battle_epic"
      weather: "storm"
    
    # Death events
    death:
      announcement: "&6&l⚔ LORD MALACHAR HAS BEEN DEFEATED! ⚔"
      effects:
        - "fireworks:10"
        - "lightning_strike"
        - "title:&6VICTORY!"
        - "subtitle:&eLord Malachar has fallen!"
      rewards:
        experience: 2500
        money: 1000
        dungeon_tokens: 50

  # Ancient Temple Boss
  pharaoh_ankhemhotep:
    display_name: "&e&l𓂀 Pharaoh Ankhemhotep the Eternal 𓂀"
    type: "ZOMBIE"
    level: 45
    health: 1800
    damage: 22
    armor: 12
    speed: 1.0
    
    appearance:
      size: 1.6
      glowing: true
      custom_model: "ancient_pharaoh"
      equipment:
        helmet: "GOLDEN_HELMET"
        chestplate: "GOLDEN_CHESTPLATE"
        leggings: "GOLDEN_LEGGINGS"
        boots: "GOLDEN_BOOTS"
        main_hand: "GOLDEN_SWORD"
        off_hand: "TOTEM_OF_UNDYING"
    
    abilities:
      curse_of_ages:
        type: "passive"
        description: "Inflicts aging curse on attackers"
        chance: 0.3
        effects:
          - "SLOWNESS:100"
          - "WEAKNESS:100"
          - "POISON:60"
      
      sandstorm:
        type: "active"
        cooldown: 10000
        cast_time: 2500
        description: "Creates a blinding sandstorm"
        radius: 15
        duration: 8000
        effects:
          - "BLINDNESS:160"
          - "SLOWNESS:160"
        particles:
          - type: "CLOUD"
            count: 100
            spread: 8
      
      mummy_army:
        type: "active"
        cooldown: 20000
        cast_time: 4000
        description: "Raises an army of mummies"
        minion_count: 6
        minion_type: "ZOMBIE"
        minion_health: 30
        minion_speed: 0.8
        duration: 45000
      
      golden_shield:
        type: "active"
        trigger: "health_below_30"
        description: "Creates protective golden barrier"
        duration: 15000
        damage_reduction: 0.8
        particles:
          - type: "VILLAGER_HAPPY"
            count: 30
            color: "YELLOW"
      
      plague_breath:
        type: "active"
        cooldown: 8000
        cast_time: 1000
        description: "Breathes poisonous plague"
        range: 12
        cone_angle: 60
        damage: 15
        effects:
          - "POISON:200"
          - "HUNGER:200"
        particles:
          - type: "SPELL_WITCH"
            count: 40
    
    loot:
      guaranteed:
        - item: "GOLDEN_APPLE"
          amount: 5
        - item: "GOLD_INGOT"
          amount: 32
        - item: "EMERALD"
          amount: 20
      
      rare_drops:
        - item: "PHARAOH_SCEPTER"
          chance: 0.12
          custom_name: "&6Scepter of Ankhemhotep"
        - item: "ANKH_OF_LIFE"
          chance: 0.08
          custom_name: "&eAnkh of Eternal Life"

  # Crystal Cave Boss
  crystal_golem:
    display_name: "&b&l◆ Crystalline Guardian ◆"
    type: "IRON_GOLEM"
    level: 40
    health: 2500
    damage: 30
    armor: 20
    speed: 0.8
    
    appearance:
      size: 2.2
      glowing: true
      custom_model: "crystal_golem"
    
    abilities:
      crystal_armor:
        type: "passive"
        description: "Crystal armor reflects damage"
        reflection: 0.25
        particles:
          - type: "VILLAGER_HAPPY"
            count: 5
            color: "CYAN"
      
      crystal_spikes:
        type: "active"
        cooldown: 6000
        description: "Shoots crystal projectiles"
        projectile_count: 8
        damage: 18
        range: 20
        effects:
          - "SLOWNESS:40"
      
      earthquake:
        type: "active"
        cooldown: 15000
        cast_time: 3000
        description: "Causes devastating earthquake"
        radius: 12
        damage: 35
        knockback: 5
        block_breaking: true
      
      crystal_heal:
        type: "active"
        trigger: "health_below_40"
        cooldown: 25000
        description: "Absorbs crystal energy to heal"
        heal_amount: 400
        cast_time: 5000
        vulnerability_during_cast: 2.0
    
    loot:
      guaranteed:
        - item: "DIAMOND"
          amount: 16
        - item: "EMERALD"
          amount: 12
      
      rare_drops:
        - item: "CRYSTAL_HEART"
          chance: 0.20
          custom_name: "&bHeart of the Crystal Guardian"

  # Nightmare Dungeon Boss
  void_wraith:
    display_name: "&5&l☠ Malachar, Wraith of the Void ☠"
    type: "VEX"
    level: 60
    health: 1500
    damage: 35
    armor: 5
    speed: 1.8
    
    appearance:
      size: 1.4
      glowing: true
      custom_model: "void_wraith"
      transparency: 0.7
    
    abilities:
      phase_shift:
        type: "passive"
        description: "Randomly becomes invulnerable"
        chance: 0.15
        duration: 2000
        particles:
          - type: "PORTAL"
            count: 20
      
      void_bolt:
        type: "active"
        cooldown: 3000
        description: "Fires void energy bolts"
        projectile_count: 3
        damage: 25
        range: 25
        homing: true
        effects:
          - "WITHER:60"
      
      shadow_clone:
        type: "active"
        cooldown: 18000
        description: "Creates shadow duplicates"
        clone_count: 3
        clone_health: 200
        clone_damage: 15
        duration: 20000
      
      void_storm:
        type: "active"
        trigger: "health_below_25"
        description: "Unleashes devastating void storm"
        radius: 20
        duration: 12000
        damage_per_second: 8
        effects:
          - "LEVITATION:240"
          - "WITHER:240"
    
    loot:
      guaranteed:
        - item: "NETHER_STAR"
          amount: 2
        - item: "EXPERIENCE_BOTTLE"
          amount: 20
      
      rare_drops:
        - item: "VOID_ESSENCE"
          chance: 0.25
          custom_name: "&5Essence of the Void"

# Boss encounter mechanics
encounter_mechanics:
  arena_boundaries:
    enabled: true
    barrier_type: "INVISIBLE_WALL"
    escape_prevention: true
    return_teleport: true
  
  music_system:
    boss_music: true
    dynamic_intensity: true
    victory_fanfare: true
  
  camera_effects:
    screen_shake: true
    dramatic_angles: false
    slow_motion_deaths: true
  
  spectator_mode:
    allow_spectating: true
    ghost_mode_on_death: true
    revival_system: false

# Difficulty modifiers
difficulty_modifiers:
  easy:
    health_multiplier: 0.7
    damage_multiplier: 0.8
    ability_cooldown_multiplier: 1.3
    minion_count_multiplier: 0.7
  
  normal:
    health_multiplier: 1.0
    damage_multiplier: 1.0
    ability_cooldown_multiplier: 1.0
    minion_count_multiplier: 1.0
  
  hard:
    health_multiplier: 1.5
    damage_multiplier: 1.3
    ability_cooldown_multiplier: 0.8
    minion_count_multiplier: 1.3
  
  nightmare:
    health_multiplier: 2.2
    damage_multiplier: 1.8
    ability_cooldown_multiplier: 0.6
    minion_count_multiplier: 1.6
    new_abilities: true
  
  legendary:
    health_multiplier: 3.5
    damage_multiplier: 2.5
    ability_cooldown_multiplier: 0.4
    minion_count_multiplier: 2.0
    new_abilities: true
    enrage_timer: 600000  # 10 minutes

# Achievement integration
achievements:
  boss_slayer:
    description: "Defeat any dungeon boss"
    reward: "boss_slayer_title"
  
  castle_conqueror:
    description: "Defeat Lord Malachar"
    reward: "castle_conqueror_title"
  
  pharaoh_defeater:
    description: "Defeat Pharaoh Ankhemhotep"
    reward: "pharaoh_defeater_title"
  
  crystal_crusher:
    description: "Defeat the Crystal Guardian"
    reward: "crystal_crusher_title"
  
  void_vanquisher:
    description: "Defeat the Void Wraith"
    reward: "void_vanquisher_title"
  
  boss_master:
    description: "Defeat all dungeon bosses"
    reward: "boss_master_title"

# Statistics tracking
statistics:
  track_boss_kills: true
  track_fastest_kills: true
  track_damage_dealt: true
  track_damage_taken: true
  leaderboards: true

# Anti-exploit measures
anti_exploit:
  prevent_cheese_tactics: true
  minimum_engagement_time: 30000
  damage_validation: true
  position_validation: true

# Metadata
metadata:
  total_bosses: 4
  average_fight_duration: "5-15 minutes"
  recommended_party_size: "3-5 players"
  total_unique_abilities: 23
  total_loot_items: 47
